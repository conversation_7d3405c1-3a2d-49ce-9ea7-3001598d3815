﻿using Everylang.App.Callback;
using Everylang.App.Data.DataModel;
using Everylang.App.Main;
using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;
using Everylang.App.View.Controls.Snippets;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using Telerik.Windows.Controls;
using Telerik.Windows.Data;
using SnippetsManager = Everylang.App.Data.DataStore.SnippetsManager;

namespace Everylang.App.ViewModels.SettingsModel
{
    public class SnippetsViewModel : ViewModelBase
    {
        internal RadObservableCollection<SnippetsDataModel?> SnippetsList { get; set; }
        internal List<SnippetsDataModel> SnippetsForSwitcher { get; set; }

        public ObservableCollection<string> ChangeMethodsList { get; set; }

        public SnippetsViewModel()
        {
            SnippetsList = new RadObservableCollection<SnippetsDataModel?>();
            ChangeMethodsList = new ObservableCollection<string>();
            SnippetsForSwitcher = new List<SnippetsDataModel?>();
            GetAllDataFromDb();
            FillChangeMethodsList();
            GlobalEventsApp.EventAddNewSnippets += AddNewCallbackEventHandler;
        }

        private void AddNewCallbackEventHandler(string? s)
        {
            AppHookManager.Instance.PressedMain(null, null);
            SnippetsHelperWindow snippetsHelperWindow = new SnippetsHelperWindow(null, s, "");
            Application curApp = Application.Current;
            if (curApp.MainWindow != null)
            {
                Window mainWindow = curApp.MainWindow;
                snippetsHelperWindow.WindowStartupLocation = WindowStartupLocation.CenterOwner;
                snippetsHelperWindow.Owner = mainWindow;
            }

            snippetsHelperWindow.ShowDialog();
            snippetsHelperWindow.TextBox.Focus();
            GlobalEventsApp.OnEventAddNewSnippetsUpdate();
        }

        private void FillChangeMethodsList()
        {
            int index = SettingsManager.Settings.SnippetsMethodPastByKey;
            ChangeMethodsList.Clear();
            ChangeMethodsList.Add(LocalizationManager.GetString("AutochangeOnTab"));
            ChangeMethodsList.Add(LocalizationManager.GetString("AutochangeOnInter"));
            ChangeMethodsList.Add(LocalizationManager.GetString("AutochangeOnTabOrInter"));
            ChangeMethodsList.Add(LocalizationManager.GetString("AutochangeOnSpace"));
            ChangeMethodsList.Add(LocalizationManager.GetString("AutochangeOnSpaceOrInter"));
            SettingsManager.Settings.SnippetsMethodPastByKey = index;
            base.OnPropertyChanged(nameof(ChangeMethodsCurrentIndex));
            // 0 Клавиша Tab
            // 1 Клавиша Enter
            // 2 Tab или Enter
            // 3 Пробел
            // 4 Пробел или Enter
        }

        internal void GetAllDataFromDb()
        {
            SnippetsForSwitcher.Clear();
            SnippetsList.Clear();
            SnippetsList.AddRange(SnippetsManager.GetAllSnippetsData());
            SnippetsForSwitcher.AddRange(SnippetsList.Where(x => !string.IsNullOrEmpty(x?.FromText)));
            base.OnPropertyChanged(nameof(SnippetsList));
        }

        internal void SaveAllToDb()
        {
            SnippetsManager.SaveAllSnippetsData(SnippetsList.ToList());
        }

        private bool _isPro;

        public bool jgebhdhs
        {
            get => _isPro;
            set
            {
                _isPro = value;
                base.OnPropertyChanged();
                base.OnPropertyChanged(nameof(IsOnSnippets));
            }
        }


        public bool IsOnSnippets
        {
            get => jgebhdhs && SettingsManager.Settings.SnippetsIsOn;
            set
            {
                SettingsManager.Settings.SnippetsIsOn = value;
                if (value)
                {
                    Snippets.SnippetsManager.Instance.Start();
                }
                else
                {
                    Snippets.SnippetsManager.Instance.Stop();
                }

                base.OnPropertyChanged();
            }
        }

        public bool IsChangeCaseLetters
        {
            get
            {
                return SettingsManager.Settings.SnippetsIsCaseSensitive;
            }
            set
            {
                SettingsManager.Settings.SnippetsIsCaseSensitive = value;
                base.OnPropertyChanged();
            }
        }

        public bool IsChangeOtherLayout
        {
            get
            {
                return SettingsManager.Settings.SnippetsWithOtherLayout;
            }
            set
            {
                SettingsManager.Settings.SnippetsWithOtherLayout = value;
                base.OnPropertyChanged();
            }
        }

        public bool IsShowMiniWindow
        {
            get
            {
                return SettingsManager.Settings.SnippetsIsShowTipWindow;
            }
            set
            {
                SettingsManager.Settings.SnippetsIsShowTipWindow = value;
                base.OnPropertyChanged();
            }
        }

        public bool IsEnabledCountUsage
        {
            get
            {
                return SettingsManager.Settings.SnippetsIsEnabledCountUsage;
            }
            set
            {
                SettingsManager.Settings.SnippetsIsEnabledCountUsage = value;
                base.OnPropertyChanged();
            }
        }

        public string Shortcut
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.SnippetsShowAllShortcut);
            }
            set
            {
                SettingsManager.Settings.SnippetsShowAllShortcut = value;
                base.OnPropertyChanged();
            }
        }

        public string ShortcutAddNew
        {
            get
            {
                return ShortcutManager.GetCharFromKey(SettingsManager.Settings.SnippetsAddNewShortcut);
            }
            set
            {
                SettingsManager.Settings.SnippetsAddNewShortcut = value;
                base.OnPropertyChanged();
            }
        }
        public int ChangeMethodsCurrentIndex
        {
            get
            {
                if (ChangeMethodsList.Count > SettingsManager.Settings.SnippetsMethodPastByKey)
                    return SettingsManager.Settings.SnippetsMethodPastByKey;
                return -1;
            }
            set
            {
                SettingsManager.Settings.SnippetsMethodPastByKey = value;
                base.OnPropertyChanged();
            }
        }
    }
}
