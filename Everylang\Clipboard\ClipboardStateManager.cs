﻿using Everylang.App.HookManager;
using Everylang.App.SwitcherLang;
using Everylang.Common.Utilities;
using System;
using System.Threading;
using System.Windows;
using System.Windows.Threading;

namespace Everylang.App.Clipboard
{
    internal class ClipboardStateManager : IDisposable
    {
        readonly bool _withHistory;
        private bool _keyHooksWereEnabled;
        private bool _disposed;
        private bool _initialized;
        private readonly bool _restoreForeground;

        internal ClipboardStateManager(bool withHistory = false, bool restoreForeground = false)
        {
            _withHistory = withHistory;
            _restoreForeground = restoreForeground;
        }

        internal void Initialize()
        {
            _initialized = true;
            _keyHooksWereEnabled = CommonHookListener.IsKeyEnabled;
            CommonHookListener.IsKeyEnabled = false;

            KeyboardState.ReleaseAllKeys();
            Thread.Sleep(50);
            if (_restoreForeground)
            {
                ForegroundWindow.RestoreForegroundWindow();
                Thread.Sleep(50);
            }
            if (!_withHistory)
            {
                ClipboardMonitorWorker.Pause = true;
            }
            ClipboardOperations.ClipboardSave();
            Thread.Sleep(100);
            ClipboardOperations.CloseClipboard();

        }

        public void Dispose()
        {
            if (_disposed || !_initialized) return;

            try
            {
                ClipboardOperations.ClipboardRestore();
                Application.Current.Dispatcher.Invoke(DispatcherPriority.Background,
                    new Action(delegate { }));
                ClipboardMonitorWorker.Pause = false;
                CommonHookListener.IsKeyEnabled = _keyHooksWereEnabled;

            }
            catch
            {
                // Ensure cleanup always happens
            }
            finally
            {
                _disposed = true;
            }

        }
    }

}
