﻿using Everylang.App.Data.DataModel;
using Everylang.Common.LogManager;
using LiteDB;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace Everylang.App.Data.DataStore
{

    internal class ClipboadManager
    {


        internal static IEnumerable<ClipboardDataModel> GetAllClipboardData()
        {
            var collection = new List<ClipboardDataModel>();
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("ClipboardDataModel");
                    var sd = schemelessCollection.FindAll().ToList();
                    foreach (var bsonDocument in sd)
                    {
                        ClipboardDataModel clipboardDataModel = new ClipboardDataModel();
                        if (!bsonDocument["_id"].IsNull) clipboardDataModel.Id = bsonDocument["_id"].AsObjectId;
                        if (!bsonDocument["DateTime"].IsNull) clipboardDataModel.DateTime = bsonDocument["DateTime"].AsDateTime;
                        if (!bsonDocument["Application"].IsNull) clipboardDataModel.Application = bsonDocument["Application"].AsString;
                        if (!bsonDocument["ShortText"].IsNull) clipboardDataModel.ShortText = bsonDocument["ShortText"].AsString;
                        if (!bsonDocument["Text"].IsNull) clipboardDataModel.Text = bsonDocument["Text"].AsString;
                        if (!bsonDocument["Files"].IsNull) clipboardDataModel.Files = bsonDocument["Files"].AsString;
                        if (!bsonDocument["ImageId"].IsNull) clipboardDataModel.ImageId = bsonDocument["ImageId"].AsString;
                        collection.Add(clipboardDataModel);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }

            collection.Reverse();
            return collection;
        }

        internal static void AddClipboardData(ClipboardDataModel clipboardData)
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("ClipboardDataModel");
                    BsonDocument bsonDocument = new BsonDocument();
                    clipboardData.Id = ObjectId.NewObjectId();
                    bsonDocument["_id"] = clipboardData.Id;
                    bsonDocument["DateTime"] = clipboardData.DateTime;
                    bsonDocument["Application"] = clipboardData.Application;
                    bsonDocument["ShortText"] = clipboardData.ShortText;
                    bsonDocument["Text"] = clipboardData.Text;
                    bsonDocument["Html"] = clipboardData.Html;
                    bsonDocument["Rtf"] = clipboardData.Rtf;
                    bsonDocument["Files"] = clipboardData.Files;
                    bsonDocument["ImageId"] = clipboardData.ImageId;
                    schemelessCollection.Insert(bsonDocument);
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void AddClipboardImage(string filePath, string? imageId)
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    db.FileStorage.Upload(imageId, filePath);
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void ClearAllClipboardData()
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    if (db.CollectionExists("ClipboardDataModel"))
                    {
                        db.DropCollection("ClipboardDataModel");
                    }

                    var allFiles = db.FileStorage.FindAll().ToList();
                    foreach (var liteFileInfo in allFiles)
                    {
                        db.FileStorage.Delete(liteFileInfo.Id);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static void DelClipboardData(List<ClipboardDataModel> clipboardDataList)
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("ClipboardDataModel");
                    foreach (var data in clipboardDataList)
                    {
                        if (data.IsImage)
                        {
                            db.FileStorage.Delete(data.ImageId);
                        }
                        var item = data;
                        if (item != null)
                        {
                            if (item.Id != null) schemelessCollection.Delete(item.Id);
                            else schemelessCollection.DeleteMany(Query.EQ("ShortText", item.ShortText));
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static Stream? GetImage(string? imageId)
        {
            Stream stream = new MemoryStream();
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var asd = db.FileStorage.FindById(imageId);
                    if (asd != null)
                    {
                        asd.CopyTo(stream);
                    }
                }

                return stream;
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }

            return null;
        }

        internal static void SaveAll(IEnumerable<ClipboardDataModel> list)
        {
            try
            {
                var db = DataBaseManager.LiteDb;
                {
                    var schemelessCollection = db.GetCollection("ClipboardDataModel");
                    foreach (var dataModel in list)
                    {
                        if (dataModel.IsImage)
                        {
                            continue;
                        }
                        BsonDocument bsonDocument = new BsonDocument();
                        if (dataModel.Id == null)
                        {
                            dataModel.Id = ObjectId.NewObjectId();
                        }
                        bsonDocument["_id"] = dataModel.Id;
                        bsonDocument["DateTime"] = dataModel.DateTime;
                        bsonDocument["Application"] = dataModel.Application;
                        bsonDocument["ShortText"] = dataModel.ShortText;
                        bsonDocument["Text"] = dataModel.Text;
                        bsonDocument["Html"] = dataModel.Html;
                        bsonDocument["Rtf"] = dataModel.Rtf;
                        bsonDocument["Files"] = dataModel.Files;
                        bsonDocument["ImageId"] = dataModel.ImageId;
                        schemelessCollection.Insert(bsonDocument);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static string GetText(ObjectId id)
        {
            var db = DataBaseManager.LiteDb;
            {
                var schemelessCollection = db.GetCollection("ClipboardDataModel");
                var bsonDocument = schemelessCollection.FindById(id);
                if (bsonDocument != null)
                {
                    if (!bsonDocument["Text"].IsNull) return bsonDocument["Text"].AsString;
                }
            }
            return "";
        }

        internal static string GetRtf(ObjectId id)
        {
            var db = DataBaseManager.LiteDb;
            {
                var schemelessCollection = db.GetCollection("ClipboardDataModel");
                var bsonDocument = schemelessCollection.FindById(id);
                if (bsonDocument != null)
                {
                    if (!bsonDocument["Rtf"].IsNull) return bsonDocument["Rtf"].AsString;
                }
            }
            return "";
        }

        internal static string GetHtml(ObjectId id)
        {
            var db = DataBaseManager.LiteDb;
            {
                var schemelessCollection = db.GetCollection("ClipboardDataModel");
                var bsonDocument = schemelessCollection.FindById(id);
                if (bsonDocument != null)
                {
                    if (!bsonDocument["Html"].IsNull) return bsonDocument["Html"].AsString;
                }
            }
            return "";
        }
    }
}
