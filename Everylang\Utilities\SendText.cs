﻿using Everylang.App.Clipboard;
using Everylang.App.HookManager;
using Everylang.Common.Utilities;
using System;
using System.Linq;
using System.Windows;
using System.Windows.Threading;
using WindowsInput;

namespace Everylang.App.Utilities
{
    class SendText
    {
        internal static void SendStringByPaste(string? text, bool isRestoreForeground, bool withHistory = false)
        {
            if (string.IsNullOrEmpty(text))
            {
                return;
            }
            if (text!.Length < 100 && !text.Any(Char.IsPunctuation) && !text.Any(Char.IsSeparator))
            {
                SendStringByTextEntry(text, isRestoreForeground);
                if (withHistory)
                {
                    ClipboardOperations.SetText(text);
                }
                return;
            }


            if (isRestoreForeground)
            {
                ForegroundWindow.RestoreForegroundWindow();
            }

            if (withHistory)
            {
                ClipboardOperations.SetText(text);
                Application.Current.Dispatcher.Invoke(DispatcherPriority.Background,
                    new Action(delegate { }));
                ClipboardOperations.SendPasteText();
                return;
            }

            using var clipboardState = new ClipboardStateManager();
            clipboardState.Initialize();

            ClipboardOperations.SetText(text);
            Application.Current.Dispatcher.Invoke(DispatcherPriority.Background,
                new Action(delegate { }));

            ClipboardOperations.SendPasteText();

        }

        internal static void SendStringByTextEntry(string? text, bool isRestoreForeground)
        {
            if (text == "")
            {
                return;
            }
            CommonHookListener.IsKeyEnabled = false;
            if (text != null && text.Length == 1)
            {
                new InputSimulator().Keyboard.TextEntry(text);
                CommonHookListener.IsKeyEnabled = true;
                return;
            }
            try
            {
                if (isRestoreForeground)
                {
                    ForegroundWindow.RestoreForegroundWindow();
                }
                var normalizeText = NormalizeLineBreaks(text);
                string[] textArr = normalizeText.Split(new[] { Environment.NewLine }, StringSplitOptions.None);
                var sim = new InputSimulator();
                for (int i = 0; i < textArr.Length; i++)
                {
                    var s1 = textArr[i];
                    if (!string.IsNullOrEmpty(s1))
                    {
                        sim.Keyboard.TextEntry(s1);
                        if (textArr.Length - 1 > i)
                        {
                            sim.Keyboard.KeyPress(VirtualKeyCode.RETURN);
                        }
                    }
                    else
                    {
                        if (textArr.Length - 1 > i)
                        {
                            sim.Keyboard.KeyPress(VirtualKeyCode.RETURN);
                        }
                    }
                }
            }
            catch
            {
                // ignore
            }
            CommonHookListener.IsKeyEnabled = true;
        }


        /// <summary>
        /// Нормализует переводы строк в тексте, приводя все варианты (\n, \r, \r\n) к стандартному Windows формату \r\n
        /// </summary>
        /// <param name="input">Входная строка для нормализации</param>
        /// <returns>Строка с нормализованными переводами строк</returns>
        static string NormalizeLineBreaks(string? input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            // Проверяем, есть ли в строке символы перевода строки
            // Если нет - возвращаем исходную строку без обработки (оптимизация)
            if (input!.IndexOfAny(new[] { '\r', '\n' }) == -1)
                return input;

            // Нормализуем все варианты переводов строк к стандартному Windows формату \r\n
            // 1. Сначала заменяем существующие \r\n на \n (чтобы избежать дублирования)
            // 2. Затем заменяем одиночные \r на \n
            // 3. Наконец заменяем все \n на \r\n
            return input.Replace("\r\n", "\n")
                       .Replace("\r", "\n")
                       .Replace("\n", "\r\n");
        }

    }
}
