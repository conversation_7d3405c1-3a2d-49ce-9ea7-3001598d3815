﻿using Everylang.App.Callback;
using Everylang.App.Clipboard;
using Everylang.App.Data.DataModel;
using Everylang.App.Data.DataStore;
using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.LangFlag;
using Everylang.App.SettingsApp;
using Everylang.App.Shortcut;
using Everylang.App.SpellCheck;
using Everylang.App.Utilities;
using Everylang.App.View.Controls.AutoSwitch;
using Everylang.App.View.Controls.Snippets;
using Everylang.App.View.Controls.SpellCheck;
using Everylang.App.ViewModels;
using Everylang.Common.LogManager;
using Everylang.Common.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;
using Vanara.PInvoke;
using WindowsInput;
using MousePosition = Everylang.App.Utilities.MousePosition;


namespace Everylang.App.SwitcherLang
{
    internal static class KeyboardLayoutWorker
    {
        internal static bool IsLayoutChanging { get; set; }
        internal static bool IsEnabled { get; set; }

        // текущий хендл для переключения
        private static IntPtr _langCode;
        private static string? _lastAutoLangCode;
        private static string? _langCodeName;
        private static IntPtr _lastWindowId;

        private static int _lastKeyDown;
        private static int _lastKeyUp;

        private static string? _lastAutoText = "";
        private static string? _lastAutoTextForRules = "";
        private static string? _lastSwitchText = "";
        private static string? _lastInputText = "";
        private static string? _lastInputTextDiary = "";

        private static List<int> _lastInputKeys = null!;
        private static string? _inputChar = "";
        private static IntPtr _currentLayoutHdl;


        private static bool _isAutoSwitchingNow;

        private static bool _isDop;
        private static bool _isAutoSwitch;
        private static bool _isAutoSwitchDic;
        private static bool _isRuleAndPunc;
        private static bool _isPrintable;
        private static bool _lastAutochangeGo;
        private static bool _keyboardLayoutChangedManually;
        private static bool _isSpellCheckReplacedLast;
        private static int _countLeftRemove;



        private static AutoSwitchAcceptWindow _autoSwitchAcceptWindow = null!;
        private static SpellCheckWhileTypingWindow? _spellCheckWhileTypingWindow;

        private static IntPtr _langCodeForCancelAutoSwitch;

        private static bool _isNoKeyPress;
        private static bool _isLastAutoswitch;
        private static bool _isBeginNewWord;
        private static bool _isSwitchLang;
        private static bool _langWasAutoSwitched;

        private static System.Timers.Timer _timerAutoSwitch = null!;

        internal static void Start()
        {
            _lastInputKeys = new List<int>();
            IsEnabled = true;
            _isSpellCheckReplacedLast = true;
            SwitcherSelectedText.Start();
            SpellCheckWhileTyping.Init();

            GlobalEventsApp.EventKeyboardLayoutChangedForLangFlag += CallbackEventHandlerKeyboardLayoutChangedManually;
            GlobalEventsApp.SpellCheckReplaceAction += SpellCheckReplaceText;
            GlobalEventsApp.SwitchAcceptAction += SwitchAccept;

            HookCallBackMouseDown.CallbackEventHandler += MouseEventSwitcherLang;
            HookCallBackMouseWheel.CallbackEventHandler += MouseEventSwitcherLang;

            KeyboardLayoutManager.Instance.KeyDownSwitcher += KeyDownSwitcherLang;
            KeyboardLayoutManager.Instance.KeyUpSwitcher += KeyUpSwitcherLang;
            KeyboardLayoutManager.Instance.KeyCombinationPressedSwitcher += KeyCombinationPressed;
            KeyboardLayoutManager.Instance.KeyCombinationPressedSwitcherLine += KeyCombinationPressedSwitcherLine;

            DoubleKeyDownManager.DoubleKeyShift += CanselLastAutoswitch;
            _autoSwitchAcceptWindow = new AutoSwitchAcceptWindow();
            _timerAutoSwitch = new System.Timers.Timer(TimeSpan.FromMilliseconds(800).TotalMilliseconds);
            _timerAutoSwitch.Elapsed += (_, _) => TimerAutoSwitchCheck();

            GlobalLangChangeHook.ForegroundWindowChanged += ForegroundWindowChanged;
        }

        internal static void Stop()
        {
            IsEnabled = false;

            SwitcherSelectedText.Stop();

            GlobalEventsApp.EventKeyboardLayoutChangedForLangFlag -= CallbackEventHandlerKeyboardLayoutChangedManually;
            GlobalEventsApp.SpellCheckReplaceAction -= SpellCheckReplaceText;
            GlobalEventsApp.SwitchAcceptAction -= SwitchAccept;
            HookCallBackMouseDown.CallbackEventHandler -= MouseEventSwitcherLang;
            HookCallBackMouseWheel.CallbackEventHandler -= MouseEventSwitcherLang;
            KeyboardLayoutManager.Instance.KeyDownSwitcher -= KeyDownSwitcherLang;
            KeyboardLayoutManager.Instance.KeyUpSwitcher -= KeyUpSwitcherLang;
            KeyboardLayoutManager.Instance.KeyCombinationPressedSwitcher -= KeyCombinationPressed;
            KeyboardLayoutManager.Instance.KeyCombinationPressedSwitcherLine -= KeyCombinationPressedSwitcherLine;
            DoubleKeyDownManager.DoubleKeyShift -= CanselLastAutoswitch;
            _autoSwitchAcceptWindow = null;
            _timerAutoSwitch.Close();
            GlobalLangChangeHook.ForegroundWindowChanged -= ForegroundWindowChanged;
        }

        private static void CallbackEventHandlerKeyboardLayoutChangedManually(IntPtr obj)
        {
            _keyboardLayoutChangedManually = false;
            if (!_langWasAutoSwitched && SettingsManager.Settings.AutoSwitcherDisableAutoSwitchAfterManualSwitch)
            {
                _keyboardLayoutChangedManually = true;
            }
            _langWasAutoSwitched = false;
        }

        private static void ForegroundWindowChanged()
        {
            _keyboardLayoutChangedManually = false;
        }

        private static void KeyCombinationPressedSwitcherLine(object? sender, EventArgs e)
        {
            SwitchText(true);
        }

        private static void KeyCombinationPressed(object? sender, EventArgs e)
        {
            SwitchText(false);
        }

        private static void CanselLastAutoswitch()
        {
            if (_langWasAutoSwitched)
            {
                SwitchText(false);
            }
        }

        private static void SwitchText(bool isAllLine, bool afterTimer = false)
        {
            try
            {
                _langWasAutoSwitched = false;
                if (!IsEnabled)
                {
                    return;
                }
                if (_isAutoSwitch && _keyboardLayoutChangedManually)
                {
                    _isDop = false;
                    _isAutoSwitch = false;
                    return;
                }
                if (_isAutoSwitch && GlobalLangChangeHook.IsPassword)
                {
                    _isDop = false;
                    _isAutoSwitch = false;
                    return;
                }
                if (_isAutoSwitch && !CheckActiveProcessFileName.CheckAutoSwitch())
                {
                    _isDop = false;
                    _isAutoSwitch = false;
                    return;
                }

                if (_isAutoSwitch && !SettingsManager.Settings.AutoSwitcherIsOn)
                {
                    _isDop = false;
                    _isAutoSwitch = false;
                    return;
                }

                if (!_isAutoSwitch && !CheckActiveProcessFileName.CheckLayoutSwitcher())
                {
                    _isDop = false;
                    _isAutoSwitch = false;
                    return;
                }
                LangInfoManager.Stop();
                if (_spellCheckWhileTypingWindow != null) _spellCheckWhileTypingWindow.Hide();
                IsEnabled = false;
                // переключение для всей строки
                var sim = new InputSimulator();
                if (isAllLine)
                {
                    if (_lastInputText != null)
                    {
                        for (int i = 0; i < _lastInputText.Length; i++)
                        {
                            sim.Keyboard.KeyPress(VirtualKeyCode.BACK);
                        }
                    }

                    _lastInputText = SwitcherSelectedText.SwitchAndReplaceSelected(_lastInputText);
                    _isAutoSwitch = false;
                    _isDop = false;
                    IsEnabled = true;
                    LangInfoManager.Start();
                    return;
                }

                // переключение выделенного текста по двойному шифту
                _currentLayoutHdl = KeyboardLayoutMethods.GetCurrentKeyboardLayoutHdl();
                var currentKeyboardLayoutName = KeyboardLayoutMethods.GetKeyboardLayoutNameByHandle(_currentLayoutHdl);
                var text = KeyboardLayoutCommon.AutoSwitcherTextByLayouts[currentKeyboardLayoutName];
                if (text?.Trim() == "")
                {
                    if (_isAutoSwitch == false)
                    {
                        string? selectionText = ClipboardOperations.GetSelectionText();

                        if (!string.IsNullOrEmpty(selectionText))
                        {
                            ForegroundWindow.StoreForegroundWindow();
                            SwitcherSelectedText.SwitchAndReplaceSelected(selectionText);
                        }
                    }
                    _isAutoSwitch = false;
                    _isDop = false;
                    IsEnabled = true;
                    LangInfoManager.Start();
                    return;
                }


                // если при автопереключении языка на который должен переключиться нет в списке допустимых то выходим
                if (_isAutoSwitch && _langCodeName != null)
                {
                    var name = currentKeyboardLayoutName.ToUpper() + "\u2192" + _langCodeName.ToUpper();
                    if (SettingsManager.Settings.AutoSwitcherNotTrueListOfLang.Contains(name))
                    {
                        _isAutoSwitch = false;
                        _isDop = false;
                        IsEnabled = true;
                        LangInfoManager.Start();
                        return;
                    }

                }

                text = GetTextAllLine(text);
                if (afterTimer && text.Length == 1)
                {
                    _isAutoSwitch = false;
                    _isDop = false;
                    IsEnabled = true;
                    LangInfoManager.Start();
                    return;
                }
                // если до этого уже этот текст вручную переключили
                if (_lastSwitchText == text)
                {
                    _isAutoSwitch = false;
                    _isDop = false;
                    IsEnabled = true;
                    LangInfoManager.Start();
                    return;
                }
                // проверка, если нельзя переключать все большие буквы
                if (_isAutoSwitch && SettingsManager.Settings.AutoSwitcherNotSwitchTextLangWithAllUpperCaseLetters && text.Trim().Length > 1)
                {
                    string textForUpper = text.Trim();
                    bool isAllUpper = !string.IsNullOrEmpty(textForUpper) && textForUpper.All(c => char.IsUpper(c));
                    if (isAllUpper)
                    {
                        _isAutoSwitch = false;
                        _isDop = false;
                        IsEnabled = true;
                        LangInfoManager.Start();
                        return;
                    }
                }
                if (_lastInputText == "")
                {
                    _isAutoSwitch = false;
                    _isDop = false;
                    IsEnabled = true;
                    LangInfoManager.Start();
                    return;
                }
                if (_isDop)
                {
                    text = text.Substring(1);
                }
                if (_isAutoSwitch && !string.IsNullOrEmpty(_lastAutoText) && text.StartsWith(_lastAutoText))
                {
                    _isAutoSwitch = false;
                    _isDop = false;
                    IsEnabled = true;
                    LangInfoManager.Start();
                    return;
                }

                var currentLayoutHdl = KeyboardLayoutMethods.GetCurrentKeyboardLayoutHdl();
                // если при автопереключении пользователь уже самостоятельно переключил язык а автопереключение еще думает что нет
                if (_isAutoSwitch && currentLayoutHdl == _langCode)
                {
                    _isAutoSwitch = false;
                    _isDop = false;
                    IsEnabled = true;
                    LangInfoManager.Start();
                    return;
                }
                // если не автопереключение то ищем следующую раскладку и назначаем в глобальные переменные значения
                if (!_isAutoSwitch)
                {
                    int index = KeyboardLayoutCommon.LayoutHandleList.IndexOf(_currentLayoutHdl) + 1;
                    if (index == KeyboardLayoutCommon.LayoutHandleList.Count) index = 0;

                    var nextKeyboardLayoutName = KeyboardLayoutMethods.GetKeyboardLayoutNameByHandle(KeyboardLayoutCommon.LayoutHandleList[index]);
                    var nameNext = currentKeyboardLayoutName.ToUpper() + "\u2192" + nextKeyboardLayoutName.ToUpper();
                    if (SettingsManager.Settings.SwitcherNotTrueListOfLang.Contains(nameNext))
                    {
                        index += 1;
                        if (index == KeyboardLayoutCommon.LayoutHandleList.Count) index = 0;
                        nextKeyboardLayoutName = KeyboardLayoutMethods.GetKeyboardLayoutNameByHandle(KeyboardLayoutCommon.LayoutHandleList[index]);
                        if (nextKeyboardLayoutName == currentKeyboardLayoutName)
                        {
                            _isAutoSwitch = false;
                            _isDop = false;
                            IsEnabled = true;
                            LangInfoManager.Start();
                            return;
                        }
                    }

                    _langCodeName = nextKeyboardLayoutName;
                    _langCode = KeyboardLayoutCommon.LayoutHandleList[index];
                }

                var textLength = text.Length;
                if (text.Contains(Environment.NewLine))
                {
                    textLength--;
                }
                bool isRShiftWasDown = CommonHookListener.IsRShiftPressed;
                bool isLShiftWasDown = CommonHookListener.IsLShiftPressed;
                if (_isAutoSwitch)
                {
                    KeyboardState.ReleaseAllKeys();
                }

                for (int i = 0; i < textLength; i++)
                {
                    sim.Keyboard.KeyPress(VirtualKeyCode.BACK).Sleep(5);
                }

                _lastSwitchText = "";

                if (_isAutoSwitch)
                {
                    _isAutoSwitchingNow = true;
                }

                IntPtr switchLayoutToLangCode;

                if (_isAutoSwitch)
                {
                    _lastAutoText = text;
                    _lastAutoTextForRules = text;
                    IsLayoutChanging = true;
                    _lastAutoLangCode = _langCodeName;
                    _langCodeForCancelAutoSwitch = _currentLayoutHdl;
                    switchLayoutToLangCode = _langCode;
                }
                else
                {
                    if (_langCodeForCancelAutoSwitch != IntPtr.Zero)
                    {
                        foreach (var intPtr in KeyboardLayoutCommon.LangCodeList)
                        {
                            if (intPtr.Value.Equals(_langCodeForCancelAutoSwitch))
                            {
                                if (KeyboardLayoutCommon.AutoSwitcherLayouts != null)
                                {
                                    foreach (var autoSwitcherLayout in KeyboardLayoutCommon.AutoSwitcherLayouts)
                                    {
                                        if (autoSwitcherLayout.Value.Equals(intPtr.Key))
                                        {
                                            _langCodeName = autoSwitcherLayout.Key;
                                        }
                                    }
                                }
                            }
                        }

                        switchLayoutToLangCode = _langCodeForCancelAutoSwitch;
                        _langCodeForCancelAutoSwitch = IntPtr.Zero;
                    }
                    else
                    {
                        switchLayoutToLangCode = _langCode;
                    }
                    _lastSwitchText = text;
                }

                if (_langCodeName != null)
                {
                    var newText = GetTextAllLine(KeyboardLayoutCommon.AutoSwitcherTextByLayouts[_langCodeName]);
                    if (_isDop)
                    {
                        newText = newText.Substring(1);
                    }
                    // повторная проверка автопереключения для нового слова

                    _lastInputText = _lastInputText?.Replace(text, newText);
                    _lastInputTextDiary = _lastInputTextDiary?.Replace(text, newText);


                    if (_langCodeName == "ko" || _langCodeName == "ch" || _langCodeName == "jp")
                    {
                        if (_lastInputKeys.Count > newText.Length)
                        {
                            _lastInputKeys.RemoveRange(0, _lastInputKeys.Count - newText.Length);
                        }

                        _isAutoSwitchingNow = false;
                        KeyboardLayoutSwitcher.SwitchLayoutToLangForAutoSwitch(switchLayoutToLangCode);
                        _currentLayoutHdl = switchLayoutToLangCode;
                        for (int i = 0; i < _lastInputKeys.Count; i++)
                        {
                            Enum.TryParse(_lastInputKeys[i].ToString(), true, out VirtualKeyCode sdf);
                            sim.Keyboard.KeyPress(sdf);
                        }
                        if (_isAutoSwitch) _langWasAutoSwitched = true;
                        else _keyboardLayoutChangedManually = true;
                    }
                    else
                    {
                        KeyboardLayoutSwitcher.SwitchLayoutToLangForAutoSwitch(switchLayoutToLangCode);
                        SendText.SendStringByTextEntry(newText, false);
                        _currentLayoutHdl = switchLayoutToLangCode;
                        if (_isAutoSwitch) _langWasAutoSwitched = true;
                        else _keyboardLayoutChangedManually = true;
                    }

                    // Правила переключения
                    text = text.ToLower().Trim();
                    newText = newText.ToLower().Trim();
                    if (SettingsManager.Settings.AutoSwitcherAddRule && text.Length > 0)
                    {
                        if (!_isAutoSwitch && _lastAutoTextForRules?.Trim() == newText.Trim())
                        {
                            var autoSwitchRuleDataModel = VMContainer.Instance.AutoSwitcherSettingsViewModel.AutoSwitchRuleDataModels.FirstOrDefault(x => x.Text?.ToLower().Trim() == newText.ToLower().Trim());
                            if (autoSwitchRuleDataModel != null)
                            {

                                if (autoSwitchRuleDataModel.ManualSwitchCount > -1)
                                {
                                    autoSwitchRuleDataModel.ManualSwitchCount -= 1;
                                }
                                if (autoSwitchRuleDataModel.ManualSwitchCount == 0)
                                {
                                    RemoveAutoSwitchRule(autoSwitchRuleDataModel);
                                }
                                else
                                {
                                    UpdateAutoSwitchRule(autoSwitchRuleDataModel);
                                }

                                if (autoSwitchRuleDataModel.Text != null)
                                    RemoveDoublelargerSize(autoSwitchRuleDataModel.Text);
                            }
                            else
                            {
                                autoSwitchRuleDataModel = new AutoSwitchRuleDataModel()
                                {
                                    Text = newText.ToLower().Trim(),
                                    ManualSwitchCount = -1
                                };
                                if (autoSwitchRuleDataModel.Text.Length > 1 || (autoSwitchRuleDataModel.Text.Length == 1 && SettingsManager.Settings.AutoSwitcherIsSwitchOneLetter))
                                {
                                    AddAutoSwitchRule(autoSwitchRuleDataModel);
                                    RemoveDoublelargerSize(autoSwitchRuleDataModel.Text);
                                }
                            }
                        }
                        if (!_isAutoSwitch && _lastAutoTextForRules?.Trim() != newText.Trim())
                        {
                            var autoSwitchRuleDataModel = VMContainer.Instance.AutoSwitcherSettingsViewModel.AutoSwitchRuleDataModels.FirstOrDefault(x => x.Text?.ToLower().Trim() == text.ToLower().Trim());
                            if (autoSwitchRuleDataModel == null)
                            {
                                // окно подтверждение на добавление правила
                                if (SettingsManager.Settings.AutoSwitcherShowAcceptWindow)
                                {
                                    ShowAcceptWindows(text);
                                }
                                else
                                {
                                    autoSwitchRuleDataModel = new AutoSwitchRuleDataModel()
                                    {
                                        Text = text.ToLower().Trim(),
                                        ManualSwitchCount = 1
                                    };
                                    if (autoSwitchRuleDataModel.Text.Length > 1 || (autoSwitchRuleDataModel.Text.Length == 1 && SettingsManager.Settings.AutoSwitcherIsSwitchOneLetter))
                                    {
                                        AddAutoSwitchRule(autoSwitchRuleDataModel);
                                        RemoveDoublelargerSize(autoSwitchRuleDataModel.Text);
                                    }
                                }
                            }
                            else
                            {
                                if (autoSwitchRuleDataModel.ManualSwitchCount < SettingsManager.Settings.AutoSwitcherCountCheckRule)
                                {
                                    autoSwitchRuleDataModel.ManualSwitchCount += 1;
                                    if (autoSwitchRuleDataModel.ManualSwitchCount == 0)
                                    {
                                        RemoveAutoSwitchRule(autoSwitchRuleDataModel);
                                    }
                                    else
                                    {
                                        UpdateAutoSwitchRule(autoSwitchRuleDataModel);
                                        if (autoSwitchRuleDataModel.Text != null)
                                            RemoveDoublelargerSize(autoSwitchRuleDataModel.Text);
                                    }
                                }
                            }

                            if (KeyboardLayoutCommon.LayoutHandleList.Count < 3)
                            {
                                var autoSwitchRuleDataModelNew = VMContainer.Instance.AutoSwitcherSettingsViewModel.AutoSwitchRuleDataModels.FirstOrDefault(x => x.Text?.ToLower().Trim() == newText.ToLower().Trim());
                                if (autoSwitchRuleDataModelNew != null)
                                {
                                    if (autoSwitchRuleDataModelNew.ManualSwitchCount > -1)
                                    {
                                        autoSwitchRuleDataModelNew.ManualSwitchCount -= 1;
                                    }
                                    if (autoSwitchRuleDataModelNew.ManualSwitchCount == 0)
                                    {
                                        RemoveAutoSwitchRule(autoSwitchRuleDataModelNew);
                                    }
                                    else
                                    {
                                        UpdateAutoSwitchRule(autoSwitchRuleDataModelNew);
                                        if (autoSwitchRuleDataModelNew.Text != null)
                                            RemoveDoublelargerSize(autoSwitchRuleDataModelNew.Text);
                                    }
                                }
                            }

                        }
                    }
                }

                if (SettingsManager.Settings.SwitcherSwitchMethod != 1)
                {
                    if (_isAutoSwitch && (isLShiftWasDown || isRShiftWasDown))
                    {
                        if (isLShiftWasDown) new InputSimulator().Keyboard.KeyDown(VirtualKeyCode.LSHIFT);
                        if (isRShiftWasDown) new InputSimulator().Keyboard.KeyDown(VirtualKeyCode.RSHIFT);
                    }
                }
                _lastAutoTextForRules = _lastAutoText;
                _isAutoSwitch = false;
                _isDop = false;
                IsEnabled = true;
                LangInfoManager.Start();

            }
            catch
            {
                _isAutoSwitch = false;
                _isDop = false;
                IsEnabled = true;
                LangInfoManager.LangFlagRestart();
            }
            _isAutoSwitchingNow = false;
        }

        private static void SwitchAccept(string text)
        {
            var autoSwitchRuleDataModel = new AutoSwitchRuleDataModel()
            {
                Text = text.ToLower().Trim(),
                ManualSwitchCount = SettingsManager.Settings.AutoSwitcherCountCheckRule
            };

            if (autoSwitchRuleDataModel.Text.Length > 1 ||
                (autoSwitchRuleDataModel.Text.Length == 1 && SettingsManager.Settings.AutoSwitcherIsSwitchOneLetter))
            {
                AddAutoSwitchRule(autoSwitchRuleDataModel);
                RemoveDoublelargerSize(autoSwitchRuleDataModel.Text);
            }
        }

        private static void ShowAcceptWindows(string? text)
        {
            _autoSwitchAcceptWindow.Show(text);
        }

        private static void RemoveDoublelargerSize(string text)
        {
            if (text.Length > 1)
            {
                var dataModel = VMContainer.Instance.AutoSwitcherSettingsViewModel.AutoSwitchRuleDataModels.FirstOrDefault(x => x.Text != null && x.Text.ToLower().Trim().StartsWith(text.ToLower().Trim()) && x.Text.Length > text.Length);
                if (dataModel != null)
                {
                    RemoveAutoSwitchRule(dataModel);
                }
            }

        }

        private static void MouseEventSwitcherLang(GlobalMouseEventArgs globalMouseEventArgs)
        {
            _isNoKeyPress = false;
            BeginNewWord(true);
        }

        private static void TimerAutoSwitchCheck()
        {
            Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Normal, (ThreadStart)delegate
            {
                if (SettingsManager.Settings.AutoSwitcherIsOn && SettingsManager.Settings.AutoSwitcherAfterPause && _isNoKeyPress && _isAutoSwitch && !_isLastAutoswitch)
                {
                    _isLastAutoswitch = true;
                    SwitchText(false, true);
                    _isRuleAndPunc = false;
                    _isLastAutoswitch = false;
                }
                _timerAutoSwitch.Stop();
            });
        }

        private static void KeyUpSwitcherLang(GlobalKeyEventArgs e)
        {
            try
            {
                if (_isAutoSwitchingNow && IsPrintable(e))
                {
                    e.Handled = true;
                    return;
                }
                if (!IsEnabled || _isAutoSwitchingNow)
                {
                    return;
                }
                if (_lastAutochangeGo)
                {
                    if (_currentAutochangeDataModel != null)
                    {
                        var autochangeModel = _currentAutochangeDataModel;
                        Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Background,
                            new Action(() => SnippetsGo(autochangeModel)));
                    }
                    _lastAutochangeGo = false;
                    return;
                }
                _lastKeyUp = (int)e.KeyCode;
                if (IsLayoutChanging)
                {
                    if (_isPrintable)
                    {
                        if (_lastKeyDown != (int)VirtualKeycodes.Space)
                        {
                            e.Handled = true;
                        }
                    }
                    return;
                }
                if (_isBeginNewWord)
                {
                    if (!_isSwitchLang)
                    {
                        _isSwitchLang = IsSwitchLang();
                        if (!_isSwitchLang || e.KeyCode == VirtualKeycodes.LeftArrow || e.KeyCode == VirtualKeycodes.RightArrow)
                        {
                            _isLastAutoswitch = false;
                            BeginNewWord(false);
                        }
                        return;
                    }
                }
                if (_isAutoSwitch && !_isLastAutoswitch && !_isRuleAndPunc)
                {
                    var currentKeyboardLayoutName = KeyboardLayoutMethods.GetCurrentKeyboardLayoutName(_currentLayoutHdl);
                    var autoText =
                        GetTextAllLine(KeyboardLayoutCommon.AutoSwitcherTextByLayouts[currentKeyboardLayoutName]).Trim().ToLower();
                    if (_inputChar != null && (_inputChar.Length == 1 && (char.IsPunctuation(Convert.ToChar(_inputChar)) || char.IsWhiteSpace(Convert.ToChar(_inputChar))) || autoText.Length > 3))
                    {
                        bool isNot = !char.IsPunctuation(Convert.ToChar(_inputChar)) &&
                                     !char.IsWhiteSpace(Convert.ToChar(_inputChar)) &&
                                     SettingsManager.Settings.AutoSwitcherOnlyAfterSeparator;
                        if (!isNot)
                        {
                            _isLastAutoswitch = true;
                            SwitchText(false);
                            if (_inputChar.Length == 1 && (char.IsPunctuation(Convert.ToChar(_inputChar)) || char.IsWhiteSpace(Convert.ToChar(_inputChar))))
                            {
                                _isLastAutoswitch = false;
                                _lastAutoText = "";
                                _lastSwitchText = "";
                                _keyboardLayoutChangedManually = false;
                            }
                            return;
                        }
                    }
                }

                if (_isPrintable)
                {
                    if (_inputChar != null && _inputChar.Length == 1 && (char.IsPunctuation(Convert.ToChar(_inputChar)) || char.IsWhiteSpace(Convert.ToChar(_inputChar))))
                    {
                        if ((_isAutoSwitch || _isAutoSwitchDic) && !_isLastAutoswitch && (_isRuleAndPunc || _isAutoSwitchDic))
                        {
                            _isAutoSwitch = true;
                            _isLastAutoswitch = true;
                            SwitchText(false);
                            _isRuleAndPunc = false;
                            _isLastAutoswitch = false;
                            return;
                        }
                    }

                    if (_inputChar != null && ((_inputChar.Length == 1 && (char.IsPunctuation(Convert.ToChar(_inputChar)) || char.IsWhiteSpace(Convert.ToChar(_inputChar)))) || e.KeyCode == VirtualKeycodes.Enter))
                    {
                        _isLastAutoswitch = false;
                        _lastAutoText = "";
                        _lastSwitchText = "";
                        var asd = GetTextAllLine(_lastInputText).Trim();
                        if (asd != "")
                        {
                            _keyboardLayoutChangedManually = false;
                        }

                    }
                }

                if (_isAutoSwitch && !_isLastAutoswitch)
                {
                    _isNoKeyPress = true;
                    _timerAutoSwitch.Start();
                }

            }
            catch
            {
                BeginNewWord(true);
            }
        }

        private static void KeyDownSwitcherLang(GlobalKeyEventArgs e)
        {
            try
            {
                _currentLayoutHdl = KeyboardLayoutMethods.GetCurrentKeyboardLayoutHdl();
                _isPrintable = IsPrintable(e);
                if (_isCheckWhileTypingWindowShow)
                {
                    if (CheckSpellCheckWhileTyping(e))
                    {
                        _isCheckWhileTypingWindowShow = false;
                        _keyboardLayoutChangedManually = false;
                        return;
                    }
                }
                _lastAutochangeGo = false;
                _isNoKeyPress = false;
                _timerAutoSwitch.Stop();
                if (_lastKeyDown == (int)VirtualKeycodes.Space && _lastKeyUp != (int)VirtualKeycodes.Space)
                {
                    KeyUpSwitcherLang(e);
                }
                _lastKeyDown = (int)e.KeyCode;
                if (!IsEnabled)
                {
                    return;
                }

                _isBeginNewWord = false;
                _isSwitchLang = false;
                if (_isAutoSwitch && !_isLastAutoswitch && e.KeyCode == VirtualKeycodes.Enter && SettingsManager.Settings.AutoSwitcherSwitchTextLangAfterPressEnter)
                {
                    e.Handled = true;
                    _isLastAutoswitch = true;
                    SwitchText(false);
                    BeginNewWord(false);
                    var sim = new InputSimulator();
                    sim.Keyboard.KeyPress(VirtualKeyCode.RETURN);
                    return;
                }

                if (HaveTrackingKeys(e))
                {
                    return;
                }
                var notModified = !HaveModifiers(e);
                if (e.KeyCode == VirtualKeycodes.Backspace && notModified)
                {
                    RemoveLast();
                    _isAutoSwitch = false;
                    return;
                }

                if (e.KeyCode == VirtualKeycodes.Esc ||
                    (e.KeyCode == VirtualKeycodes.LeftArrow && Keyboard.IsKeyDown(Key.Right)) ||
                    (e.KeyCode == VirtualKeycodes.RightArrow && Keyboard.IsKeyDown(Key.Left)))
                {
                    BeginNewWord(false);
                    return;
                }

                if (_isCheckWhileTypingWindowShow)
                {
                    if (e.KeyCode == VirtualKeycodes.Delete || e.KeyCode == VirtualKeycodes.Insert)
                    {
                        return;
                    }
                }
                if (e.KeyCode == VirtualKeycodes.LeftArrow && notModified)
                {
                    _countLeftRemove++;
                    if (SpellCheckWhileTyping.CheckLast(GetTextAllLine(_lastInputText)) && !_isSpellCheckReplacedLast)
                    {
                        SpellCheckStart(_lastCharForSpell);
                        SpellCheckWhileTyping.Suggestions.Clear();
                    }
                    _isAutoSwitch = false;
                    return;
                }
                if (e.KeyCode == VirtualKeycodes.RightArrow && notModified)
                {
                    _countLeftRemove--;
                    if (_countLeftRemove < 0)
                    {
                        _countLeftRemove = 0;
                    }
                    if (SpellCheckWhileTyping.CheckLast(GetTextAllLine(_lastInputText)) && !_isSpellCheckReplacedLast)
                    {
                        SpellCheckStart(_lastCharForSpell);
                        SpellCheckWhileTyping.Suggestions.Clear();
                    }
                    _isAutoSwitch = false;
                    return;
                }
                if (e.KeyCode == VirtualKeycodes.End && notModified)
                {
                    _countLeftRemove = 0;
                    _isAutoSwitch = false;
                    return;
                }
                _countLeftRemove = 0;
                _langCodeForCancelAutoSwitch = IntPtr.Zero;
                if (CheckSnippets(e))
                {
                    BeginNewWord(false);
                    return;
                }

                if (_isPrintable)
                {
                    CorrectTwoCapitalLetters();
                    var windowId = ForegroundWindow.GetForegroundNative();

                    if (_lastWindowId != windowId)
                    {
                        BeginNewWord(true);
                    }
                    _lastWindowId = windowId;
                    AddKeyToCurrentText(e);
                    CorrectCapsLock();
                    if (IsLayoutChanging)
                    {
                        if (_lastKeyDown != (int)VirtualKeycodes.Space)
                        {
                            e.Handled = true;
                            if (_lastAutoLangCode != null)
                            {
                                string newText = GetTextAllLine(KeyboardLayoutCommon.AutoSwitcherTextByLayouts[_lastAutoLangCode]).Last().ToString();
                                SendText.SendStringByTextEntry(newText, false);
                            }
                        }
                    }

                    CheckAutochangeInText();
                    if (SettingsManager.Settings.AutoSwitcherIsOn)
                    {
                        if (_inputChar != null && !_isLastAutoswitch && !char.IsSeparator(Convert.ToChar(_inputChar)) && !char.IsWhiteSpace(Convert.ToChar(_inputChar)))
                        {
                            CheckAutoswitch();
                        }
                    }
                    if (SettingsManager.Settings.SpellCheckWhileTyping)
                    {
                        if (_inputChar?.Length == 1)
                        {
                            char c = Convert.ToChar(_inputChar);
                            if (!_isAutoSwitch && c != '\r' && (char.IsSeparator(c) || char.IsWhiteSpace(c) || char.IsPunctuation(c)))
                            {
                                if (SpellCheckWhileTyping.Suggestions.Count > 0 && !_langWasAutoSwitched)
                                {
                                    SpellCheckStart(_inputChar);
                                    SpellCheckWhileTyping.Suggestions.Clear();
                                }
                            }
                            if (!_isAutoSwitch && char.IsLetter(c))
                            {
                                SpellCheckInText(_lastInputText);
                            }
                        }

                    }


                    return;
                }
                else
                {
                    _keyboardLayoutChangedManually = false;
                    SpellCheckWhileTyping.Suggestions.Clear();
                }
                if (!IsLayoutChanging)
                    _isBeginNewWord = true;
            }
            catch
            {
                BeginNewWord(true);
                CommonHookListener.Stop();
                CommonHookListener.Start();
            }
        }

        private static bool CheckSpellCheckWhileTyping(GlobalKeyEventArgs e)
        {
            List<string?> numberList;
            if (SettingsManager.Settings.SpellCheckWhileTypingUseNumber)
            {
                numberList = new List<string?>() { "1", "2", "3", "4", "5", "6", "7", "8", "9" };
            }
            else
            {
                numberList = new List<string?>() { "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9" };
            }
            if (SettingsManager.Settings.SpellCheckWhileTypingUseNumber)
            {
                var key = KeyboardLayoutMethods.CodeToString(e);
                if (numberList.Contains(key) && numberList.IndexOf(key) >= 0)
                {
                    return true;
                }
            }
            else
            {
                if (numberList.Contains(e.KeyCode.ToString()) && numberList.IndexOf(e.KeyCode.ToString()) >= 0)
                {
                    return true;
                }
            }
            return false;
        }

        private static void CorrectTwoCapitalLetters()
        {
            if (SettingsManager.Settings.AutoSwitcherFixTwoUpperCaseLettersInStart && CheckActiveProcessFileName.CheckAutoSwitch())
            {
                var oldText = GetTextAllLine(_lastInputText?.Trim()).Trim();
                if (oldText != "" && oldText.Length == 3)
                {
                    if (Char.IsLetter(oldText[0]) && Char.IsLetter(oldText[1]) && Char.IsLetter(oldText[2]))
                    {
                        if (Char.IsUpper(oldText[0]) && Char.IsUpper(oldText[1]) && !Char.IsUpper(oldText[2]))
                        {
                            var autoSwitchRuleDataModel = VMContainer.Instance.AutoSwitcherSettingsViewModel.AutoSwitchRuleDataModels.FirstOrDefault(x => _lastInputText != null && x.Text != null && x.Text.Trim().StartsWith(_lastInputText.Trim()));
                            if (autoSwitchRuleDataModel == null)
                            {
                                IsEnabled = false;
                                var newText = oldText[1].ToString().ToLower() + oldText[2];
                                var sim = new InputSimulator();
                                sim.Keyboard.KeyPress(VirtualKeyCode.BACK);
                                sim.Keyboard.KeyPress(VirtualKeyCode.BACK);
                                SendText.SendStringByTextEntry(newText, false);
                                _lastInputText = _lastInputText?.Remove(_lastInputText.Length - 2);
                                _lastInputText += newText;

                                _lastInputTextDiary = _lastInputTextDiary?.Remove(_lastInputTextDiary.Length - 2);
                                _lastInputTextDiary += newText;
                                foreach (var switcherLayout in KeyboardLayoutCommon.AutoSwitcherLayouts)
                                {
                                    var text = KeyboardLayoutCommon.AutoSwitcherTextByLayouts?[switcherLayout.Key];
                                    if (KeyboardLayoutCommon.AutoSwitcherTextByLayouts != null)
                                        if (text != null)
                                        {
                                            KeyboardLayoutCommon.AutoSwitcherTextByLayouts[switcherLayout.Key] =
                                                text[0] + text[1].ToString().ToLower() + text[2];
                                        }
                                }

                                IsEnabled = true;
                            }
                        }
                    }
                }
            }
        }

        private static void CorrectCapsLock()
        {
            if (SettingsManager.Settings.AutoSwitcherFixWrongUpperCase && CheckActiveProcessFileName.CheckAutoSwitch())
            {

                var oldText = GetTextAllLine(_lastInputText);
                if (_langWasAutoSwitched)
                {
                    if (_lastAutoLangCode != null)
                        oldText = GetTextAllLine(KeyboardLayoutCommon.AutoSwitcherTextByLayouts[_lastAutoLangCode]);
                }

                if (oldText != "" && oldText.Length > 2)
                {
                    if (!char.IsLower(oldText[0]))
                    {
                        return;
                    }
                    bool isUpperAll = false;
                    for (var i = 1; i < oldText.Length; i++)
                    {
                        if (char.IsLetter(oldText[i]) && char.IsUpper(oldText[i]))
                        {
                            isUpperAll = true;
                        }
                        if (char.IsLetter(oldText[i]) && char.IsLower(oldText[i]))
                        {
                            isUpperAll = false;
                            break;
                        }
                    }

                    if (isUpperAll)
                    {
                        if (_inputChar != null)
                        {
                            char c = Convert.ToChar(_inputChar);
                            if (_inputChar.Length == 1)
                            {
                                if (char.IsSeparator(c) || char.IsWhiteSpace(c) || char.IsPunctuation(c))
                                {
                                    var autoSwitchRuleDataModel = VMContainer.Instance.AutoSwitcherSettingsViewModel.AutoSwitchRuleDataModels.FirstOrDefault(x => _lastInputText != null && x.Text != null && x.Text.Trim().StartsWith(_lastInputText.Trim()));
                                    if (autoSwitchRuleDataModel == null)
                                    {
                                        IsEnabled = false;
                                        var newText = "";

                                        var sim = new InputSimulator();
                                        foreach (char ch in oldText)
                                        {
                                            if (char.IsLetter(ch) && char.IsUpper(ch))
                                            {
                                                newText += char.ToLower(ch);
                                                sim.Keyboard.KeyPress(VirtualKeyCode.BACK);
                                            }
                                            else if (char.IsLetter(ch) && char.IsLower(ch))
                                            {
                                                newText += char.ToUpper(ch);
                                                sim.Keyboard.KeyPress(VirtualKeyCode.BACK);
                                            }
                                            else newText += ch;

                                        }
                                        SendText.SendStringByTextEntry(newText.Remove(newText.Length - 1, 1), false);
                                        if (Keyboard.GetKeyStates(Key.CapsLock) == KeyStates.Toggled)
                                        {
                                            if (SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 5 ||
                                                SettingsManager.Settings.SwitcherSwitchLangByNonStandartKey == 8)
                                            {
                                                sim.Keyboard.KeyDown(VirtualKeyCode.LSHIFT);
                                                sim.Keyboard.KeyPress(VirtualKeyCode.RSHIFT);
                                                sim.Keyboard.KeyUp(VirtualKeyCode.LSHIFT);
                                            }
                                            else
                                            {
                                                sim.Keyboard.KeyPress(VirtualKeyCode.CAPITAL);
                                            }
                                        }
                                        _lastInputText = _lastInputText?.Replace(oldText, newText);
                                        _lastInputTextDiary = _lastInputTextDiary?.Replace(oldText, newText);
                                        foreach (var switcherLayout in KeyboardLayoutCommon.AutoSwitcherLayouts)
                                        {
                                            var newTextFixed = "";
                                            var text = KeyboardLayoutCommon.AutoSwitcherTextByLayouts?[
                                                switcherLayout.Key];
                                            if (text != null)
                                            {
                                                foreach (char ch in text)
                                                {
                                                    if (char.IsLetter(ch) && char.IsUpper(ch))
                                                        newTextFixed += char.ToLower(ch);
                                                    else if (char.IsLetter(ch) && char.IsLower(ch))
                                                        newTextFixed += char.ToUpper(ch);
                                                    else newTextFixed += ch;
                                                }
                                            }
                                            if (KeyboardLayoutCommon.AutoSwitcherTextByLayouts != null)
                                                KeyboardLayoutCommon.AutoSwitcherTextByLayouts[switcherLayout.Key] =
                                                    newTextFixed;
                                        }

                                        IsEnabled = true;
                                    }
                                }
                            }
                        }
                    }

                }
            }
        }

        private static void CheckAutoswitch()
        {
            try
            {
                _isAutoSwitchDic = false;
                var currentKeyboardLayoutName = KeyboardLayoutMethods.GetCurrentKeyboardLayoutName(_currentLayoutHdl);
                var textCurrent = GetTextAllLine(KeyboardLayoutCommon.AutoSwitcherTextByLayouts[currentKeyboardLayoutName]).Trim().ToLower();
                if (GetRuleSwitch(textCurrent, currentKeyboardLayoutName, false, false, false) != 0)
                {
                    return;
                }
                if (SettingsManager.Settings.AutoSwitcherIsOn && textCurrent.Length < 5)
                {
                    _isDop = false;
                    GetAutoSwitch(textCurrent, currentKeyboardLayoutName, false);
                    if (!_isAutoSwitch && textCurrent.Length > 1 && (textCurrent.StartsWith("\"") || textCurrent.StartsWith("'") || textCurrent.StartsWith("@") || textCurrent.StartsWith(".")))
                    {
                        if (GetRuleSwitch(textCurrent, currentKeyboardLayoutName, true, false, false) != 0)
                        {
                            return;
                        }
                        GetAutoSwitch(textCurrent.Substring(1), currentKeyboardLayoutName, true);
                        _isDop = _isAutoSwitch;
                        if (textCurrent.StartsWith("@"))
                        {
                            _isDop = false;
                        }
                    }
                }
            }
            catch
            {
                BeginNewWord(true);
            }
        }

        private static bool _innerGetRuleSwitch;

        private static int GetRuleSwitch(string textCurrent, string? currentKeyboardLayoutName, bool isDop, bool onlyCheck, bool checkForContains)
        {
            if (string.IsNullOrEmpty(currentKeyboardLayoutName))
            {
                return 0;
            }
            if (textCurrent.Length < 1)
            {
                return 0;
            }

            if (checkForContains)
            {
                if (textCurrent.Length > 3)
                {
                    var autoSwitchRuleForContains = VMContainer.Instance.AutoSwitcherSettingsViewModel.AutoSwitchRuleDataModels.FirstOrDefault(x => x.Text != null && x.Text.Trim().ToLower().StartsWith(textCurrent.Trim().ToLower()));
                    if (autoSwitchRuleForContains != null)
                    {
                        return autoSwitchRuleForContains.ManualSwitchCount;
                    }
                }
                return 0;
            }

            if (isDop)
            {
                textCurrent = textCurrent.Substring(1);
            }
            if (_inputChar != null && !char.IsPunctuation(Convert.ToChar(_inputChar)) && !char.IsWhiteSpace(Convert.ToChar(_inputChar)) && _isRuleAndPunc)
            {
                _isRuleAndPunc = false;
                _isAutoSwitch = false;
            }
            var autoSwitchRuleDataModel = GetMatchForRules(textCurrent);
            if (autoSwitchRuleDataModel != null)
            {
                if (autoSwitchRuleDataModel.ManualSwitchCount == SettingsManager.Settings.AutoSwitcherCountCheckRule)
                {

                    var code = KeyboardLayoutCommon.LangCodeList[KeyboardLayoutCommon.AutoSwitcherLayouts[currentKeyboardLayoutName]];
                    var codeName = currentKeyboardLayoutName;
                    foreach (var autoSwitcherLayout in KeyboardLayoutCommon.AutoSwitcherLayouts)
                    {
                        if (KeyboardLayoutCommon.LangCodeList[autoSwitcherLayout.Value] != code)
                        {
                            code = KeyboardLayoutCommon.LangCodeList[autoSwitcherLayout.Value];
                            codeName = autoSwitcherLayout.Key;
                            break;
                        }
                    }
                    if (onlyCheck)
                    {
                        return 1;
                    }

                    if (!_innerGetRuleSwitch)
                    {
                        _innerGetRuleSwitch = true;
                        var textNew = GetTextAllLine(KeyboardLayoutCommon.AutoSwitcherTextByLayouts[codeName]).Trim().ToLower();
                        if (GetRuleSwitch(textNew, codeName, isDop, false, false) == 1)
                        {
                            _innerGetRuleSwitch = false;
                            return 1;
                        }
                    }
                    _innerGetRuleSwitch = false;
                    _isRuleAndPunc = false;
                    if (textCurrent.Length < 5)
                    {
                        _isRuleAndPunc = true;
                    }
                    _langCodeName = codeName;
                    _langCode = code;

                    _isAutoSwitch = true;
                    return 1;
                }
                if (autoSwitchRuleDataModel.ManualSwitchCount == -1)
                {
                    if (onlyCheck)
                    {
                        return -1;
                    }
                    _isAutoSwitch = false;
                    _isRuleAndPunc = false;
                    return -1;
                }
            }
            return 0;
        }

        private static AutoSwitchRuleDataModel? GetMatchForRules(string textCurrent)
        {
            AutoSwitchRuleDataModel? autoSwitchRuleDataModel = null;
            if (textCurrent.Length > 5)
            {
                autoSwitchRuleDataModel = VMContainer.Instance.AutoSwitcherSettingsViewModel.AutoSwitchRuleDataModels.FirstOrDefault(x => x.Text != null && x.Text.Trim().ToLower().StartsWith(textCurrent.Trim().ToLower()) && textCurrent.Length / (double)x.Text.Length > 0.7);
            }
            if (autoSwitchRuleDataModel == null)
            {
                autoSwitchRuleDataModel = VMContainer.Instance.AutoSwitcherSettingsViewModel.AutoSwitchRuleDataModels.FirstOrDefault(x => x.Text != null && x.Text.Trim().ToLower() == textCurrent.Trim().ToLower());
            }
            return autoSwitchRuleDataModel;
        }

        private static void GetAutoSwitch(string textCurrent, string? currentKeyboardLayoutName, bool isDop)
        {
            if (string.IsNullOrEmpty(currentKeyboardLayoutName))
            {
                return;
            }
            bool withoutDict;
            if (textCurrent.Length == 1)
            {
                withoutDict =
                    KeyboardLayoutCommon.AutoSwitcherPossibleList.ContainsKey(currentKeyboardLayoutName) &&
                    KeyboardLayoutCommon.AutoSwitcherPossibleList[currentKeyboardLayoutName]
                        .FirstOrDefault(x => x.Equals(textCurrent)) == null;
            }
            else
            {
                withoutDict =
                    KeyboardLayoutCommon.AutoSwitcherPossibleList.ContainsKey(currentKeyboardLayoutName) &&
                    KeyboardLayoutCommon.AutoSwitcherPossibleList[currentKeyboardLayoutName]
                        .FirstOrDefault(x => x.StartsWith(textCurrent)) == null;
            }

            if (withoutDict)
            {
                foreach (var switcherLayout in KeyboardLayoutCommon.AutoSwitcherLayouts)
                {
                    var textOther =
                        GetTextAllLine(KeyboardLayoutCommon.AutoSwitcherTextByLayouts?[switcherLayout.Key]).Trim()
                            .ToLower();
                    if (isDop)
                    {
                        textOther = textOther.Substring(1);
                    }

                    if (KeyboardLayoutCommon.AutoSwitcherPossibleList != null &&
                        switcherLayout.Key != currentKeyboardLayoutName &&
                        KeyboardLayoutCommon.AutoSwitcherPossibleList.ContainsKey(switcherLayout.Key) &&
                        KeyboardLayoutCommon.AutoSwitcherPossibleList[switcherLayout.Key].Contains(textOther))
                    {
                        var ruleResult = GetRuleSwitch(textOther, switcherLayout.Key, false, true, false);
                        if (ruleResult != 1)
                        {
                            // случай всегда переключается слово genband
                            var ruleForContainsResult =
                                GetRuleSwitch(textOther, switcherLayout.Key, false, false, true);
                            if (ruleForContainsResult != 1)
                            {
                                _langCodeName = switcherLayout.Key;
                                _langCode = KeyboardLayoutCommon.LangCodeList[switcherLayout.Value];
                                _isAutoSwitch = true;
                                break;
                            }
                        }
                    }
                    else
                    {
                        _isAutoSwitch = false;
                    }
                }
            }
            else if (KeyboardLayoutCommon.AutoSwitcherPossibleList.TryGetValue(currentKeyboardLayoutName, out List<string>? value) && value.FirstOrDefault(x => x.StartsWith(textCurrent)) != null)
            {
                if (KeyboardLayoutCommon.AutoSwitcherPossibleDicList.ContainsKey(currentKeyboardLayoutName) && KeyboardLayoutCommon.AutoSwitcherPossibleDicList[currentKeyboardLayoutName].FirstOrDefault(x => x.Equals(textCurrent)) == null)
                {
                    foreach (var switcherLayout in KeyboardLayoutCommon.AutoSwitcherLayouts)
                    {
                        var textOther = GetTextAllLine(KeyboardLayoutCommon.AutoSwitcherTextByLayouts[switcherLayout.Key]).Trim().ToLower();
                        if (isDop)
                        {
                            textOther = textOther.Substring(1);
                        }

                        if (switcherLayout.Key != currentKeyboardLayoutName && KeyboardLayoutCommon.AutoSwitcherPossibleDicList.ContainsKey(switcherLayout.Key) && KeyboardLayoutCommon.AutoSwitcherPossibleDicList[switcherLayout.Key].Contains(textOther))
                        {
                            _langCodeName = switcherLayout.Key;
                            _langCode = KeyboardLayoutCommon.LangCodeList[switcherLayout.Value];
                            _isAutoSwitchDic = true;
                            break;
                        }
                        else
                        {
                            _isAutoSwitch = false;
                        }
                    }
                }
            }
            else
            {
                _isAutoSwitch = false;
                _isAutoSwitchDic = false;
            }
        }

        private static SnippetsDataModel? _currentAutochangeDataModel;



        // проверка что текущая последовательность символов удовлетворяет автозамене и показывает малое окно
        private static void CheckAutochangeInText()
        {
            if (SettingsManager.Settings.SnippetsIsOn)
            {
                if (!CheckActiveProcessFileName.CheckAutochange())
                {
                    return;
                }
                _currentAutochangeDataModel = null;
                if (SettingsManager.Settings.SnippetsWithOtherLayout)
                {
                    var textList = new List<string?>();
                    foreach (var switcherLayout in KeyboardLayoutCommon.AutoSwitcherLayouts)
                    {
                        textList.Add(KeyboardLayoutCommon.AutoSwitcherTextByLayouts[switcherLayout.Key]);
                    }
                    for (int i = 0; i < textList.Count; i++)
                    {
                        var text = GetTextAllLine(textList[i]);
                        if (SettingsManager.Settings.SnippetsIsCaseSensitive)
                        {
                            _currentAutochangeDataModel = VMContainer.Instance.SnippetsViewModel.SnippetsForSwitcher.FirstOrDefault(
                            x => x?.FromText == text || x?.FromText == " " + text);
                        }
                        else
                        {
                            _currentAutochangeDataModel = VMContainer.Instance.SnippetsViewModel.SnippetsForSwitcher.FirstOrDefault(
                            x => x?.FromText.ToLower() == text.ToLower() || x?.FromText.ToLower() == " " + text.ToLower());
                        }

                        if (_currentAutochangeDataModel != null) break;
                    }
                }
                else
                {
                    var text = GetTextAllLine(_lastInputText);
                    _currentAutochangeDataModel = VMContainer.Instance.SnippetsViewModel.SnippetsForSwitcher.FirstOrDefault(
                            x => x?.FromText == text || x?.FromText == " " + text);

                }

                if (_currentAutochangeDataModel != null)
                {
                    if (_currentAutochangeDataModel.IsChangeAtOnce && CheckActiveProcessFileName.CheckAutochange())
                    {
                        _lastAutochangeGo = true;
                        return;
                    }
                }

                if (_currentAutochangeDataModel != null && SettingsManager.Settings.SnippetsIsShowTipWindow)
                {
                    SnippetsMiniWindow autochangeDataModel = new SnippetsMiniWindow(_currentAutochangeDataModel.ShortText);

                    var location = CarretPosition.GetPosition();
                    autochangeDataModel.WindowStartupLocation = WindowStartupLocation.CenterScreen;
                    autochangeDataModel.Show();
                    if (Math.Abs(location.X - location.Y) > 0.0001)
                    {
                        var pos = MousePosition.GetMousePoint(location);
                        autochangeDataModel.WindowStartupLocation = WindowStartupLocation.Manual;
                        autochangeDataModel.Left = pos.X + 30;
                        autochangeDataModel.Top = pos.Y - 30;
                    }
                    autochangeDataModel.ShowHide();
                }
            }
        }

        // автозамена
        private static bool CheckSnippets(GlobalKeyEventArgs key)
        {
            if (SettingsManager.Settings.SnippetsIsOn && _currentAutochangeDataModel != null)
            {
                if (_currentAutochangeDataModel.IsChangeAtOnce)
                {
                    key.Handled = true;
                    // Запускаем SnippetsGo через делегат после завершения CheckSnippets
                    Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Background,
                        new Action(() => SnippetsGo(_currentAutochangeDataModel)));
                    return true;
                }
                if (
                    (SettingsManager.Settings.SnippetsMethodPastByKey == 0 && key.KeyCode == VirtualKeycodes.Tab)
                    || (SettingsManager.Settings.SnippetsMethodPastByKey == 1 && key.KeyCode == VirtualKeycodes.Enter)
                    || (SettingsManager.Settings.SnippetsMethodPastByKey == 2 && (key.KeyCode == VirtualKeycodes.Tab || key.KeyCode == VirtualKeycodes.Enter))
                    || (SettingsManager.Settings.SnippetsMethodPastByKey == 3 && key.KeyCode == VirtualKeycodes.Space)
                    || (SettingsManager.Settings.SnippetsMethodPastByKey == 4 && (key.KeyCode == VirtualKeycodes.Space || key.KeyCode == VirtualKeycodes.Enter))
                )
                {
                    key.Handled = true;
                    _lastAutochangeGo = true;
                    // Запускаем SnippetsGo через делегат после завершения CheckSnippets
                    var autochangeModel = _currentAutochangeDataModel;
                    Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Background,
                        new Action(() => SnippetsGo(autochangeModel)));
                    return true;
                }
            }
            return false;
        }



        private static void SnippetsGo(SnippetsDataModel? autochangeDataModel)
        {
            try
            {
                // Поскольку мы уже в основном потоке через Dispatcher.BeginInvoke,
                // можем безопасно работать с UI элементами
                SpellCheckWhileTyping.Suggestions.Clear();
                IsEnabled = false;
                CommonHookListener.IsKeyEnabled = false;
                var sim = new InputSimulator();
                KeyboardState.ReleaseAllKeys();

                if (autochangeDataModel != null)
                {
                    for (int i = 0; i < autochangeDataModel.FromText!.Length; i++)
                    {
                        sim.Keyboard.KeyPress(VirtualKeyCode.BACK).Sleep(5);
                    }

                    // Теперь можем использовать обычный SendStringByPaste с Dispatcher.Invoke
                    SendText.SendStringByPaste(autochangeDataModel.Text, false, false);

                    if (!string.IsNullOrEmpty(autochangeDataModel.LangToSwitch))
                    {
                        var langCode = KeyboardLayoutCommon.LangCodeList[
                            KeyboardLayoutCommon.AutoSwitcherLayouts[autochangeDataModel.LangToSwitch.ToLower()]];
                        if (KeyboardLayoutMethods.GetCurrentKeyboardLayoutHdl() != langCode)
                        {
                            KeyboardLayoutSwitcher.SwitchLayoutToLang(langCode);
                        }
                    }

                    _currentAutochangeDataModel = null;

                    if (autochangeDataModel.IsSetCursorPosition)
                    {
                        Thread.Sleep(100);
                        string? text = autochangeDataModel.Text?.Replace(Environment.NewLine, " ");
                        int carretPos = autochangeDataModel.CursorPosition;
                        for (int i = 0; i < autochangeDataModel.CursorPosition; i++)
                        {
                            if (autochangeDataModel.Text != null && autochangeDataModel.Text[i] == '\r')
                            {
                                carretPos--;
                            }
                        }

                        bool isLShift = Keyboard.IsKeyDown(Key.LeftShift);
                        bool isRShift = Keyboard.IsKeyDown(Key.RightShift);

                        if (isLShift)
                        {
                            sim.Keyboard.KeyUp(VirtualKeyCode.LSHIFT).Sleep(5);
                        }

                        if (isRShift)
                        {
                            sim.Keyboard.KeyUp(VirtualKeyCode.RSHIFT).Sleep(5);
                        }

                        if (text != null)
                        {
                            int leftCount = text.Length - carretPos;
                            for (int i = 0; i < leftCount; i++)
                            {
                                sim.Keyboard.KeyPress(VirtualKeyCode.LEFT).Sleep(5);
                            }
                        }

                        if (isLShift)
                        {
                            sim.Keyboard.KeyDown(VirtualKeyCode.LSHIFT).Sleep(5);
                        }

                        if (isRShift)
                        {
                            sim.Keyboard.KeyDown(VirtualKeyCode.RSHIFT).Sleep(5);
                        }
                    }

                    IsEnabled = true;
                    CommonHookListener.IsKeyEnabled = true;
                    if (SettingsManager.Settings.SnippetsIsEnabledCountUsage)
                    {
                        autochangeDataModel.CountUsage++;
                    }

                    SnippetsManager.UpdateData(autochangeDataModel);
                    BeginNewWord(true);
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
                IsEnabled = true;
                CommonHookListener.IsKeyEnabled = true;
            }
        }



        // проверка правописания текущего слова
        private static void SpellCheckInText(string? text)
        {
            SpellCheckWhileTyping.Suggestions.Clear();
            var checktext = GetTextAllLine(text);
            if (checktext != "")
                SpellCheckWhileTyping.CheckWord(checktext,
                    KeyboardLayoutMethods.GetCurrentKeyboardLayoutName(_currentLayoutHdl));
        }


        private static string? _lastCharForSpell;
        private static string? _lastInputTextForSpell;
        private static bool _isCheckWhileTypingWindowShow;


        private static void SpellCheckStart(string? c)
        {
            _isSpellCheckReplacedLast = false;
            if (_lastKeyUp == (int)VirtualKeycodes.Backspace || !string.IsNullOrEmpty(_lastAutoText))
            {
                return;
            }
            if (!CheckActiveProcessFileName.CheckSpellCheckWhileTyping() || _isLastAutoswitch)
            {
                return;
            }

            _lastCharForSpell = c;
            _lastInputTextForSpell = _lastInputText;
            _isCheckWhileTypingWindowShow = true;
            _spellCheckWhileTypingWindow = new SpellCheckWhileTypingWindow();
            _spellCheckWhileTypingWindow.HideAction += HideAction;
            _spellCheckWhileTypingWindow.Show(SpellCheckWhileTyping.Suggestions, GetTextAllLine(_lastInputText));
            SpellCheckWhileTyping.SaveLast(GetTextAllLine(_lastInputText));
            if (SettingsManager.Settings.SpellCheckWhileTypingSoundOn)
            {
                SoundManager.PlayForSpellCheck();
            }
        }

        private static void HideAction()
        {
            _isCheckWhileTypingWindowShow = false;
        }

        private static void SpellCheckReplaceText(string s)
        {
            _isSpellCheckReplacedLast = true;
            IsEnabled = false;
            var sim = new InputSimulator();
            var currentText = GetTextAllLine(_lastInputTextForSpell);
            for (int i = 0; i < currentText.Length; i++)
            {
                sim.Keyboard.KeyPress(VirtualKeyCode.BACK).Sleep(10);
            }
            SendText.SendStringByTextEntry(s + _lastCharForSpell, false);
            IsEnabled = true;
        }


        private static void RemoveLast()
        {
            try
            {
                if (_lastInputText != null && _lastInputText.Length > 0)
                {
                    _lastInputText = _lastInputText.Remove(_lastInputText.Length - 1);
                }
                if (_lastInputTextDiary != null && _lastInputTextDiary.Length > 0)
                {
                    _lastInputTextDiary = _lastInputTextDiary.Remove(_lastInputTextDiary.Length - 1);
                }
                if (_lastInputKeys.Count > 0)
                {
                    _lastInputKeys.RemoveAt(_lastInputKeys.Count - 1);
                }
                foreach (var switcherLayout in KeyboardLayoutCommon.AutoSwitcherLayouts)
                {
                    if (KeyboardLayoutCommon.AutoSwitcherTextByLayouts[switcherLayout.Key]!.Length > 0)
                    {
                        KeyboardLayoutCommon.AutoSwitcherTextByLayouts[switcherLayout.Key] = KeyboardLayoutCommon.AutoSwitcherTextByLayouts[switcherLayout.Key]!.Remove(KeyboardLayoutCommon.AutoSwitcherTextByLayouts[switcherLayout.Key]!.Length - 1);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        private static void AddKeyToCurrentText(GlobalKeyEventArgs key)
        {
            var currentKeyboardLayoutName = KeyboardLayoutMethods.GetCurrentKeyboardLayoutName(_currentLayoutHdl);
            foreach (var switcherLayout in KeyboardLayoutCommon.AutoSwitcherLayouts)
            {
                var keyCode = key.KeyCode;
                //if (currentKeyboardLayoutName == "de" && switcherLayout.Key != "de")
                {
                    keyCode = (VirtualKeycodes)User32.MapVirtualKey((uint)key.HardwareScanCode, User32.MAPVK.MAPVK_VSC_TO_VK);
                }
                GlobalKeyEventArgs eventArgs = new GlobalKeyEventArgs(keyCode, key.HardwareScanCode, key.IsModifierKey, key.Alt, key.Control, key.Shift);
                string? currentText = KeyboardLayoutMethods.CodeToString(eventArgs, switcherLayout.Value);
                if (currentText != null && currentText.Length == 1)
                {
                    KeyboardLayoutCommon.AutoSwitcherTextByLayouts[switcherLayout.Key] += currentText;
                    if (switcherLayout.Key == currentKeyboardLayoutName)
                    {
                        _lastInputKeys.Add((int)eventArgs.KeyCode);
                        _lastInputText += currentText;
                        _lastInputTextDiary += currentText;
                        _inputChar = currentText;

                    }
                }
            }
        }

        private static void BeginNewWord(bool withDiary)
        {
            try
            {
                //Log.AddStartMethod("BeginNewWord");
                if (!IsEnabled)
                {
                    return;
                }
                _isSpellCheckReplacedLast = false;
                _keyboardLayoutChangedManually = false;
                _isAutoSwitch = false;
                _isAutoSwitchDic = false;
                _isRuleAndPunc = false;
                _isDop = false;
                SpellCheckWhileTyping.Suggestions.Clear();
                _lastInputKeys.Clear();
                _isLastAutoswitch = false;
                _lastAutoText = "";
                _inputChar = "";
                _lastSwitchText = "";
                _lastInputTextForSpell = "";
                _lastCharForSpell = "";
                _lastAutoTextForRules = "";
                _langWasAutoSwitched = false;
                _lastAutochangeGo = false;
                _isAutoSwitchingNow = false;

                KeyboardLayoutCommon.AutoSwitcherTextByLayouts = new Dictionary<string, string?>();

                foreach (var switcherLayout in KeyboardLayoutCommon.AutoSwitcherLayouts)
                {
                    KeyboardLayoutCommon.AutoSwitcherTextByLayouts[switcherLayout.Key] = "";
                }
                if (!string.IsNullOrEmpty(_lastInputTextDiary) && withDiary)
                {
                    if (!GlobalLangChangeHook.IsPassword)
                    {
                        GlobalEventsApp.OnEventAddDiary(_lastInputTextDiary);
                    }
                    _lastInputTextDiary = "";
                }
                _lastInputText = "";
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        private static bool IsPrintable(GlobalKeyEventArgs evtData)
        {
            if (!IsLayoutChanging)
            {
                if (Keyboard.IsKeyDown(Key.RWin)) return false;
                if (Keyboard.IsKeyDown(Key.LWin)) return false;
                if (Keyboard.IsKeyDown(Key.RightCtrl)) return false;
                if (Keyboard.IsKeyDown(Key.LeftCtrl)) return false;
                if (Keyboard.IsKeyDown(Key.RightAlt)) return false;
                if (Keyboard.IsKeyDown(Key.LeftAlt)) return false;
                return KeyboardLayoutMethods.IsPrintable(evtData, _currentLayoutHdl);
            }
            return false;
        }

        private static bool IsSwitchLang()
        {
            var switch1 = Keyboard.IsKeyDown(Key.LWin) && Keyboard.IsKeyDown(Key.Space);
            var switch2 = Keyboard.IsKeyDown(Key.RWin) && Keyboard.IsKeyDown(Key.Space);

            var switch3 = Keyboard.IsKeyDown(Key.LeftCtrl) && Keyboard.IsKeyDown(Key.LeftShift);
            var switch4 = Keyboard.IsKeyDown(Key.LeftAlt) && Keyboard.IsKeyDown(Key.LeftShift);

            var switch5 = Keyboard.IsKeyDown(Key.RightCtrl) && Keyboard.IsKeyDown(Key.RightShift);
            var switch6 = Keyboard.IsKeyDown(Key.RightAlt) && Keyboard.IsKeyDown(Key.RightShift);

            var switch7 = Keyboard.IsKeyDown(Key.RightCtrl) && Keyboard.IsKeyDown(Key.LeftShift);
            var switch8 = Keyboard.IsKeyDown(Key.RightAlt) && Keyboard.IsKeyDown(Key.LeftShift);

            var switch9 = Keyboard.IsKeyDown(Key.LeftCtrl) && Keyboard.IsKeyDown(Key.RightShift);
            var switch10 = Keyboard.IsKeyDown(Key.LeftAlt) && Keyboard.IsKeyDown(Key.RightShift);
            var asd = switch1 || switch2 || switch3 || switch4 || switch5 || switch6 || switch7 || switch8 || switch9 || switch10;
            return asd;
        }


        private static bool HaveModifiers(GlobalKeyEventArgs evtData)
        {
            var ctrl = evtData.Control;
            var alt = evtData.Alt;
            var win = Keyboard.IsKeyDown(Key.RWin) || Keyboard.IsKeyDown(Key.LWin);
            return ctrl != ModifierKeySide.None || alt != ModifierKeySide.None || win;
        }

        private static bool HaveTrackingKeys(GlobalKeyEventArgs evtData)
        {
            var vkCode = evtData.KeyCode;

            return
              vkCode == VirtualKeycodes.Control ||
              vkCode == VirtualKeycodes.LeftCtrl ||
              vkCode == VirtualKeycodes.RightCtrl ||
              vkCode == VirtualKeycodes.PrintScreen ||
              vkCode == VirtualKeycodes.Shift ||
              vkCode == VirtualKeycodes.RightShift ||
              vkCode == VirtualKeycodes.LeftShift ||
              vkCode == VirtualKeycodes.Numlock ||
              vkCode == VirtualKeycodes.Insert ||
              vkCode == VirtualKeycodes.Pause ||
              vkCode == VirtualKeycodes.ScrollLock;
        }

        private static string GetTextAllLine(string? text)
        {
            if (string.IsNullOrEmpty(text)) return "";
            try
            {
                if (_countLeftRemove > 0)
                {
                    var index = text.Length - _countLeftRemove;
                    if (index < 0)
                    {
                        return "";
                    }
                    text = text.Remove(text.Length - _countLeftRemove);
                }
                if (string.IsNullOrEmpty(text)) return "";
                string[] split;

                if (text.Last() == '(')
                {
                    split = text.Split(new[] { " ", "\t", "\r", Environment.NewLine }, StringSplitOptions.RemoveEmptyEntries);
                }
                else
                {
                    split = text.Split(new[] { " ", "\t", "\r", "(", Environment.NewLine }, StringSplitOptions.RemoveEmptyEntries);
                }
                if (split.Length > 0)
                {
                    var lastIndex = text.LastIndexOf(split[split.Length - 1], StringComparison.InvariantCulture);
                    if (lastIndex > -1)
                    {
                        return text.Substring(lastIndex);
                    }

                }
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
            var res = text.TrimStart().TrimStart('\r', '\n');
            return res;
        }


        private static void AddAutoSwitchRule(AutoSwitchRuleDataModel? autoSwitchRuleDataModel)
        {
            if (autoSwitchRuleDataModel != null && autoSwitchRuleDataModel.Text != null)
                VMContainer.Instance.AutoSwitcherSettingsViewModel.AddDataModel(autoSwitchRuleDataModel);
        }

        private static void UpdateAutoSwitchRule(AutoSwitchRuleDataModel? autoSwitchRuleDataModel)
        {
            if (autoSwitchRuleDataModel != null && autoSwitchRuleDataModel.Text != null)
                VMContainer.Instance.AutoSwitcherSettingsViewModel.UpdateDataModel(autoSwitchRuleDataModel);
        }

        private static void RemoveAutoSwitchRule(AutoSwitchRuleDataModel? autoSwitchRuleDataModel)
        {
            if (autoSwitchRuleDataModel != null && autoSwitchRuleDataModel.Text != null)
                VMContainer.Instance.AutoSwitcherSettingsViewModel.RemoveDataModel(autoSwitchRuleDataModel);
        }
    }
}
