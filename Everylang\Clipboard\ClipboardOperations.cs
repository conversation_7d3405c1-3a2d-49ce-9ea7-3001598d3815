﻿using Everylang.App.Clipboard.ClipboardWinApi;
using Everylang.App.HookManager;
using Everylang.App.SwitcherLang;
using Everylang.App.Utilities;
using Everylang.App.Utilities.Automation.UIA3;
using Everylang.Common.LogManager;
using Everylang.Common.Utilities;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Windows;
using System.Windows.Threading;
using Vanara.PInvoke;
using WindowsInput;

namespace Everylang.App.Clipboard
{
    internal class ClipboardOperations
    {
        internal static bool IsLastClipContainsRtf { get; set; }

        internal static void SetText(string? text)
        {
            if (string.IsNullOrEmpty(text))
            {
                EmptyClipboard();
                return;
            }

            ClipboardData clipboardData = new ClipboardData(CLIPFORMAT.CF_UNICODETEXT, "",
                TransformToUnicodeClipboardBytes(Encoding.Unicode.GetBytes(text)));
            SetClipboard(new List<ClipboardData>() { clipboardData });
            Thread.Sleep(50);

        }

        internal static void SetTextWithoutHistory(string? strText)
        {
            ClipboardMonitorWorker.IgnoreLast = true;
            SetText(strText);
            ClipboardMonitorWorker.IgnoreLast = false;
        }

        internal static string GetText()
        {
            var clipboardDataList = GetClipboard();
            foreach (var dataClip in clipboardDataList)
            {
                if (dataClip.Format == (ulong)CLIPFORMAT.CF_UNICODETEXT)
                {
                    if (dataClip.Buffer != null)
                    {
                        var text = Encoding.Unicode.GetString(dataClip.Buffer).TrimEnd('\0');
                        return text;
                    }
                }
            }

            return "";
        }


        internal static string GetSelectionText()
        {
            var text = GetTextUiAuto();
            if (!string.IsNullOrEmpty(text))
            {
                return text!;
            }
            ClipboardMonitorWorker.IgnoreLast = true;
            string result = "";
            try
            {
                using var stateManager = new ClipboardStateManager();
                stateManager.Initialize();

                IsLastClipContainsRtf = IsClipboardContainsRtf();
                EmptyClipboard();
                SendCopyCommand();
                result = GetText();
                if (string.IsNullOrEmpty(result))
                {
                    SendCopyText();
                    result = GetText();
                    if (string.IsNullOrEmpty(result))
                    {
                        Application.Current.Dispatcher.Invoke(DispatcherPriority.Background,
                            new Action(delegate { }));
                        result = ClipboardOperations.GetText();
                    }
                }
            }
            catch
            {
                // ignored
            }
            return result;
        }


        private static void SendCopyCommand()
        {
            const uint wmCopy = 0x0301;
            User32.SendMessage(ForegroundWindow.GetForeground(), wmCopy);
            Application.Current.Dispatcher.Invoke(DispatcherPriority.Background,
                new Action(delegate { }));
        }

        private static string? GetTextUiAuto()
        {
            try
            {
                var targetControl = AutomationUi.Uia3Automation.FromHandle(ForegroundWindow.GetForeground());

                if (targetControl == null)
                    return null;

                if (targetControl.Patterns.Text.TryGetPattern(out var textPattern))
                {
                    var sb = new StringBuilder();

                    foreach (var r in textPattern.GetSelection())
                    {
                        sb.AppendLine(r.GetText(-1));
                    }
                    return sb.ToString().TrimEnd('\r', '\n');
                }
            }
            catch (COMException)
            {
            }
            catch (Win32Exception)
            {
            }
            catch
            {
                // ignored
            }
            return null;
        }

        internal static void SendCopyText()
        {
            CommonHookListener.IsKeyEnabled = false;
            var sim = new InputSimulator();
            KeyboardState.ReleaseAllKeys();
            sim.Keyboard.KeyDown(VirtualKeyCode.LCONTROL);
            sim.Keyboard.KeyPress(VirtualKeyCode.VK_C);
            sim.Keyboard.KeyUp(VirtualKeyCode.LCONTROL);
            Thread.Sleep(50);
            KeyboardState.ReleaseAllKeys();
            CommonHookListener.IsKeyEnabled = true;
        }

        internal static void SendPasteText()
        {
            CommonHookListener.IsKeyEnabled = false;
            var sim = new InputSimulator();
            KeyboardState.ReleaseAllKeys();
            sim.Keyboard.KeyDown(VirtualKeyCode.LCONTROL);
            sim.Keyboard.KeyPress(VirtualKeyCode.VK_V);
            sim.Keyboard.KeyUp(VirtualKeyCode.LCONTROL);
            Thread.Sleep(50);
            KeyboardState.ReleaseAllKeys();
            CommonHookListener.IsKeyEnabled = true;
        }

        private static ClipboardData[]? _dataClipBackup;

        internal static void ClipboardSave()
        {
            _dataClipBackup = null;
            if (System.Windows.Clipboard.ContainsImage())
            {
                _dataClipBackup = GetClipboardImages();
            }
            else
            {
                _dataClipBackup = GetClipboard();
            }
        }

        internal static void ClipboardRestore()
        {
            if (_dataClipBackup != null)
            {
                ClipboardMonitorWorker.IgnoreLast = true;
                SetClipboard((IEnumerable<ClipboardData>)_dataClipBackup.Clone());
            }
            _dataClipBackup = null;
        }

        internal static bool IsClipboardContainsRtf()
        {
            bool result = false;
            DispatcherHelper.RunAsStaThread(() =>
            {
                try
                {
                    using var clipboard = new ClipboardApi();
                    result = clipboard.IsClipboardContainsRtf();
                }
                catch (COMException)
                {
                }
                catch (Win32Exception)
                {
                }
                catch
                {
                    // ignored
                }
            });
            return result;
        }

        internal static void EmptyClipboard()
        {
            DispatcherHelper.RunAsStaThread(() =>
            {
                try
                {
                    using var clipboard = new ClipboardApi();
                    clipboard.EmptyClipboard();
                }
                catch (COMException)
                {
                }
                catch (Win32Exception)
                {
                }
                catch
                {
                    // ignored
                }
            });
        }

        internal static void CloseClipboard()
        {
            DispatcherHelper.RunAsStaThread(() =>
            {
                try
                {
                    using var clipboard = new ClipboardApi();
                    clipboard.CloseClipboard();
                }
                catch (COMException)
                {
                }
                catch (Win32Exception)
                {
                }
                catch
                {
                    // ignored
                }
            });
        }


        #region private methods

        private static byte[]? TransformToUnicodeClipboardBytes(byte[]? textBytes)
        {
            if (textBytes != null)
            {
                var withZeroBytes = new byte[textBytes.Length + 2];
                Array.Copy(textBytes, withZeroBytes, textBytes.Length);
                withZeroBytes[textBytes.Length] = 0;
                withZeroBytes[textBytes.Length + 1] = 0;
                return withZeroBytes;
            }

            return null;
        }

        private static void SetClipboard(IEnumerable<ClipboardData> clipData)
        {
            DispatcherHelper.RunAsStaThread(() =>
            {
                try
                {
                    using var clipboard = new ClipboardApi();
                    clipboard.SetClipboard(clipData);
                }
                catch (COMException e)
                {
                    Logger.LogTo.Debug(e, e.Message);
                }
                catch (Win32Exception e)
                {
                    Logger.LogTo.Debug(e, e.Message);
                }
                catch
                {
                    // ignored
                }
            });
        }

        private static ClipboardData[] GetClipboard()
        {
            var resultBlockingCollection = new BlockingCollection<ClipboardData>();
            DispatcherHelper.RunAsStaThread(() =>
            {
                try
                {
                    using var clipboard = new ClipboardApi();
                    resultBlockingCollection = clipboard.GetClipboard();
                }
                catch (COMException)
                {
                }
                catch (Win32Exception)
                {
                }
                catch
                {
                    // ignored
                }
            });
            return resultBlockingCollection.ToArray();
        }

        private static ClipboardData[] GetClipboardImages()
        {
            var resultBlockingCollection = new BlockingCollection<ClipboardData>();
            DispatcherHelper.RunAsStaThread(() =>
            {
                try
                {
                    using var clipboard = new ClipboardApi();
                    resultBlockingCollection = clipboard.GetClipboardImages();
                }
                catch (COMException)
                {
                }
                catch (Win32Exception)
                {
                }
                catch
                {
                    // ignored
                }
            });
            return resultBlockingCollection.ToArray();
        }

        #endregion

    }
}
