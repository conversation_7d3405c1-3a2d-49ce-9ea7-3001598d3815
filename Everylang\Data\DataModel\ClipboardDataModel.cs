﻿using Everylang.App.Data.DataStore;
using LiteDB;
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Windows.Media.Imaging;

namespace Everylang.App.Data.DataModel
{
    public class ClipboardDataModel
    {
        private string? _text;
        private string? _rtf;
        private string? _html;

        internal ClipboardDataModel()
        {
            Files = "";
            ImageId = "";
        }
        internal ObjectId Id { get; set; }

        public string? Text
        {
            get
            {
                if (_text == null)
                {
                    if (Id != null) _text = ClipboadManager.GetText(Id);
                    else _text = "";
                }
                return _text;
            }
            set => _text = value;
        }

        public string? TextPrev => Text.Length > 2000 ? Text.Substring(0, 2000) + "......" : Text;

        public string? Rtf
        {
            get
            {
                if (_rtf == null)
                {
                    if (Id != null) _rtf = ClipboadManager.GetRtf(Id);
                    else _rtf = "";
                }
                return _rtf;
            }
            set => _rtf = value;
        }

        public string? Html
        {
            get
            {
                if (_html == null)
                {
                    if (Id != null) _html = ClipboadManager.GetHtml(Id);
                    else _html = "";
                }
                return _html;
            }
            set => _html = value;
        }

        internal string? Files { get; set; }
        internal string? ImageId { get; set; }
        public string? ShortText { get; set; }
        public string? Application { get; set; }
        public string DateText
        {
            get { return DateTime.ToString("D"); }
        }
        public DateTime DateTimeDate
        {
            get { return new DateTime(DateTime.Year, DateTime.Month, DateTime.Day); }
        }
        public DateTime DateTime { get; set; }
        internal bool IsHtml
        {
            get { return !string.IsNullOrEmpty(Html) && string.IsNullOrEmpty(Rtf); }
        }

        internal bool IsRtf
        {
            get { return !string.IsNullOrEmpty(Rtf); }
        }

        internal bool IsText
        {
            get { return !string.IsNullOrEmpty(Text) && string.IsNullOrEmpty(Html) && string.IsNullOrEmpty(Rtf) && string.IsNullOrEmpty(Files) && string.IsNullOrEmpty(ImageId); }
        }
        internal bool IsFile
        {
            get { return !string.IsNullOrEmpty(Files); }
        }

        internal bool IsImage
        {
            get { return !string.IsNullOrEmpty(ImageId); }
        }

        public string Format
        {
            get
            {
                if (IsText)
                {
                    return "Text";
                }
                if (IsRtf)
                {
                    return "Rtf";
                }
                if (IsHtml)
                {
                    return "Html";
                }
                if (IsFile)
                {
                    return "File";
                }
                if (IsImage)
                {
                    return "Image";
                }
                return "";
            }
        }

        public BitmapImage? ImagePrev
        {
            get
            {
                if (!IsImage)
                {
                    return null;
                }

                try
                {
                    var imStream = ClipboadManager.GetImage(ImageId);
                    if (imStream != null)
                    {
                        using Image img = Image.FromStream(imStream);
                        double imgHeight = img.Size.Height;
                        double imgWidth = img.Size.Width;

                        double x = imgWidth / 200;
                        int newWidth = Convert.ToInt32(imgWidth / x);
                        int newHeight = Convert.ToInt32(imgHeight / x);

                        using Image myThumbnail =
                            img.GetThumbnailImage(newWidth, newHeight, null, IntPtr.Zero);
                        using var memory = new MemoryStream();
                        myThumbnail.Save(memory, ImageFormat.Png);
                        memory.Position = 0;
                        var bitmapImage = new BitmapImage();
                        bitmapImage.BeginInit();
                        bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                        bitmapImage.StreamSource = memory;
                        bitmapImage.EndInit();
                        bitmapImage.Freeze();
                        return bitmapImage;
                    }
                }
                catch
                {
                    // ignore
                }

                return null;
            }
        }

        internal Stream? GetImage()
        {
            return ClipboadManager.GetImage(ImageId);
        }
    }
}
