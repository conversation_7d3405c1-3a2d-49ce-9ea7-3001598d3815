﻿using Everylang.App.Clipboard;
using Everylang.App.Data.DataModel;
using Everylang.App.Data.DataStore;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.SettingsApp;
using Everylang.App.SwitcherLang;
using Everylang.App.Utilities;
using Everylang.App.ViewModels;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using Telerik.Windows;
using Telerik.Windows.Controls;

namespace Everylang.App.View.Controls.Common.CommonWindow
{
    /// <summary>
    /// Interaction logic for FastActionSnippets.xaml
    /// </summary>
    internal partial class FastActionSnippets : IFastActionComponent
    {
        private ICollectionView? _collectionView;
        private string? _selectedText;
        private int _currentSelected;

        internal FastActionSnippets()
        {
            InitializeComponent();
            DataBindTags();
            if (FastActionCommonWindow.Instance != null)
                FastActionCommonWindow.Instance.GlobalKeyEvent += GlobalKeyEvent;
        }

        private void DataBindTags()
        {
            var tagList = new Dictionary<string, int>();
            foreach (var autochangeDataModel in VMContainer.Instance.SnippetsViewModel.SnippetsList)
            {
                if (autochangeDataModel?.Tags != null)
                {
                    var tags = autochangeDataModel.Tags.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                    foreach (var tag in tags)
                    {
                        if (!tagList.ContainsKey(tag))
                        {
                            tagList.Add(tag, autochangeDataModel.CountUsage);
                        }
                        else
                        {
                            tagList[tag] += autochangeDataModel.CountUsage;
                        }
                    }
                }
            }
            var ordered = tagList.OrderByDescending(x => x.Value).ToDictionary(x => x.Key, x => x.Value);
            List<string> keyList = new List<string>(ordered.Keys);
            keyList.Insert(0, LocalizationManager.GetString("All"));
            LvTags.ItemsSource = keyList;
            LvTags.SelectedIndex = 0;
        }

        private void DataBindSnippets()
        {

            var snippetList = new List<SnippetsDataModel>();
            var selectedItems = LvTags.SelectedItems.Cast<string>().ToList();
            if (LvTags.SelectedIndex == 0)
            {
                var autochangeDataModels = VMContainer.Instance.SnippetsViewModel.SnippetsList.OrderByDescending(x => x?.CountUsage).ToList();
                LvSnippets.ItemsSource = autochangeDataModels;
                _collectionView = CollectionViewSource.GetDefaultView(autochangeDataModels);
            }
            else
            {
                foreach (var selectedItem in selectedItems)
                {
                    foreach (var snippet in VMContainer.Instance.SnippetsViewModel.SnippetsList)
                    {
                        if (!string.IsNullOrEmpty(snippet?.Tags) && (snippet.Tags.Contains(selectedItem) && !snippetList.Contains(snippet)))
                        {
                            snippetList.Add(snippet);
                        }
                    }
                }
                var autochangeDataModels = snippetList.OrderByDescending(x => x.CountUsage).ToList();
                LvSnippets.ItemsSource = autochangeDataModels;
                _collectionView = CollectionViewSource.GetDefaultView(autochangeDataModels);
            }

            Init();
        }

        private void LvSnippets_OnPreviewMouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            Replace();
        }

        private void TextBlockSelectionChanged(string selectedtext)
        {
            _selectedText = selectedtext;
        }

        public void Replace()
        {
            if (LvSnippets.SelectedItems != null && LvSnippets.SelectedItems.Count > 0)
            {
                string replacedText = "";
                string langName = "";
                for (int i = 0; i < LvSnippets.SelectedItems.Count; i++)
                {
                    SnippetsDataModel? dataModel = ((LvSnippets.SelectedItems[i] as SnippetsDataModel));
                    if (SettingsManager.Settings.SnippetsIsEnabledCountUsage)
                    {
                        if (dataModel != null) dataModel.CountUsage++;
                    }

                    if (dataModel != null)
                    {
                        SnippetsManager.UpdateData(dataModel);
                        replacedText += dataModel.Text;
                        if (i != LvSnippets.SelectedItems.Count - 1)
                        {
                            replacedText += Environment.NewLine;
                        }

                        langName = dataModel.LangToSwitch;
                    }
                }
                FastActionCommonWindow.Instance?.Hide();
                SendText.SendStringByPaste(replacedText, false);
                if (!string.IsNullOrEmpty(langName))
                {
                    var langCode = KeyboardLayoutCommon.LangCodeList[KeyboardLayoutCommon.AutoSwitcherLayouts[langName.ToLower()]];
                    KeyboardLayoutSwitcher.SwitchLayoutToLang(langCode);
                }
            }
        }

        public void Replace(int i)
        {
            SnippetsDataModel dataModel = (SnippetsDataModel)LvSnippets.Items[i];
            string langName = dataModel.LangToSwitch;
            FastActionCommonWindow.Instance?.Hide();
            SendText.SendStringByPaste(dataModel.Text, false);
            if (!string.IsNullOrEmpty(langName))
            {
                var langCode = KeyboardLayoutCommon.LangCodeList[KeyboardLayoutCommon.AutoSwitcherLayouts[langName.ToLower()]];
                KeyboardLayoutSwitcher.SwitchLayoutToLang(langCode);
            }
        }

        public void Copy()
        {
            if (LvSnippets.SelectedItems.Count > 0)
            {
                if (_selectedText != null && !string.IsNullOrEmpty(_selectedText))
                {
                    ClipboardOperations.SetTextWithoutHistory(_selectedText);
                    return;
                }
                var copyText = "";
                for (int i = 0; i < LvSnippets.SelectedItems.Count; i++)
                {
                    SnippetsDataModel? dataModel = (LvSnippets.SelectedItems[i] as SnippetsDataModel);
                    if (SettingsManager.Settings.SnippetsIsEnabledCountUsage)
                    {
                        if (dataModel != null) dataModel.CountUsage++;
                    }

                    if (dataModel != null)
                    {
                        SnippetsManager.UpdateData(dataModel);
                        copyText += dataModel.Text;
                    }

                    if (i != LvSnippets.SelectedItems.Count - 1)
                    {
                        copyText += Environment.NewLine;
                    }
                }
                ClipboardOperations.SetTextWithoutHistory(copyText);
            }
        }

        public void SelectAll()
        {

        }

        public void Delete()
        {

        }

        public void Init()
        {
            LvSnippets.Focus();
            if (LvSnippets.Items.Count > 0) LvSnippets.SelectedItem = LvSnippets.Items[0];
            _currentSelected = 0;
        }

        private void GlobalKeyEvent(GlobalKeyEventArgs e)
        {
            if (e.KeyCode == VirtualKeycodes.Tab && e.Shift != ModifierKeySide.None)
            {
                if (LvTags.SelectedIndex == LvTags.Items.Count - 1)
                {
                    LvTags.SelectedIndex = 0;
                }
                else
                {
                    LvTags.SelectedIndex += 1;
                }
            }
        }

        public void Close()
        {
            LvTags.ItemsSource = null;
            LvSnippets.ItemsSource = null;
            if (_collectionView != null) _collectionView.Filter = null;
            if (FastActionCommonWindow.Instance != null)
                FastActionCommonWindow.Instance.GlobalKeyEvent -= GlobalKeyEvent;
        }

        public void SelectNext(bool shift)
        {
            var oldSelIndex = _currentSelected;
            if (_currentSelected == LvSnippets.Items.Count - 1)
            {
                _currentSelected = 0;
            }
            else
            {
                _currentSelected += 1;
            }
            if (shift)
            {
                var clipItem = LvSnippets.Items[_currentSelected];
                if (!LvSnippets.SelectedItems.Contains(clipItem))
                {
                    LvSnippets.SelectedItems.Add(clipItem);
                    LvSnippets.ScrollIntoView(clipItem);
                }
                else
                {
                    if (oldSelIndex != -1)
                    {
                        LvSnippets.SelectedItems.Remove(LvSnippets.Items[oldSelIndex]);
                        LvSnippets.ScrollIntoView(LvSnippets.Items[oldSelIndex]);
                    }
                }
            }
            else
            {
                if (_currentSelected < -1)
                {
                    _currentSelected = -1;
                }
                LvSnippets.SelectedIndex = _currentSelected;
                LvSnippets.ScrollIntoView(_currentSelected);
            }
        }

        public void SelectPrev(bool shift)
        {
            var oldSelIndex = _currentSelected;
            if (_currentSelected == 0)
            {
                _currentSelected = LvSnippets.Items.Count - 1;
            }
            else
            {
                _currentSelected -= 1;
            }
            if (shift)
            {
                var clipItem = LvSnippets.Items[_currentSelected];
                if (!LvSnippets.SelectedItems.Contains(clipItem))
                {
                    LvSnippets.SelectedItems.Add(clipItem);
                    LvSnippets.ScrollIntoView(clipItem);
                }
                else
                {
                    if (oldSelIndex != -1)
                    {
                        LvSnippets.SelectedItems.Remove(LvSnippets.Items[oldSelIndex]);
                        LvSnippets.ScrollIntoView(LvSnippets.Items[oldSelIndex]);
                    }
                }
            }
            else
            {
                if (_currentSelected < -1)
                {
                    _currentSelected = -1;
                }
                LvSnippets.SelectedIndex = _currentSelected;
                LvSnippets.ScrollIntoView(_currentSelected);
            }
        }

        public void FindText(string text)
        {
            if (_collectionView != null)
            {
                _collectionView.Filter = null;

                _collectionView.Filter = item =>
                {
                    var s = (item as SnippetsDataModel)?.Text;
                    return s != null && s.ToLower().Contains(text.ToLower());
                };
            }

            if (LvSnippets.Items.Count > 0)
            {
                LvSnippets.SelectedItem = LvSnippets.Items[0];
            }
            _currentSelected = 0;
        }

        private void LvTags_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            DataBindSnippets();
        }

        private RadContextMenu? _contextMenu;

        private void OpenMenuMouseDown(object sender, MouseButtonEventArgs e)
        {
            if (LvSnippets.SelectedItem == null)
            {
                return;
            }
            _contextMenu = new RadContextMenu();
            _contextMenu.Closed += (_, _) =>
            {
                if (FastActionCommonWindow.Instance != null) FastActionCommonWindow.Instance.MenuIsOpen = false;
            };

            RadMenuItem itemPastButton = new RadMenuItem();
            itemPastButton.Header = LocalizationManager.GetString("PastButton");
            itemPastButton.PreviewMouseDown += RadMenuItemPaste;
            _contextMenu.Items.Add(itemPastButton);

            RadMenuItem itemCopy = new RadMenuItem();
            itemCopy.Header = LocalizationManager.GetString("CopyButton");
            itemCopy.Click += RadMenuItemCopyClick;
            _contextMenu.Items.Add(itemCopy);

            _contextMenu.PlacementTarget = (UIElement)sender;
            if (FastActionCommonWindow.Instance != null) FastActionCommonWindow.Instance.MenuIsOpen = true;
            _contextMenu.IsOpen = true;
        }

        private void RadMenuItemPaste(object sender, MouseButtonEventArgs mouseButtonEventArgs)
        {
            Replace();
        }

        private void RadMenuItemCopyClick(object sender, RadRoutedEventArgs radRoutedEventArgs)
        {
            Copy();
        }
    }
}
