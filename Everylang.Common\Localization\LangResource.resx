﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="About" xml:space="preserve">
    <value>About the program</value>
  </data>
  <data name="AvailableNewVersion" xml:space="preserve">
    <value>New version available, please restart the application</value>
  </data>
  <data name="ClearButton" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="FirstStartWithEvaluate" xml:space="preserve">
    <value>Trial period activated for 40 days, all features included</value>
  </data>
  <data name="CloseHeaderButton" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="CopyButton" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="CopyButtonHtml" xml:space="preserve">
    <value>Copy HTML</value>
  </data>
  <data name="CopyButtonRtf" xml:space="preserve">
    <value>Copy Rtf</value>
  </data>
  <data name="FastActionIndex" xml:space="preserve">
    <value>Click on the number to insert text</value>
  </data>
  <data name="BreakInterButton" xml:space="preserve">
    <value>Split text at newline character</value>
  </data>
  <data name="BreakSpaceButton" xml:space="preserve">
    <value>Split text by space</value>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>Exit</value>
  </data>
  <data name="FixWindow" xml:space="preserve">
    <value>Freeze window</value>
  </data>
  <data name="FromLang" xml:space="preserve">
    <value>From the language:</value>
  </data>
  <data name="ShowHistoryButton" xml:space="preserve">
    <value>Show history</value>
  </data>
  <data name="HideHeaderButton" xml:space="preserve">
    <value>Collapse</value>
  </data>
  <data name="MaxHeaderButton" xml:space="preserve">
    <value>Expand</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="PastButton" xml:space="preserve">
    <value>Insert</value>
  </data>
  <data name="GoToMainWindowButton" xml:space="preserve">
    <value>Open the main window with translation</value>
  </data>
  <data name="SiteSourceTextButton" xml:space="preserve">
    <value>Open the translation service website</value>
  </data>
  <data name="OpenMainWindowShortcut" xml:space="preserve">
    <value>Keyboard shortcut to open the main window</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="ListOnMainWindow" xml:space="preserve">
    <value>Open list in main window</value>
  </data>
  <data name="StopWorkingShortcut" xml:space="preserve">
    <value>Keyboard shortcut to disable all features</value>
  </data>
  <data name="WorkingOff" xml:space="preserve">
    <value>Disable the program</value>
  </data>
  <data name="WorkingOn" xml:space="preserve">
    <value>Enable program</value>
  </data>
  <data name="WorkingOffTitle" xml:space="preserve">
    <value>The program is disabled</value>
  </data>
  <data name="StayOnTopButton" xml:space="preserve">
    <value>Don't close</value>
  </data>
  <data name="LatinButton" xml:space="preserve">
    <value>In Latin</value>
  </data>
  <data name="ToLang" xml:space="preserve">
    <value>Tongue:</value>
  </data>
  <data name="TranslateButton" xml:space="preserve">
    <value>Translate</value>
  </data>
  <data name="TranslateError" xml:space="preserve">
    <value>An error occurred during the translation process, please try again or select another translation service</value>
  </data>
  <data name="SoundButton" xml:space="preserve">
    <value>Listen</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="SettingHeaderButton" xml:space="preserve">
    <value>SETTINGS</value>
  </data>
  <data name="HistoryHeaderButton" xml:space="preserve">
    <value>STORY</value>
  </data>
  <data name="ClipboardHeaderButton" xml:space="preserve">
    <value>CLIPBOARD</value>
  </data>
  <data name="DiaryHeaderButton" xml:space="preserve">
    <value>DIARY</value>
  </data>
  <data name="SnippetsHeaderButton" xml:space="preserve">
    <value>SNIPPETS</value>
  </data>
  <data name="ProHeaderButton" xml:space="preserve">
    <value>Activate PRO</value>
  </data>
  <data name="HistoryMenuItem" xml:space="preserve">
    <value>Story</value>
  </data>
  <data name="ClipboardMenuItem" xml:space="preserve">
    <value>Clipboard</value>
  </data>
  <data name="HotkeysMenuItem" xml:space="preserve">
    <value>Hotkeys</value>
  </data>
  <data name="DiaryMenuItem" xml:space="preserve">
    <value>Diary</value>
  </data>
  <data name="ErrorHotkey" xml:space="preserve">
    <value>Cannot register key combination</value>
  </data>
  <data name="UpdateAvailable" xml:space="preserve">
    <value>Update available, restart the application</value>
  </data>
  <data name="MinimizedText" xml:space="preserve">
    <value>The application is running and minimized</value>
  </data>
  <data name="AppUpdated" xml:space="preserve">
    <value>The application has been updated, the list of changes is on the website</value>
  </data>
  <data name="AppNotAsAdmin" xml:space="preserve">
    <value>For the program to work correctly with all applications, you must run it as an administrator</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="ErrorText" xml:space="preserve">
    <value>An error has occurred and the application will be terminated. Please send text with <NAME_EMAIL></value>
  </data>
  <data name="InputLanguagesError" xml:space="preserve">
    <value>Your computer has more than one keyboard layout installed for some languages, which may adversely affect the correct operation of the layout switching feature.</value>
  </data>
  <data name="SetFont" xml:space="preserve">
    <value>Font</value>
  </data>
  <data name="SearchText" xml:space="preserve">
    <value>Find...</value>
  </data>
  <data name="OnlyPro" xml:space="preserve">
    <value>(PRO version)</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Change</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>Create</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="AllApp" xml:space="preserve">
    <value>All programs</value>
  </data>
  <data name="OnlyInProVersion" xml:space="preserve">
    <value>Only in PRO version</value>
  </data>
  <data name="SystemTrayHide" xml:space="preserve">
    <value>The program is minimized to tray</value>
  </data>
  <data name="StartWindowTitle" xml:space="preserve">
    <value>Before using the program, read the documentation</value>
  </data>
  <data name="FastActionTextWindowSearch" xml:space="preserve">
    <value>Tab - Search.  Esc - Cancel and clear</value>
  </data>
  <data name="noun" xml:space="preserve">
    <value>Noun</value>
  </data>
  <data name="pronoun" xml:space="preserve">
    <value>Pronoun</value>
  </data>
  <data name="adjective" xml:space="preserve">
    <value>Adjective</value>
  </data>
  <data name="verb" xml:space="preserve">
    <value>Verb</value>
  </data>
  <data name="adverb" xml:space="preserve">
    <value>Adverb</value>
  </data>
  <data name="preposition" xml:space="preserve">
    <value>Pretext</value>
  </data>
  <data name="conjunction" xml:space="preserve">
    <value>Union</value>
  </data>
  <data name="interjection" xml:space="preserve">
    <value>Interjection</value>
  </data>
  <data name="participle" xml:space="preserve">
    <value>Communion</value>
  </data>
  <data name="auxiliary verb" xml:space="preserve">
    <value>Auxiliary verb</value>
  </data>
  <data name="parenthetic" xml:space="preserve">
    <value>Introductory word</value>
  </data>
  <data name="SpellCheckHeader" xml:space="preserve">
    <value>Spell check</value>
  </data>
  <data name="LabelNoErrors" xml:space="preserve">
    <value>No errors</value>
  </data>
  <data name="LangNotCorrect" xml:space="preserve">
    <value>Language not supported</value>
  </data>
  <data name="LabelError" xml:space="preserve">
    <value>An error has occurred</value>
  </data>
  <data name="LabelTextTooLong" xml:space="preserve">
    <value>Text is too long</value>
  </data>
  <data name="ButtonClose" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="LabelOptions" xml:space="preserve">
    <value>Options:</value>
  </data>
  <data name="LabelOptionsNo" xml:space="preserve">
    <value>No options</value>
  </data>
  <data name="LabelOptionsEnd" xml:space="preserve">
    <value>Verification completed</value>
  </data>
  <data name="bSkip" xml:space="preserve">
    <value>Skip</value>
  </data>
  <data name="bSkipAll" xml:space="preserve">
    <value>Skip all</value>
  </data>
  <data name="bReplace" xml:space="preserve">
    <value>Replace</value>
  </data>
  <data name="bReplaceAll" xml:space="preserve">
    <value>Replace all</value>
  </data>
  <data name="bReplaceText" xml:space="preserve">
    <value>Insert text</value>
  </data>
  <data name="bCopy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="buttonBack" xml:space="preserve">
    <value>Return</value>
  </data>
  <data name="SetDefaultSetting" xml:space="preserve">
    <value>Reset settings</value>
  </data>
  <data name="InterKeyboardShortcuts" xml:space="preserve">
    <value>Press the key combination</value>
  </data>
  <data name="Russian" xml:space="preserve">
    <value>Russian</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>French</value>
  </data>
  <data name="Italian" xml:space="preserve">
    <value>Italian</value>
  </data>
  <data name="Ukrainian" xml:space="preserve">
    <value>Ukrainian</value>
  </data>
  <data name="AboutSettingsHeader" xml:space="preserve">
    <value>ABOUT THE PROGRAM</value>
  </data>
  <data name="AboutSettingsLicense" xml:space="preserve">
    <value>License Agreement</value>
  </data>
  <data name="AboutSettingsDesc" xml:space="preserve">
    <value>A universal assistant for working with text in different languages</value>
  </data>
  <data name="AboutSettingsVersion" xml:space="preserve">
    <value>Version:</value>
  </data>
  <data name="AboutSettingsUpdateAvailable" xml:space="preserve">
    <value>New version of the program is available</value>
  </data>
  <data name="AboutSettingsUpdate" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="AboutSettingsContactHeader" xml:space="preserve">
    <value>FEEDBACK</value>
  </data>
  <data name="AboutSettingsContactText" xml:space="preserve">
    <value>Write your comments and/or questions</value>
  </data>
  <data name="AboutResetSettings" xml:space="preserve">
    <value>Reset all program settings</value>
  </data>
  <data name="AboutOpenStartWindow" xml:space="preserve">
    <value>Open welcome window</value>
  </data>
  <data name="AboutResetSettingsQuestion" xml:space="preserve">
    <value>Reset all settings and program data?</value>
  </data>
  <data name="AboutSettingsContactLink" xml:space="preserve">
    <value>Contact form</value>
  </data>
  <data name="AboutSettingsIsAdmin" xml:space="preserve">
    <value>The program is running as administrator</value>
  </data>
  <data name="AboutSettingsIsNotAdmin" xml:space="preserve">
    <value>The program is not running as administrator</value>
  </data>
  <data name="AboutSettingsAckowledgementsHeader" xml:space="preserve">
    <value>COMPONENTS</value>
  </data>
  <data name="SwitcherSettingsHeader" xml:space="preserve">
    <value>Changing the layout</value>
  </data>
  <data name="SwitcherSettingsKeyboardShortcutsSwitch" xml:space="preserve">
    <value>Switch last word layout</value>
  </data>
  <data name="SwitcherSettingsIsUseBreak" xml:space="preserve">
    <value>Use Break</value>
  </data>
  <data name="SwitcherSettingsKeyboardShortcutsSwitchSelected" xml:space="preserve">
    <value>Change the layout of selected text</value>
  </data>
  <data name="SwitcherSettingsMethodSelect" xml:space="preserve">
    <value>Selecting a layout switching method</value>
  </data>
  <data name="SwitcherSettingsSwitchMethod1" xml:space="preserve">
    <value>Emulation of keys for switching layouts</value>
  </data>
  <data name="SwitcherSettingsSwitchMethod2" xml:space="preserve">
    <value>Running a Windows command</value>
  </data>
  <data name="SwitcherSettingsLeaveTextSelectedAfterSwitch" xml:space="preserve">
    <value>Keep text selected after switching layouts</value>
  </data>
  <data name="SwitcherSettingsIsOn" xml:space="preserve">
    <value>Layout switching enabled</value>
  </data>
  <data name="SwitcherSettingsSwitcherCtrlNumberIsOn" xml:space="preserve">
    <value>Use Ctrl+(number) to switch to a specific language</value>
  </data>
  <data name="SwitcherSettingsIsUseShift" xml:space="preserve">
    <value>Use double-click Shift key</value>
  </data>
  <data name="SwitcherSettingsIsOnInsert" xml:space="preserve">
    <value>Switch from beginning of line</value>
  </data>
  <data name="SwitcherSettingsIsUseScrollLock" xml:space="preserve">
    <value>Use double-click ScrollLock button</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOn" xml:space="preserve">
    <value>Switch layout by button</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnStandart" xml:space="preserve">
    <value>System Settings</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRCtrl" xml:space="preserve">
    <value>Right Ctrl</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLCtrl" xml:space="preserve">
    <value>Left Ctrl</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRShift" xml:space="preserve">
    <value>Right Shift</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLShift" xml:space="preserve">
    <value>Left Shift</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLRCtrl" xml:space="preserve">
    <value>Right or left Ctrl</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLRShift" xml:space="preserve">
    <value>Right or left Shift</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRCtrlOrCapsLock" xml:space="preserve">
    <value>Right Ctrl or CapsLock</value>
  </data>
  <data name="SwitcherSettingsToolTipForCurrentSwitchOnKey" xml:space="preserve">
    <value>To enable or disable the CapsLock function, press the right and left Shift simultaneously</value>
  </data>
  <data name="SwitcherSettingsSwitcherSountIsOn" xml:space="preserve">
    <value>Layout switching sound</value>
  </data>
  <data name="SwitcherSettingsSoundEdit" xml:space="preserve">
    <value>Setting the sound for switching layouts</value>
  </data>
  <data name="SwitcherSettingsTrueListOfLang" xml:space="preserve">
    <value>List of languages ​​to which the layout will switch</value>
  </data>
  <data name="SwitcherLangAndKeysForSwitch" xml:space="preserve">
    <value>Setting the keys to switch to a specific language</value>
  </data>
  <data name="SwitcherSettingsAskToDeactivateAutoswitcherOff" xml:space="preserve">
    <value>Disable auto layout switching?</value>
  </data>
  <data name="SwitcherSettingsAskToDeactivateAutoswitcherOn" xml:space="preserve">
    <value>Enable auto layout switching?</value>
  </data>
  <data name="AutoSwitcherSettingsHeader" xml:space="preserve">
    <value>Automatic layout switching</value>
  </data>
  <data name="SwitcherSettingsHeaderAuto" xml:space="preserve">
    <value>Auto switch enabled</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnTwoUpperCaseLetters" xml:space="preserve">
    <value>Correct two capital letters at the beginning of a word</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnFixWrongUpperCase" xml:space="preserve">
    <value>Fix accidental pressing of CapsLock</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnUpperCaseNotSwitch" xml:space="preserve">
    <value>Do not switch if all letters in a word are capitalized</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnByEnter" xml:space="preserve">
    <value>Correct the layout after pressing the Enter key</value>
  </data>
  <data name="AutoSwitcherSettingsIsSwitchOneLetter" xml:space="preserve">
    <value>Add one-letter words to rules</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnAddingRule" xml:space="preserve">
    <value>Automatically add switching rules</value>
  </data>
  <data name="AutoSwitcherSettingsDisableAutoSwitchAfterManualSwitch" xml:space="preserve">
    <value>Do not correct the layout if it was previously changed manually</value>
  </data>
  <data name="AutoSwitcherSettingsOnlyAfterSeparator" xml:space="preserve">
    <value>Switch layout only after entering a whole word</value>
  </data>
  <data name="AutoSwitcherSettingsAfterPause" xml:space="preserve">
    <value>Switch layout after stopping typing</value>
  </data>
  <data name="AutoSwitcherSettingsResetRule" xml:space="preserve">
    <value>Delete all auto-switch rules</value>
  </data>
  <data name="AutoSwitcherSettingsCombination" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="AutoSwitcherSettingsAllLayouts" xml:space="preserve">
    <value>All layouts</value>
  </data>
  <data name="AutoSwitcherSettingsAction" xml:space="preserve">
    <value>Action</value>
  </data>
  <data name="AutoSwitcherSettingsShowAcceptWindow" xml:space="preserve">
    <value>Add rules only after confirmation</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionConvert" xml:space="preserve">
    <value>Switch</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionNotConvert" xml:space="preserve">
    <value>Don't switch</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionIntermediate" xml:space="preserve">
    <value>Candidate</value>
  </data>
  <data name="AutoSwitcherSettingsHelpWindowTitle" xml:space="preserve">
    <value>Auto switch rules</value>
  </data>
  <data name="AutoSwitcherSettingsTrueListOfLang" xml:space="preserve">
    <value>List of languages ​​for which auto-switching will work</value>
  </data>
  <data name="AutoSwitcherSettingsListRulesHeader" xml:space="preserve">
    <value>List of rules for auto switching</value>
  </data>
  <data name="AutoSwitcherSettingsListRulesHeaderShowAll" xml:space="preserve">
    <value>Show candidates</value>
  </data>
  <data name="AutoSwitcherSettingsOpenRulesList" xml:space="preserve">
    <value>Open the list of auto-switching rules</value>
  </data>
  <data name="AutoSwitcherSettingsCountCheckRule" xml:space="preserve">
    <value>Number of manual switches of the word layout for inclusion in the rules</value>
  </data>
  <data name="AutoSwitchAcceptText" xml:space="preserve">
    <value>Add a word to the auto-switch rules? Enter - YES</value>
  </data>
  <data name="IsLangInfoWindowShowForMouse" xml:space="preserve">
    <value>Current input language on the mouse pointer</value>
  </data>
  <data name="IsLangInfoWindowShowForCaret" xml:space="preserve">
    <value>Current input language in the text cursor</value>
  </data>
  <data name="IsLangInfoWindowShowLargeWindow" xml:space="preserve">
    <value>Separate language indicator window</value>
  </data>
  <data name="IsLangInfoInTray" xml:space="preserve">
    <value>Current language in system tray</value>
  </data>
  <data name="IsLangInfoWindowShowForCaretEx" xml:space="preserve">
    <value>Advanced Features</value>
  </data>
  <data name="IsLangInfoShowIconsImage" xml:space="preserve">
    <value>Show country flag</value>
  </data>
  <data name="IsLangInfoShowIconsText" xml:space="preserve">
    <value>Show language name</value>
  </data>
  <data name="OpacityIconLangInfo" xml:space="preserve">
    <value>Indicator transparency</value>
  </data>
  <data name="SizeIconLangInfo" xml:space="preserve">
    <value>Increasing the size of the indicator as a percentage</value>
  </data>
  <data name="IsIndicateCurrentLangInKeyboardLed" xml:space="preserve">
    <value>Indication of the current language on the keyboard</value>
  </data>
  <data name="PosMouse" xml:space="preserve">
    <value>Position of the indicator on the mouse pointer</value>
  </data>
  <data name="IsHideIndicateInFullScreenApp" xml:space="preserve">
    <value>Hide the indicator in programs running full screen</value>
  </data>
  <data name="IsIndicateCapsLockState" xml:space="preserve">
    <value>Show CapsLock status</value>
  </data>
  <data name="StatusButtonCapsLockIsOn" xml:space="preserve">
    <value>CapsLock enabled</value>
  </data>
  <data name="PosCarret" xml:space="preserve">
    <value>Position of the indicator in the text cursor</value>
  </data>
  <data name="SpellcheckingSettingsHeader" xml:space="preserve">
    <value>Spell checking</value>
  </data>
  <data name="SpellcheckingSettingsIsOn" xml:space="preserve">
    <value>Spell checking enabled</value>
  </data>
  <data name="SpellcheckingSettingsWhileTyping" xml:space="preserve">
    <value>Check spelling while typing</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingSoundOn" xml:space="preserve">
    <value>Spell check sound when typing</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingUseNumber" xml:space="preserve">
    <value>Use numbers for quick replacement</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingSoundEdit" xml:space="preserve">
    <value>Sound settings for spell checking</value>
  </data>
  <data name="SpellcheckingKeyboardShortcuts" xml:space="preserve">
    <value>Keyboard shortcut to check the spelling of selected text</value>
  </data>
  <data name="SpellcheckingKeyboardShortcutsShort" xml:space="preserve">
    <value>Check the spelling of selected text</value>
  </data>
  <data name="SpellcheckingSettingsCloseByTimer" xml:space="preserve">
    <value>Close the window if there are no errors after 3 seconds</value>
  </data>
  <data name="ClipboardSettingsHeader" xml:space="preserve">
    <value>Clipboard Manager</value>
  </data>
  <data name="ClipboardKeyboardShortcuts" xml:space="preserve">
    <value>Paste text without formatting and paste the path of the copied file</value>
  </data>
  <data name="ClipboardOn" xml:space="preserve">
    <value>Clipboard manager enabled</value>
  </data>
  <data name="ClipboardKeyboardViewShortcuts" xml:space="preserve">
    <value>Open clipboard history using keyboard shortcut</value>
  </data>
  <data name="ClipboardKeyboardRoundShortcuts" xml:space="preserve">
    <value>Sequentially pasting text from the clipboard history for the current window</value>
  </data>
  <data name="ClipboardKeyboardRoundShortcutsShort" xml:space="preserve">
    <value>Sequentially pasting text from the clipboard history</value>
  </data>
  <data name="ClipboardSettingsPasteByIndexIsOn" xml:space="preserve">
    <value>Paste text by Ctrl+Shift+(number) - number 1, 2, 3, 4, 5, 6, 7, 8, 9 - index of entry in clipboard history</value>
  </data>
  <data name="ClipboardSettingsSaveFilePath" xml:space="preserve">
    <value>Save the path to the copied file in the clipboard history</value>
  </data>
  <data name="ClipboardSettingsSaveImage" xml:space="preserve">
    <value>Save image buffer history</value>
  </data>
  <data name="ClipboardSettingsReplaceWithoutChangeClipboard" xml:space="preserve">
    <value>When pasting text from history, replace the current value in the clipboard</value>
  </data>
  <data name="ClipboardMaxClipboardItems" xml:space="preserve">
    <value>Clipboard history size</value>
  </data>
  <data name="ClipboardSound" xml:space="preserve">
    <value>Clipboard change sound</value>
  </data>
  <data name="ConverterSettingsHeader" xml:space="preserve">
    <value>Text converter</value>
  </data>
  <data name="ConverterSettingsConvertDependsOnKeyboardLayout" xml:space="preserve">
    <value>Convert based on current keyboard layout</value>
  </data>
  <data name="ConverterSettingsExpression" xml:space="preserve">
    <value>Convert numbers and dates to strings, evaluate expressions</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchCapsSettings" xml:space="preserve">
    <value>Set up hotkeys</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsInvert" xml:space="preserve">
    <value>Invert case of selected text</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsUp" xml:space="preserve">
    <value>Convert selected text to uppercase</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsDown" xml:space="preserve">
    <value>Lowercase selected text</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsFirstLetterToDown" xml:space="preserve">
    <value>Lowercase the first character of the word under the cursor</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsFirstLetterToUp" xml:space="preserve">
    <value>Uppercase the first character of the word under the cursor</value>
  </data>
  <data name="ConverterSettingsTransliteration" xml:space="preserve">
    <value>Transliterate selected text</value>
  </data>
  <data name="ConverterSettingsCamelCase" xml:space="preserve">
    <value>Convert text to camelCase style</value>
  </data>
  <data name="ConverterReplaceSelText" xml:space="preserve">
    <value>Find and replace text in selected text</value>
  </data>
  <data name="ConverterSettingsEncloseTextQuotationMarks" xml:space="preserve">
    <value>Frame selected text with symbols (example)</value>
  </data>
  <data name="ConverterAdd" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="ConverterStart" xml:space="preserve">
    <value>Left</value>
  </data>
  <data name="ConverterEnd" xml:space="preserve">
    <value>Right</value>
  </data>
  <data name="ConverterDelete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="ConverterSampleText" xml:space="preserve">
    <value>example text</value>
  </data>
  <data name="TransCloseHeaderButton" xml:space="preserve">
    <value>Close ESC</value>
  </data>
  <data name="TransReplaceTextButton" xml:space="preserve">
    <value>Replace text ENTER</value>
  </data>
  <data name="TransSettingsHeader" xml:space="preserve">
    <value>Translation</value>
  </data>
  <data name="TransSettingsNativeLanguage" xml:space="preserve">
    <value>Default language to translate into</value>
  </data>
  <data name="TransSettingsLanguageFromTranslate" xml:space="preserve">
    <value>Default language to translate from</value>
  </data>
  <data name="TransSettingsMainLanguageForTheTranslation" xml:space="preserve">
    <value>Main language for translation</value>
  </data>
  <data name="TransSettingsProviderOfTranslation" xml:space="preserve">
    <value>Translation service</value>
  </data>
  <data name="TransSettingsKeyboardShortcuts" xml:space="preserve">
    <value>Keyboard shortcut to translate selected text</value>
  </data>
  <data name="TransSettingsInterKeyboardShortcuts" xml:space="preserve">
    <value>Press the key combination</value>
  </data>
  <data name="TransSettingsUseDoubleCtrl" xml:space="preserve">
    <value>Double-clicking the Ctrl button</value>
  </data>
  <data name="TransSettingsTranslationIsAlways" xml:space="preserve">
    <value>Translate when selecting text with the mouse</value>
  </data>
  <data name="TransSettingsIsOn" xml:space="preserve">
    <value>Translation included</value>
  </data>
  <data name="GeneralSettingsHeader" xml:space="preserve">
    <value>General settings</value>
  </data>
  <data name="GeneralSettingsLanguageProgram" xml:space="preserve">
    <value>Program interface language</value>
  </data>
  <data name="GeneralSettingsLanguageProgramRestart" xml:space="preserve">
    <value>Restart the application to change the language?</value>
  </data>
  <data name="GeneralSettingsOther" xml:space="preserve">
    <value>Miscellaneous</value>
  </data>
  <data name="GeneralSettingsMinimizeToTray" xml:space="preserve">
    <value>Minimize to tray</value>
  </data>
  <data name="GeneralSettingsStartUpWithWindows" xml:space="preserve">
    <value>Running from windows</value>
  </data>
  <data name="GeneralSettingsStartAdmin" xml:space="preserve">
    <value>Run with administrator rights</value>
  </data>
  <data name="GeneralSettingsIsCheckUpdate" xml:space="preserve">
    <value>Check for updates</value>
  </data>
  <data name="GeneralSettingsIsCheckUpdateBeta" xml:space="preserve">
    <value>Update to beta version</value>
  </data>
  <data name="GeneralSettingsTheme" xml:space="preserve">
    <value>Subject</value>
  </data>
  <data name="GeneralSettingsThemeDayNight" xml:space="preserve">
    <value>Day or night</value>
  </data>
  <data name="GeneralSettingsThemeAccent" xml:space="preserve">
    <value>Design styles</value>
  </data>
  <data name="GeneralSettingsDataFilePath" xml:space="preserve">
    <value>Folder for saving program settings</value>
  </data>
  <data name="GeneralSettingsIsProxyUseIE" xml:space="preserve">
    <value>Use system proxy settings</value>
  </data>
  <data name="GeneralSettingsProxyServer" xml:space="preserve">
    <value>Enter server address</value>
  </data>
  <data name="GeneralSettingsProxyPort" xml:space="preserve">
    <value>Enter port</value>
  </data>
  <data name="GeneralSettingsProxyUsername" xml:space="preserve">
    <value>Enter your username</value>
  </data>
  <data name="GeneralSettingsProxyPassword" xml:space="preserve">
    <value>Enter your password</value>
  </data>
  <data name="GeneralSettingsSaveProxy" xml:space="preserve">
    <value>Save proxy settings</value>
  </data>
  <data name="GeneralSettingsProxy" xml:space="preserve">
    <value>Proxy settings</value>
  </data>
  <data name="GeneralSettingsProxyError" xml:space="preserve">
    <value>Proxy server settings error, change settings</value>
  </data>
  <data name="GeneralSettingsUseNightTheme" xml:space="preserve">
    <value>Use a dark theme in the evening hours</value>
  </data>
  <data name="GeneralSettingsUseNightThemeStart" xml:space="preserve">
    <value>Evening hours from</value>
  </data>
  <data name="GeneralSettingsUseNightThemeEnd" xml:space="preserve">
    <value>Evening hours until</value>
  </data>
  <data name="GeneralSettingsIsStopWorkingFullScreen" xml:space="preserve">
    <value>Disable all functions in programs running in full screen mode</value>
  </data>
  <data name="GeneralSettingsImport" xml:space="preserve">
    <value>Import settings</value>
  </data>
  <data name="GeneralSettingsExport" xml:space="preserve">
    <value>Export settings</value>
  </data>
  <data name="GeneralTab" xml:space="preserve">
    <value>Basic settings</value>
  </data>
  <data name="TranslationTab" xml:space="preserve">
    <value>Translator</value>
  </data>
  <data name="CheckSpellingTab" xml:space="preserve">
    <value>Spell checking</value>
  </data>
  <data name="LangFlagTab" xml:space="preserve">
    <value>Layout indicator</value>
  </data>
  <data name="ProTabs" xml:space="preserve">
    <value>PRO features</value>
  </data>
  <data name="ClipboardTab" xml:space="preserve">
    <value>Clipboard</value>
  </data>
  <data name="DiareTab" xml:space="preserve">
    <value>Diary</value>
  </data>
  <data name="ConverterTab" xml:space="preserve">
    <value>Text converter</value>
  </data>
  <data name="SwitcherTab" xml:space="preserve">
    <value>Switching layouts</value>
  </data>
  <data name="AutoSwitcherTab" xml:space="preserve">
    <value>Auto switch</value>
  </data>
  <data name="ProgramsExceptionsTab" xml:space="preserve">
    <value>Exception programs</value>
  </data>
  <data name="ProgramsSetLayoutTab" xml:space="preserve">
    <value>Default layouts</value>
  </data>
  <data name="UniversalWindowTab" xml:space="preserve">
    <value>SmartClick</value>
  </data>
  <data name="AboutTab" xml:space="preserve">
    <value>About the program</value>
  </data>
  <data name="ProTab" xml:space="preserve">
    <value>License</value>
  </data>
  <data name="AutochangeTab" xml:space="preserve">
    <value>Snippets</value>
  </data>
  <data name="UniTranslate" xml:space="preserve">
    <value>Translate</value>
  </data>
  <data name="UniCopy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="UniSpellCheck" xml:space="preserve">
    <value>Check spelling</value>
  </data>
  <data name="UniSearch" xml:space="preserve">
    <value>Call search</value>
  </data>
  <data name="UniLink" xml:space="preserve">
    <value>Open link in browser</value>
  </data>
  <data name="UniLinkTranslate" xml:space="preserve">
    <value>Translate the site using the link</value>
  </data>
  <data name="UniLinkShorter" xml:space="preserve">
    <value>Generating a short link</value>
  </data>
  <data name="UniEmail" xml:space="preserve">
    <value>Create a mail message</value>
  </data>
  <data name="UniPaste" xml:space="preserve">
    <value>Insert text</value>
  </data>
  <data name="UniPasteUnf" xml:space="preserve">
    <value>Paste text without formatting</value>
  </data>
  <data name="UniClipboardHistory" xml:space="preserve">
    <value>Open clipboard history</value>
  </data>
  <data name="UniTranslateHistory" xml:space="preserve">
    <value>Open translation history</value>
  </data>
  <data name="UniDiaryHistory" xml:space="preserve">
    <value>Open diary</value>
  </data>
  <data name="UniUppercase" xml:space="preserve">
    <value>Change the case of selected text</value>
  </data>
  <data name="UniAutochange" xml:space="preserve">
    <value>Open list of snippets</value>
  </data>
  <data name="UniConverter" xml:space="preserve">
    <value>Text converter</value>
  </data>
  <data name="UniDownCase" xml:space="preserve">
    <value>Lowercase selected text</value>
  </data>
  <data name="UniUpCase" xml:space="preserve">
    <value>Convert selected text to uppercase</value>
  </data>
  <data name="UniInvertCase" xml:space="preserve">
    <value>Invert case of selected text</value>
  </data>
  <data name="UniEnclose" xml:space="preserve">
    <value>Frame selected text with symbols</value>
  </data>
  <data name="UniTranslit" xml:space="preserve">
    <value>Transliterate selected text</value>
  </data>
  <data name="UniConvertExpressions" xml:space="preserve">
    <value>Convert numbers and dates to strings, evaluate expressions</value>
  </data>
  <data name="UniTextConverter" xml:space="preserve">
    <value>Converter</value>
  </data>
  <data name="UniTextTranslate" xml:space="preserve">
    <value>Translation</value>
  </data>
  <data name="UniTextCopy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="UniTextSpellCheck" xml:space="preserve">
    <value>Spelling</value>
  </data>
  <data name="UniTextSearch" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="UniTextLink" xml:space="preserve">
    <value>Link</value>
  </data>
  <data name="UniTextLinkTranslate" xml:space="preserve">
    <value>Website translation</value>
  </data>
  <data name="UniTextLinkShorter" xml:space="preserve">
    <value>URL Shorter</value>
  </data>
  <data name="UniTextEmail" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="UniTextPaste" xml:space="preserve">
    <value>Insert</value>
  </data>
  <data name="UniTextPasteUnf1" xml:space="preserve">
    <value>Insertion without format.</value>
  </data>
  <data name="UniTextPasteUnf2" xml:space="preserve">
    <value>without format.</value>
  </data>
  <data name="UniTextClipboardHistory1" xml:space="preserve">
    <value>Buffer history</value>
  </data>
  <data name="UniTextClipboardHistory2" xml:space="preserve">
    <value>buffer</value>
  </data>
  <data name="UniTextDiaryHistory" xml:space="preserve">
    <value>Diary</value>
  </data>
  <data name="UniTextAutochange" xml:space="preserve">
    <value>Snippets</value>
  </data>
  <data name="UniTextDownCase" xml:space="preserve">
    <value>Register down</value>
  </data>
  <data name="UniTextUpCase" xml:space="preserve">
    <value>Register up</value>
  </data>
  <data name="UniTextInvertCase" xml:space="preserve">
    <value>Invert register</value>
  </data>
  <data name="UniTextEnclose" xml:space="preserve">
    <value>Framing text</value>
  </data>
  <data name="UniTextTranslit" xml:space="preserve">
    <value>Transliteration</value>
  </data>
  <data name="UniTextConvertExpressions" xml:space="preserve">
    <value>Mat. expressions</value>
  </data>
  <data name="HistoryDateStart" xml:space="preserve">
    <value>Start:</value>
  </data>
  <data name="HistoryDateEnd" xml:space="preserve">
    <value>End:</value>
  </data>
  <data name="HistoryDelSelected" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="HistoryClear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="HistoryToggleSwitch" xml:space="preserve">
    <value>Included</value>
  </data>
  <data name="DiaryHeaderText" xml:space="preserve">
    <value>TEXT</value>
  </data>
  <data name="DiaryHeaderApp" xml:space="preserve">
    <value>APPLICATION</value>
  </data>
  <data name="ProgramsExceptionsHeader" xml:space="preserve">
    <value>Exception programs</value>
  </data>
  <data name="ProgramsExceptionsAddNew" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="ProgramsExceptionsAddNewExeFile" xml:space="preserve">
    <value>Add exe file</value>
  </data>
  <data name="ProgramsExceptionsAddNewFilesFromFolder" xml:space="preserve">
    <value>Add folder</value>
  </data>
  <data name="ProgramsExceptionsAddNewTitle" xml:space="preserve">
    <value>Add by window title</value>
  </data>
  <data name="ProgramsExceptionsIsOnLayoutFlag" xml:space="preserve">
    <value>Layout indicator</value>
  </data>
  <data name="ProgramsExceptionsIsOnLayoutSwitcher" xml:space="preserve">
    <value>Switching layouts</value>
  </data>
  <data name="ProgramsExceptionsIsOnSmartClick" xml:space="preserve">
    <value>SmartClick</value>
  </data>
  <data name="ProgramsExceptionsIsOnAutochange" xml:space="preserve">
    <value>Snippets</value>
  </data>
  <data name="ProgramsExceptionsIsOnHotKeys" xml:space="preserve">
    <value>Hotkeys</value>
  </data>
  <data name="ProgramsExceptionsIsOnAutoSwitch" xml:space="preserve">
    <value>Automatic language switching</value>
  </data>
  <data name="ProgramsExceptionsIsOnDiary" xml:space="preserve">
    <value>Text input diary</value>
  </data>
  <data name="ProgramsExceptionsIsOnConverter" xml:space="preserve">
    <value>Converter and text case</value>
  </data>
  <data name="ProgramsExceptionsIsOnClipboard" xml:space="preserve">
    <value>Clipboard history</value>
  </data>
  <data name="ProgramsExceptionsIsOnClipboardImage" xml:space="preserve">
    <value>Save images</value>
  </data>
  <data name="ProgramsExceptionsDelete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="ProgramsExceptionsAddNewHelp" xml:space="preserve">
    <value>Click on the desired program</value>
  </data>
  <data name="ProgramsExceptionsCurrentInfoTooltip" xml:space="preserve">
    <value>Mark the functions that will work for the program</value>
  </data>
  <data name="ProgramsExceptionsProgramsList" xml:space="preserve">
    <value>Adding from the list of programs</value>
  </data>
  <data name="ProgramsExceptionsFromPoint" xml:space="preserve">
    <value>Adding with the cursor</value>
  </data>
  <data name="ProgramsSetLayoutHeader" xml:space="preserve">
    <value>Program languages</value>
  </data>
  <data name="ProgramsSetLayoutTabHeader" xml:space="preserve">
    <value>Default language for selected programs</value>
  </data>
  <data name="ProSettingsHeader" xml:space="preserve">
    <value>Activation of PRO functions</value>
  </data>
  <data name="ProSettingsStatus" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="ProSettingsInput" xml:space="preserve">
    <value>Activation code</value>
  </data>
  <data name="ProSettingsActivation" xml:space="preserve">
    <value>Activate</value>
  </data>
  <data name="ProSettingsEvaluation" xml:space="preserve">
    <value>Try it</value>
  </data>
  <data name="ProSettingsStatusOk" xml:space="preserve">
    <value>PRO activated</value>
  </data>
  <data name="ProSettingsStatusEvaluate" xml:space="preserve">
    <value>Trial period for 40 days</value>
  </data>
  <data name="ProSettingsLicenseInfo" xml:space="preserve">
    <value>License information</value>
  </data>
  <data name="ProSettingsLicenseActivateDate" xml:space="preserve">
    <value>Activation date:</value>
  </data>
  <data name="ProSettingsLicenseExpiryDate" xml:space="preserve">
    <value>License validity period:</value>
  </data>
  <data name="ProSettingsLicenseEmail" xml:space="preserve">
    <value>Email:</value>
  </data>
  <data name="ProSettingsLicenseUserName" xml:space="preserve">
    <value>Owner:</value>
  </data>
  <data name="ProSettingsGetData" xml:space="preserve">
    <value>License information</value>
  </data>
  <data name="ProSettingsLicenseUsersCount" xml:space="preserve">
    <value>Number of seats:</value>
  </data>
  <data name="ProSettingsLicenseCountReact" xml:space="preserve">
    <value>Number of available reactivations:</value>
  </data>
  <data name="ProSettingsLicenseCountFreeSeats" xml:space="preserve">
    <value>Available seats:</value>
  </data>
  <data name="ProSettingsLicenseIsEnterprise" xml:space="preserve">
    <value>License type:</value>
  </data>
  <data name="ProSettingsLicenseNotPro" xml:space="preserve">
    <value>PRO not activated</value>
  </data>
  <data name="ProSettingsIsEvaluation" xml:space="preserve">
    <value>PRO activated for trial period</value>
  </data>
  <data name="ProSettingsPurchase" xml:space="preserve">
    <value>Buy</value>
  </data>
  <data name="ProSettingsEmail" xml:space="preserve">
    <value>Enter email</value>
  </data>
  <data name="ProSettingsCode" xml:space="preserve">
    <value>Enter code</value>
  </data>
  <data name="ProSendEmailLic" xml:space="preserve">
    <value>Your license information has been sent to:</value>
  </data>
  <data name="ProSettingsDelete" xml:space="preserve">
    <value>Remove license</value>
  </data>
  <data name="ProSettingsCodeEmail" xml:space="preserve">
    <value>Enter code and email</value>
  </data>
  <data name="ProSettingsActivationOk" xml:space="preserve">
    <value>Activation completed successfully</value>
  </data>
  <data name="ProSettingsActivationError" xml:space="preserve">
    <value>Activation completed with error</value>
  </data>
  <data name="ProSettingsActivationBlocked" xml:space="preserve">
    <value>Activation completed with error, your license is blocked</value>
  </data>
  <data name="ProSettingsActivationReactivated" xml:space="preserve">
    <value>The program was activated on the new workplace, but was deactivated on the computer</value>
  </data>
  <data name="ProSettingsActivationInternetError" xml:space="preserve">
    <value>Activation completed with error, check your internet connection</value>
  </data>
  <data name="ProSettingsActivationErrorEmail" xml:space="preserve">
    <value>Activation completed with an error, an incorrect email may have been entered</value>
  </data>
  <data name="ProSettingsExpiredEva" xml:space="preserve">
    <value>Trial period has expired</value>
  </data>
  <data name="ProSettingsExpired" xml:space="preserve">
    <value>License has expired</value>
  </data>
  <data name="ProSettingsBlockedByMonthActivateLimit" xml:space="preserve">
    <value>Activation is not possible due to exceeding the reactivation limit, please purchase additional seats for this license</value>
  </data>
  <data name="ProSettingsBlockedByEarlyActivation" xml:space="preserve">
    <value>Activation is not possible due to reactivation of this license on another computer</value>
  </data>
  <data name="ProSettingsNewLic" xml:space="preserve">
    <value>Your activation code has been updated</value>
  </data>
  <data name="ProSettingsNoExpiry" xml:space="preserve">
    <value>Perpetual license</value>
  </data>
  <data name="ProBlockedByEarlyActivation" xml:space="preserve">
    <value>You have exceeded the limit on the number of seats for the license, all PRO features will be disabled</value>
  </data>
  <data name="ProBlockedExp" xml:space="preserve">
    <value>The PRO version has expired, a new license can be purchased on the EVERYLANG.NET website</value>
  </data>
  <data name="ProBlockedExpToDays" xml:space="preserve">
    <value>PRO version expires soon</value>
  </data>
  <data name="ProBlockedByMonthActivateLimit" xml:space="preserve">
    <value>The license reactivation limit for the last 30 days has been exceeded, all PRO features will be disabled</value>
  </data>
  <data name="ProBlocked" xml:space="preserve">
    <value>Your license is blocked, all PRO features will be disabled</value>
  </data>
  <data name="UniversalWindowSettingsHeader" xml:space="preserve">
    <value>SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsUniversalWindowIsOn" xml:space="preserve">
    <value>SmartClick enabled</value>
  </data>
  <data name="UniversalWindowSettingsSearchServices" xml:space="preserve">
    <value>Select a search service for SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsItemsCheck" xml:space="preserve">
    <value>Check the features that will be available</value>
  </data>
  <data name="UniversalWindowSettingsShowOnPressLeftAndRightMouseButtons" xml:space="preserve">
    <value>Open by pressing the left and then right mouse button</value>
  </data>
  <data name="UniversalWindowSettingsShowOnDoubleMiddle" xml:space="preserve">
    <value>Open by double-clicking the middle mouse button</value>
  </data>
  <data name="UniversalWindowSettingsShowOnPressHotKeys" xml:space="preserve">
    <value>Use hotkeys to open</value>
  </data>
  <data name="SmartClickShortcutSettingsHeader" xml:space="preserve">
    <value>Hotkeys for opening SmartClick window</value>
  </data>
  <data name="UniversalWindowSettingsShowMiniOn" xml:space="preserve">
    <value>Show help window after text selection</value>
  </data>
  <data name="SmartClickMiniSize" xml:space="preserve">
    <value>Window size</value>
  </data>
  <data name="SmartClickMiniPos" xml:space="preserve">
    <value>Window position relative to mouse pointer</value>
  </data>
  <data name="DiarySettingsHeader" xml:space="preserve">
    <value>Diary</value>
  </data>
  <data name="DiaryShortcuts" xml:space="preserve">
    <value>Open diary</value>
  </data>
  <data name="DiaryIsOn" xml:space="preserve">
    <value>Diary included</value>
  </data>
  <data name="DiaryPassword" xml:space="preserve">
    <value>Diary password</value>
  </data>
  <data name="DiaryPasswordOld" xml:space="preserve">
    <value>Enter your old diary password</value>
  </data>
  <data name="DiaryPasswordSaved" xml:space="preserve">
    <value>New password saved</value>
  </data>
  <data name="DiaryPasswordReset" xml:space="preserve">
    <value>Password reset</value>
  </data>
  <data name="DiaryMaxItems" xml:space="preserve">
    <value>Number of entries in the diary</value>
  </data>
  <data name="DiaryOldPasswordWrong" xml:space="preserve">
    <value>The entered password is incorrect, reset the current password? In this case, all data from the diary will be deleted</value>
  </data>
  <data name="IsSaveOneWordSentences" xml:space="preserve">
    <value>Keep one-word sentences</value>
  </data>
  <data name="AutochangeHelperFromText" xml:space="preserve">
    <value>Text to replace (optional):</value>
  </data>
  <data name="AutochangeHelperToText" xml:space="preserve">
    <value>Snippet text:</value>
  </data>
  <data name="AutochangeHelperLangListDesc" xml:space="preserve">
    <value>Which language should I switch the layout to?</value>
  </data>
  <data name="AutochangeHelperLangListNoSwitch" xml:space="preserve">
    <value>Don't switch</value>
  </data>
  <data name="AutochangeHelperTags" xml:space="preserve">
    <value>Tags (separated by spaces):</value>
  </data>
  <data name="AutochangeHelperTagsWatermark" xml:space="preserve">
    <value>Tags</value>
  </data>
  <data name="AutochangeHelperDesc" xml:space="preserve">
    <value>Description:</value>
  </data>
  <data name="AutochangeHelperOk" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="AutochangeHelperCancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="AutochangeHelperSaveCursorPosition" xml:space="preserve">
    <value>Maintain cursor position</value>
  </data>
  <data name="AutochangeHelperChangeAtOnce" xml:space="preserve">
    <value>Replace when typing</value>
  </data>
  <data name="AutochangeTextHeader" xml:space="preserve">
    <value>Snippets</value>
  </data>
  <data name="ToReplacerButton" xml:space="preserve">
    <value>New snippet</value>
  </data>
  <data name="AutochangeDelWithTag" xml:space="preserve">
    <value>Delete all snippets with this tag?</value>
  </data>
  <data name="AutochangeOnInSnippetsList" xml:space="preserve">
    <value>Snippetst enabled</value>
  </data>
  <data name="AutochangeSnippetsList" xml:space="preserve">
    <value>Edit snippets</value>
  </data>
  <data name="AutochangeHeaderFromText" xml:space="preserve">
    <value>What to replace</value>
  </data>
  <data name="AutochangeHeaderToText" xml:space="preserve">
    <value>What to replace with</value>
  </data>
  <data name="AutochangeAddNew" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="AutochangeEdit" xml:space="preserve">
    <value>Change</value>
  </data>
  <data name="AutochangeDelete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="AutochangeOtherLayout" xml:space="preserve">
    <value>Replace when typing in another layout</value>
  </data>
  <data name="AutochangeCaseLetters" xml:space="preserve">
    <value>Match letter case</value>
  </data>
  <data name="AutochangeShowMiniWindow" xml:space="preserve">
    <value>Show hint when typing</value>
  </data>
  <data name="AutochangeIsEnabledCountUsage" xml:space="preserve">
    <value>Sort by frequency of use</value>
  </data>
  <data name="AutochangeChangeMethods" xml:space="preserve">
    <value>Replace by:</value>
  </data>
  <data name="AutochangeIsOn" xml:space="preserve">
    <value>Snippets enabled</value>
  </data>
  <data name="AutochangeKeyboardShortcuts" xml:space="preserve">
    <value>Open list of snippets to insert</value>
  </data>
  <data name="AutochangeKeyboardShortcutsAddNew" xml:space="preserve">
    <value>Add highlighted text to snippets</value>
  </data>
  <data name="AutochangeOnTab" xml:space="preserve">
    <value>Tab key</value>
  </data>
  <data name="AutochangeOnInter" xml:space="preserve">
    <value>Enter key</value>
  </data>
  <data name="AutochangeOnTabOrInter" xml:space="preserve">
    <value>Tab or Enter</value>
  </data>
  <data name="AutochangeOnSpace" xml:space="preserve">
    <value>Space</value>
  </data>
  <data name="AutochangeOnSpaceOrInter" xml:space="preserve">
    <value>Space or Enter</value>
  </data>
  <data name="HotKeyUseHotkey" xml:space="preserve">
    <value>Hotkeys</value>
  </data>
  <data name="HotKeyUseDoubleKeyDown" xml:space="preserve">
    <value>Double key press</value>
  </data>
  <data name="HotKeyUseMouseXKey" xml:space="preserve">
    <value>Clicking a mouse button</value>
  </data>
  <data name="HotKeyIsON" xml:space="preserve">
    <value>Hotkeys enabled</value>
  </data>
  <data name="HotKeyWithoutShortcutNull" xml:space="preserve">
    <value>Absent</value>
  </data>
  <data name="HotKeyWithoutPressShortcut" xml:space="preserve">
    <value>Press hotkeys</value>
  </data>
  <data name="HotKeyShortcut" xml:space="preserve">
    <value>Combination</value>
  </data>
  <data name="HotKeyDoubleKeyDown" xml:space="preserve">
    <value>Double tap</value>
  </data>
  <data name="HotKeyMouse" xml:space="preserve">
    <value>Clicking a mouse button</value>
  </data>
  <data name="HotKeyDoubleKeyDownSelectKey" xml:space="preserve">
    <value>Select a key to double press</value>
  </data>
  <data name="HotKeyMouseSelectKey" xml:space="preserve">
    <value>Select an additional mouse button</value>
  </data>
  <data name="HotKeyIsOff" xml:space="preserve">
    <value>Hotkeys disabled</value>
  </data>
  <data name="HotKeyDoubleLeftOrRightCtrl" xml:space="preserve">
    <value>Left or Right Ctrl</value>
  </data>
  <data name="HotKeyDoubleLeftCtrl" xml:space="preserve">
    <value>Left Ctrl</value>
  </data>
  <data name="HotKeyDoubleRightCtrl" xml:space="preserve">
    <value>Right Ctrl</value>
  </data>
  <data name="HotKeyDoubleLeftOrRightShift" xml:space="preserve">
    <value>Left or Right Shift</value>
  </data>
  <data name="HotKeyDoubleLeftShift" xml:space="preserve">
    <value>Left Shift</value>
  </data>
  <data name="HotKeyDoubleRightShift" xml:space="preserve">
    <value>Right Shift</value>
  </data>
  <data name="HotKeyDoubleLeftAlt" xml:space="preserve">
    <value>Left Alt</value>
  </data>
  <data name="HotKeyDoubleRightAlt" xml:space="preserve">
    <value>Right Alt</value>
  </data>
  <data name="SoundOnOff" xml:space="preserve">
    <value>Sound on</value>
  </data>
  <data name="SoundFormVolume" xml:space="preserve">
    <value>Volume</value>
  </data>
  <data name="SoundFormSelectTrack" xml:space="preserve">
    <value>Select a sound</value>
  </data>
  <data name="StartPageHeader" xml:space="preserve">
    <value>Before using the program, read the documentation</value>
  </data>
  <data name="StartPageHelp" xml:space="preserve">
    <value>Explore the program's features</value>
  </data>
  <data name="StartPageVideo" xml:space="preserve">
    <value>Watch the video presentation</value>
  </data>
  <data name="StartPageSite" xml:space="preserve">
    <value>Program website</value>
  </data>
  <data name="StartPageLicense" xml:space="preserve">
    <value>Purchase a license</value>
  </data>
  <data name="OcrHeader" xml:space="preserve">
    <value>Text recognition</value>
  </data>
  <data name="OcrKeyboardShortcuts" xml:space="preserve">
    <value>Keyboard shortcut to start OCR</value>
  </data>
  <data name="OcrDescDefault" xml:space="preserve">
    <value>Select default languages</value>
  </data>
  <data name="OcrDescLang" xml:space="preserve">
    <value>Select languages</value>
  </data>
  <data name="OcrDescNotSup" xml:space="preserve">
    <value>Using both European and Asian languages ​​is not supported</value>
  </data>
  <data name="OcrEuropeanLang" xml:space="preserve">
    <value>European languages:</value>
  </data>
  <data name="OcrAsianLang" xml:space="preserve">
    <value>Asian languages:</value>
  </data>
  <data name="OcrCaptureArea" xml:space="preserve">
    <value>Select screen area</value>
  </data>
  <data name="OcrOpenImageOrPDFFile" xml:space="preserve">
    <value>Open image</value>
  </data>
  <data name="OcrRecognize" xml:space="preserve">
    <value>Recognize</value>
  </data>
  <data name="OcrRecognizeBarcode" xml:space="preserve">
    <value>Recognize barcode</value>
  </data>
  <data name="OcrSelectLanguages" xml:space="preserve">
    <value>Select languages</value>
  </data>
  <data name="OcrStartText" xml:space="preserve">
    <value>Select an area on the screen or upload a file for recognition</value>
  </data>
  <data name="OcrTab" xml:space="preserve">
    <value>Text recognition</value>
  </data>
  <data name="OcrInit" xml:space="preserve">
    <value>Please wait, module is loading</value>
  </data>
  <data name="OcrLoadLibs" xml:space="preserve">
    <value>Click to download module</value>
  </data>
  <data name="OcrWaitResult" xml:space="preserve">
    <value>Text copied</value>
  </data>
  <data name="OcrWaitResultFail" xml:space="preserve">
    <value>Text not recognized</value>
  </data>
  <data name="OcrCopyImage" xml:space="preserve">
    <value>Copy image</value>
  </data>
  <data name="OcrCopyText" xml:space="preserve">
    <value>Copy text</value>
  </data>
  <data name="ReplaceTo" xml:space="preserve">
    <value>Replace with:</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Search:</value>
  </data>
  <data name="TotalMatchFound" xml:space="preserve">
    <value>Total matches found:</value>
  </data>
  <data name="NoMatchFound" xml:space="preserve">
    <value>No matches found!</value>
  </data>
  <data name="TotalMatchFoundReplace" xml:space="preserve">
    <value>Total replacements made:</value>
  </data>
  <data name="DiaryHeaderDate" xml:space="preserve">
    <value>DATE</value>
  </data>
  <data name="DiaryHeaderFormat" xml:space="preserve">
    <value>TYPE</value>
  </data>
  <data name="ClipboardOff" xml:space="preserve">
    <value>Clipboard manager is disabled</value>
  </data>
  <data name="GridViewClearFilter" xml:space="preserve">
    <value>Clear filter</value>
  </data>
  <data name="GridViewFilter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="GridViewColumnsSelectionButtonTooltip" xml:space="preserve">
    <value>Select Columns</value>
  </data>
  <data name="GridViewSearchPanelTopText" xml:space="preserve">
    <value>Full text search</value>
  </data>
  <data name="GridViewGroupPanelTopTextGrouped" xml:space="preserve">
    <value>Grouped by:</value>
  </data>
  <data name="GridViewGroupPanelTopText" xml:space="preserve">
    <value>Group header</value>
  </data>
  <data name="GridViewGroupPanelText" xml:space="preserve">
    <value>Drag a column header and drop it here to group by this column</value>
  </data>
  <data name="GridViewFilterIsNotEmpty" xml:space="preserve">
    <value>not empty</value>
  </data>
  <data name="GridViewFilterIsEmpty" xml:space="preserve">
    <value>Empty</value>
  </data>
  <data name="GridViewFilterIsNotNull" xml:space="preserve">
    <value>Is not null</value>
  </data>
  <data name="GridViewFilterIsNull" xml:space="preserve">
    <value>Null</value>
  </data>
  <data name="GridViewFilterStartsWith" xml:space="preserve">
    <value>Starts with</value>
  </data>
  <data name="GridViewFilterShowRowsWithValueThat" xml:space="preserve">
    <value>Show rows with value that</value>
  </data>
  <data name="GridViewFilterSelectAll" xml:space="preserve">
    <value>Select all</value>
  </data>
  <data name="GridViewFilterOr" xml:space="preserve">
    <value>Or</value>
  </data>
  <data name="GridViewFilterMatchCase" xml:space="preserve">
    <value>Case sensitive</value>
  </data>
  <data name="GridViewFilterIsNotEqualTo" xml:space="preserve">
    <value>not equal</value>
  </data>
  <data name="GridViewFilterIsLessThanOrEqualTo" xml:space="preserve">
    <value>Less than or equal to</value>
  </data>
  <data name="GridViewFilterIsLessThan" xml:space="preserve">
    <value>Less than</value>
  </data>
  <data name="GridViewFilterIsNotContainedIn" xml:space="preserve">
    <value>Not contained in</value>
  </data>
  <data name="GridViewFilterIsGreaterThanOrEqualTo" xml:space="preserve">
    <value>Greater than or equal to</value>
  </data>
  <data name="GridViewFilterIsGreaterThan" xml:space="preserve">
    <value>More than</value>
  </data>
  <data name="GridViewFilterIsEqualTo" xml:space="preserve">
    <value>Equals</value>
  </data>
  <data name="GridViewFilterIsContainedIn" xml:space="preserve">
    <value>Contained in</value>
  </data>
  <data name="GridViewFilterEndsWith" xml:space="preserve">
    <value>Ends with</value>
  </data>
  <data name="GridViewFilterDoesNotContain" xml:space="preserve">
    <value>Does not contain</value>
  </data>
  <data name="GridViewFilterContains" xml:space="preserve">
    <value>Contains</value>
  </data>
  <data name="GridViewFilterAnd" xml:space="preserve">
    <value>AND</value>
  </data>
  <data name="DiaryOn" xml:space="preserve">
    <value>Diary included</value>
  </data>
  <data name="DiaryOff" xml:space="preserve">
    <value>Diary is turned off</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>Included</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>Disabled</value>
  </data>
  <data name="SwitcherSettingsIsOff" xml:space="preserve">
    <value>Layout switching disabled</value>
  </data>
  <data name="AutoSwitchSettingsIsOn" xml:space="preserve">
    <value>Auto layout switching enabled</value>
  </data>
  <data name="AutoSwitchSettingsIsOff" xml:space="preserve">
    <value>Auto layout switching is disabled</value>
  </data>
  <data name="GridViewAlwaysVisibleNewRow" xml:space="preserve">
    <value>Click here to add a new item</value>
  </data>
  <data name="TransSettingsClearAllHistory" xml:space="preserve">
    <value>Clear translation history</value>
  </data>
  <data name="TransSettingsHistoryIsOn" xml:space="preserve">
    <value>Store translation history</value>
  </data>
  <data name="CopyTranslatedText" xml:space="preserve">
    <value>Copy translated text</value>
  </data>
  <data name="ClearAll" xml:space="preserve">
    <value>Clear all</value>
  </data>
  <data name="SiteSourceButton" xml:space="preserve">
    <value>Open in browser</value>
  </data>
  <data name="CloseQuestion" xml:space="preserve">
    <value>Close the program?</value>
  </data>
  <data name="TransSettingsFavoriteLanguages" xml:space="preserve">
    <value>Featured languages</value>
  </data>
  <data name="TransSettingsChooseYourFavoriteLanguages" xml:space="preserve">
    <value>Select your favorite languages</value>
  </data>
  <data name="GeneralSettingsFont" xml:space="preserve">
    <value>Font</value>
  </data>
  <data name="ProgramsExceptionsCurrentInfo" xml:space="preserve">
    <value>Functions</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Loading</value>
  </data>
  <data name="ImageEditor_CanvasResize" xml:space="preserve">
    <value>Changing the Canvas Size</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="ImageEditor_Adjust" xml:space="preserve">
    <value>Correction</value>
  </data>
  <data name="ImageEditor_Amount" xml:space="preserve">
    <value>Sum</value>
  </data>
  <data name="ImageEditor_Auto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="ImageEditor_Background" xml:space="preserve">
    <value>Background:</value>
  </data>
  <data name="ImageEditor_BorderColor" xml:space="preserve">
    <value>Border color:</value>
  </data>
  <data name="ImageEditor_BorderThickness" xml:space="preserve">
    <value>Border Thickness:</value>
  </data>
  <data name="ImageEditor_CanvasSize" xml:space="preserve">
    <value>Canvas size</value>
  </data>
  <data name="ImageEditor_ColorPicker_NoColorText_White" xml:space="preserve">
    <value>white</value>
  </data>
  <data name="ImageEditor_Crop" xml:space="preserve">
    <value>Trim</value>
  </data>
  <data name="ImageEditor_DrawText" xml:space="preserve">
    <value>Picture text</value>
  </data>
  <data name="ImageEditor_DrawText_YourTextHere" xml:space="preserve">
    <value>Your text</value>
  </data>
  <data name="ImageEditor_DrawTool" xml:space="preserve">
    <value>Draw</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushColor" xml:space="preserve">
    <value>Brush color:</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushSize" xml:space="preserve">
    <value>Brush size:</value>
  </data>
  <data name="ImageEditor_Effect_Blur" xml:space="preserve">
    <value>Blur</value>
  </data>
  <data name="ImageEditor_Effect_Brightness" xml:space="preserve">
    <value>Brightness</value>
  </data>
  <data name="ImageEditor_Effect_ContrastAdjust" xml:space="preserve">
    <value>Contrast</value>
  </data>
  <data name="ImageEditor_Effect_HueShift" xml:space="preserve">
    <value>Changing Hue</value>
  </data>
  <data name="ImageEditor_Effect_InvertColors" xml:space="preserve">
    <value>Invert colors</value>
  </data>
  <data name="ImageEditor_Effect_Saturation" xml:space="preserve">
    <value>Saturation</value>
  </data>
  <data name="ImageEditor_Effect_Sharpen" xml:space="preserve">
    <value>Sharpen</value>
  </data>
  <data name="ImageEditor_Effects" xml:space="preserve">
    <value>Modification</value>
  </data>
  <data name="ImageEditor_FlipHorizontal" xml:space="preserve">
    <value>Flip horizontally</value>
  </data>
  <data name="ImageEditor_FlipVertical" xml:space="preserve">
    <value>Flip vertically</value>
  </data>
  <data name="ImageEditor_FontSize" xml:space="preserve">
    <value>Font size</value>
  </data>
  <data name="ImageEditor_Height" xml:space="preserve">
    <value>Height:</value>
  </data>
  <data name="ImageEditor_HorizontalPosition" xml:space="preserve">
    <value>Horizontal position</value>
  </data>
  <data name="ImageEditor_ImageAlignment" xml:space="preserve">
    <value>Image Alignment</value>
  </data>
  <data name="ImageEditor_ImagePreview" xml:space="preserve">
    <value>Image preview</value>
  </data>
  <data name="ImageEditor_ImageSize" xml:space="preserve">
    <value>Image Size</value>
  </data>
  <data name="ImageEditor_Open" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="ImageEditor_Options" xml:space="preserve">
    <value>Options</value>
  </data>
  <data name="ImageEditor_PreserveAspectRatio" xml:space="preserve">
    <value>Keep original aspect ratio</value>
  </data>
  <data name="ImageEditor_Radius" xml:space="preserve">
    <value>Radius:</value>
  </data>
  <data name="ImageEditor_Redo" xml:space="preserve">
    <value>Return</value>
  </data>
  <data name="ImageEditor_RelativeSize" xml:space="preserve">
    <value>Relative size</value>
  </data>
  <data name="ImageEditor_Resize" xml:space="preserve">
    <value>Resize</value>
  </data>
  <data name="ImageEditor_Rotate180" xml:space="preserve">
    <value>Rotate 180°</value>
  </data>
  <data name="ImageEditor_Rotate270" xml:space="preserve">
    <value>Rotate 270°</value>
  </data>
  <data name="ImageEditor_Rotate90" xml:space="preserve">
    <value>Rotate 90°</value>
  </data>
  <data name="ImageEditor_Rotation" xml:space="preserve">
    <value>Turn</value>
  </data>
  <data name="ImageEditor_RoundCorners" xml:space="preserve">
    <value>Rounded corners</value>
  </data>
  <data name="ImageEditor_Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="ImageEditor_Shape" xml:space="preserve">
    <value>Figure</value>
  </data>
  <data name="ImageEditor_Shapes_Ellipse" xml:space="preserve">
    <value>Ellipse</value>
  </data>
  <data name="ImageEditor_Shapes_Line" xml:space="preserve">
    <value>Schedule</value>
  </data>
  <data name="ImageEditor_Shapes_Rectangle" xml:space="preserve">
    <value>Rectangle</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderColor" xml:space="preserve">
    <value>Border color</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderThickness" xml:space="preserve">
    <value>Border Thickness</value>
  </data>
  <data name="ImageEditor_ShapeTool_FillShape" xml:space="preserve">
    <value>Pipe Filling Form</value>
  </data>
  <data name="ImageEditor_ShapeTool_LockRatio" xml:space="preserve">
    <value>Lock proportions</value>
  </data>
  <data name="ImageEditor_ShapeTool_Shape" xml:space="preserve">
    <value>Figure</value>
  </data>
  <data name="ImageEditor_ShapeTool_ShapeFill" xml:space="preserve">
    <value>Filling a shape</value>
  </data>
  <data name="ImageEditor_Text" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="ImageEditor_TextColor" xml:space="preserve">
    <value>Text color</value>
  </data>
  <data name="ImageEditor_TheFileCannotBeOpened" xml:space="preserve">
    <value>The file cannot be opened.</value>
  </data>
  <data name="ImageEditor_TheFileIsLocked" xml:space="preserve">
    <value>The file cannot be opened. This may be blocked by another application.</value>
  </data>
  <data name="ImageEditor_Transform" xml:space="preserve">
    <value>Convert</value>
  </data>
  <data name="ImageEditor_UnableToSaveFile" xml:space="preserve">
    <value>Failed to save file.</value>
  </data>
  <data name="ImageEditor_Undo" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="ImageEditor_UnsupportedFileFormat" xml:space="preserve">
    <value>This file format is not supported.</value>
  </data>
  <data name="ImageEditor_VerticalPosition" xml:space="preserve">
    <value>Vertical position</value>
  </data>
  <data name="ImageEditor_Width" xml:space="preserve">
    <value>Width:</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="ResetAll" xml:space="preserve">
    <value>Reset everything</value>
  </data>
  <data name="OcrFromClipboard" xml:space="preserve">
    <value>From clipboard</value>
  </data>
  <data name="OcrEditImage" xml:space="preserve">
    <value>Edit image</value>
  </data>
  <data name="PasteButtonWithoutClipboard" xml:space="preserve">
    <value>Paste emulating input</value>
  </data>
  <data name="AutochangeEditor" xml:space="preserve">
    <value>Snippet editor</value>
  </data>
  <data name="AutochangeHelperFromTextTootlTip" xml:space="preserve">
    <value>Replacement text:</value>
  </data>
  <data name="OcrModuleNotLoaded" xml:space="preserve">
    <value>Text recognition module is not loaded</value>
  </data>
  <data name="AppearanceTab" xml:space="preserve">
    <value>Appearance</value>
  </data>
  <data name="OrderFunctionsTab" xml:space="preserve">
    <value>Order of functions</value>
  </data>
  <data name="TranslateOnlyFavoriteLanguages" xml:space="preserve">
    <value>Show only selected languages</value>
  </data>
  <data name="TranslateSowAll" xml:space="preserve">
    <value>Show all</value>
  </data>
  <data name="GeneralSettingsThemeProgramRestart" xml:space="preserve">
    <value>Restart the program to change the theme?</value>
  </data>
  <data name="CommonWindowPressKeyForPast" xml:space="preserve">
    <value>To insert text, press the key</value>
  </data>
  <data name="MiminoteTab" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="NoteTab" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="NotesShow" xml:space="preserve">
    <value>Show notes</value>
  </data>
  <data name="NotesToArchive" xml:space="preserve">
    <value>To the archive</value>
  </data>
  <data name="NotesAddNew" xml:space="preserve">
    <value>Add a note</value>
  </data>
  <data name="NotesList" xml:space="preserve">
    <value>List of notes</value>
  </data>
  <data name="NoteColor" xml:space="preserve">
    <value>Note color</value>
  </data>
  <data name="NoteConvertToNote" xml:space="preserve">
    <value>Convert to Note</value>
  </data>
  <data name="NoteConvertToTaskList" xml:space="preserve">
    <value>Convert to task list</value>
  </data>
  <data name="NotePasteAsPlainText" xml:space="preserve">
    <value>Paste as normal text</value>
  </data>
  <data name="NotePasteAsText" xml:space="preserve">
    <value>Paste as text</value>
  </data>
  <data name="NoteArchiveList" xml:space="preserve">
    <value>Notes Archive</value>
  </data>
  <data name="NoteRestore" xml:space="preserve">
    <value>Restore</value>
  </data>
  <data name="NoteFontFamilyAndSize" xml:space="preserve">
    <value>Font family and size for notes</value>
  </data>
  <data name="NoteTransparencyForInactiveNotes" xml:space="preserve">
    <value>Transparency of inactive notes</value>
  </data>
  <data name="UniversalWindowSettingsUniversalWindowIsOff" xml:space="preserve">
    <value>SmartClick disabled</value>
  </data>
  <data name="DiaryIsOff" xml:space="preserve">
    <value>Diary disabled</value>
  </data>
  <data name="AutochangeIsOff" xml:space="preserve">
    <value>Snippets disabled</value>
  </data>
  <data name="GeneralSettingsCanClose" xml:space="preserve">
    <value>Show close button</value>
  </data>
  <data name="SwitcherPauseTimeForKeysSend" xml:space="preserve">
    <value>Timeout for keystroke emulation</value>
  </data>
  <data name="OcrImage" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="OcrRecognizedText" xml:space="preserve">
    <value>Recognized text</value>
  </data>
  <data name="UpdateError" xml:space="preserve">
    <value>The update was not completed, please update manually.</value>
  </data>
  <data name="UpdateErrorTitle" xml:space="preserve">
    <value>EveryLang update error</value>
  </data>
  <data name="NoteCheckMarket" xml:space="preserve">
    <value>Tagged</value>
  </data>
  <data name="NotesIsShowing" xml:space="preserve">
    <value>Included</value>
  </data>
  <data name="SearchHelperText" xml:space="preserve">
    <value>Enter text to search</value>
  </data>
  <data name="TransLangAuto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="NotesFromArchive" xml:space="preserve">
    <value>From the archive</value>
  </data>
  <data name="NotesIsHiding" xml:space="preserve">
    <value>Disabled</value>
  </data>
  <data name="NoteInArchive" xml:space="preserve">
    <value>In the archive</value>
  </data>
  <data name="KeyboardLayoutTab" xml:space="preserve">
    <value>Keyboard layout</value>
  </data>
  <data name="CapsTab" xml:space="preserve">
    <value>Text case</value>
  </data>
  <data name="IsIndicateNumLockState" xml:space="preserve">
    <value>Show NumLock status</value>
  </data>
  <data name="StatusButtonNumLockIsOff" xml:space="preserve">
    <value>NumLock is disabled</value>
  </data>
  <data name="StatusButtonNumLockIsOn" xml:space="preserve">
    <value>NumLock enabled</value>
  </data>
  <data name="ConverterSettingsOpenWindow" xml:space="preserve">
    <value>Open a window with converter functions</value>
  </data>
  <data name="UniFirstLetterUp" xml:space="preserve">
    <value>Uppercase the first letter</value>
  </data>
  <data name="UniTextFirstLetterUp" xml:space="preserve">
    <value>First up</value>
  </data>
  <data name="UniFirstLetterDown" xml:space="preserve">
    <value>Lowercase first letter</value>
  </data>
  <data name="UniTextFirstLetterDown" xml:space="preserve">
    <value>First down</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsCapsOpenWindow" xml:space="preserve">
    <value>Open a window with case change functions</value>
  </data>
  <data name="ConverterSettingsSnakeCase" xml:space="preserve">
    <value>Convert text to snake_case style</value>
  </data>
  <data name="ConverterSettingsKebabCase" xml:space="preserve">
    <value>Converting text to kebab-case style</value>
  </data>
  <data name="ConverterSettingsPascalCase" xml:space="preserve">
    <value>Converting text to PascalCase style</value>
  </data>
  <data name="UniCase" xml:space="preserve">
    <value>Text case</value>
  </data>
  <data name="AboutSettingsUpdatePressForUpdate" xml:space="preserve">
    <value>Click to download</value>
  </data>
</root>