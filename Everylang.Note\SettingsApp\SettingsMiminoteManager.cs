﻿using LiteDB;
using Microsoft.Win32;
using System;
using System.IO;
using System.Windows;

namespace Everylang.Note.SettingsApp
{
    public class SettingsMiminoteManager
    {
        public static Window? OwnerWindow { get; set; }

        public static AppSettings AppSettings = null!;

        private static LiteDatabase? _liteDb; // LiteDatabase

        public static LiteDatabase LiteDb
        {
            get
            {
                if (_liteDb == null)
                {
                    _liteDb = new(DbConStr);
                }
                return _liteDb;
            }
        }

        public static string UserDbFolderPath { get; set; }

        public static string DbFilePath
        {
            get
            {
                if (string.IsNullOrEmpty(UserDbFolderPath) || !Directory.Exists(UserDbFolderPath))
                {
                    RegistryKey reg = Registry.CurrentUser.CreateSubKey("Software\\EveryLang\\");
                    string? value = reg.GetValue("DataFilePath") as string;
                    if (!string.IsNullOrEmpty(value))
                    {
                        var appDataPath = value;
                        if (!Directory.Exists(appDataPath))
                        {
                            Directory.CreateDirectory(appDataPath);
                        }
                        return Path.Combine(appDataPath, "everynote.db");
                    }
                    return Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "EveryLang");
                }
                else
                {
                    return Path.Combine(UserDbFolderPath, "everynote.db");
                }
            }
        }

        internal static ConnectionString DbConStr
        {
            get
            {
                var connString = new ConnectionString
                {
                    Filename = DbFilePath,
                    Connection = ConnectionType.Direct,
                    Password = "n89yr9873y48r983rxhdbhbuueuhuh8",
                    Upgrade = true
                };
                return connString;
            }
        }

        public static void ChangeLocationForDbFile(string newLocation)
        {
            if (Directory.Exists(newLocation))
            {
                LiteDb.Dispose();
                File.Move(DbFilePath, Path.Combine(newLocation, "Everynote.db"));
                UserDbFolderPath = newLocation;
            }
        }

        public static void SaveSettings()
        {
            SettingsMiminoteDataManager.SaveSettings(AppSettings);
        }

        public static void LoadSettings()
        {
            AppSettings = new AppSettings();
            SettingsMiminoteDataManager.GetSettings(AppSettings);
        }

        public static void Dispose()
        {
            LiteDb.Dispose();
            _liteDb = null;
        }
    }
}
