﻿using Everylang.App.Annotations;
using Everylang.App.Callback;
using Everylang.App.Translator.Bing;
using Everylang.App.Translator.Deepl;
using Everylang.App.Translator.Google;
using Everylang.App.Translator.Microsoft;
using Everylang.App.Translator.NetRequest;
using Everylang.App.Translator.Yandex;
using NAudio.Wave;
using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace Everylang.App.Translator
{
    class Translation
    {
        internal async void GetListening(string? text, string? lang)
        {
            try
            {
                var ms = await Task.Run(() => GoogleTranslator.GetListening(text, lang));
                if (ms != null)
                {
                    ms.Position = 0;
                    using WaveStream blockAlignedStream =
                        new BlockAlignReductionStream(
                            WaveFormatConversionStream.CreatePcmStream(
                                new Mp3FileReader(ms)));
                    using WaveOut waveOut = new WaveOut(WaveCallbackInfo.FunctionCallback());
                    waveOut.Init(blockAlignedStream);
                    waveOut.Play();
                    while (waveOut.PlaybackState == PlaybackState.Playing)
                    {
                        Application.Current.Dispatcher.Invoke(DispatcherPriority.Background,
                            new Action(delegate { }));
                        Thread.Sleep(20);
                    }
                }
            }
            catch
            {
                // Ignored
            }
        }

        [CanBeNull]
        internal string GetGoogleTranslation(string langFrom, string langTo, string text)
        {
            try
            {
                RequestSettings requestSettings = new RequestSettings();
                requestSettings.SourceTextTrimmed = text;
                requestSettings.LanguageFromCurrent = new Language() { Abbreviation = langFrom };
                requestSettings.LanguageToCurrent = new Language() { Abbreviation = langTo };
                var webResult = new GoogleTranslator().Translate(requestSettings);
                return webResult?.ResultTextWithNonChar;
            }
            catch
            {
                return null;
            }
        }

        private bool _nowGetTranslate;
        private int _lastTranslateSource;
        internal event Action<WebResultTranslator>? CallBackTranslate;

        internal async void GetTranslation(RequestSettings? requestSettings)
        {
            if (_nowGetTranslate && _lastTranslateSource == requestSettings?.CurrentTranslateServiceIndex)
            {
                requestSettings.IsNowTranslating = false;
                return;
            }
            _nowGetTranslate = true;
            if (requestSettings != null)
            {
                _lastTranslateSource = requestSettings.CurrentTranslateServiceIndex;

                if (string.IsNullOrEmpty(requestSettings.SourceText))
                {
                    _nowGetTranslate = false;
                    requestSettings.IsNowTranslating = false;
                    return;
                }

                GlobalEventsApp.OnEventAddNewHistory(requestSettings.SourceText);
                requestSettings.StartNonCharList = "";
                requestSettings.EndNonCharList = "";
                for (var i = 0; i < requestSettings.SourceText.Length; i++)
                {
                    if (char.IsWhiteSpace(requestSettings.SourceText[i]))
                    {
                        requestSettings.StartNonCharList += requestSettings.SourceText[i];
                    }
                    else
                    {
                        break;
                    }
                }

                for (var i = requestSettings.SourceText.Length - 1; i >= 0; i--)
                {
                    if (char.IsWhiteSpace(requestSettings.SourceText[i]))
                    {
                        requestSettings.EndNonCharList += requestSettings.SourceText[i];
                    }
                    else
                    {
                        break;
                    }
                }

                requestSettings.SourceTextTrimmed = requestSettings.SourceText.Trim();

                requestSettings.IsOneWord = IsContainsOnlyLettes(requestSettings.SourceTextTrimmed);
                var webResult = new WebResultTranslator();
                try
                {
                    if (requestSettings.CurrentTranslateServiceIndex == 0)
                    {
                        webResult = await Task.Run(() => new GoogleTranslator().Translate(requestSettings));
                    }

                    if (requestSettings.CurrentTranslateServiceIndex == 1)
                    {
                        webResult = await Task.Run(() => new BingTranslator().Translate(requestSettings));
                    }

                    if (requestSettings.CurrentTranslateServiceIndex == 2)
                    {
                        webResult = await Task.Run(() => new YandexTranslator().Translate(requestSettings));
                    }

                    if (requestSettings.CurrentTranslateServiceIndex == 3)
                    {
                        webResult = await Task.Run(() => new DeepLTranslator().Translate(requestSettings));
                    }

                    if (requestSettings.CurrentTranslateServiceIndex == 4)
                    {
                        webResult = await Task.Run(() => new MicrosoftTranslator().Translate(requestSettings));
                    }


                }
                catch
                {
                    // Ignored
                }


                if (webResult != null && !webResult.WithError && webResult.ResultText == "")
                {
                    webResult = new WebResultTranslator() { WithError = true, ErrorText = "Connecting Error" };
                }

                requestSettings.LastSourceText = requestSettings.SourceText;
                requestSettings.TranslatedText = webResult?.ResultText;
                requestSettings.TranslatedTextLatin = webResult?.LatinText;
                CallBackTranslate?.Invoke(webResult ?? new WebResultTranslator() { WithError = true, ErrorText = "Translate Error" });
            }

            _nowGetTranslate = false;
        }

        //internal static void GetBingAppId()
        //{
        //    Task.Run(() =>
        //    {
        //        new BingTranslator().GetBingAppId();
        //    });

        //}

        private bool IsContainsOnlyLettes(string text)
        {
            foreach (var t in text)
            {
                if (!char.IsLetter(t))
                {
                    return false;
                }
            }
            return true;
        }
    }
}
