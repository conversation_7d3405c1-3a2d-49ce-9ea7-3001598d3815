﻿<Popup x:Class="Everylang.App.View.Controls.Ocr.OcrWaitWindow"
       xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
       xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
       xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
       xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
       xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
       mc:Ignorable="d" x:Name="me" PopupAnimation="Slide"
      Height="150" Width="200" Placement="Absolute"
       StaysOpen="True" Focusable="False" x:ClassModifier="internal">

    <Popup.Resources>
        <ResourceDictionary>

            <Style x:Key="ProgressRingStyle" TargetType="telerik:RadBusyIndicator" BasedOn="{StaticResource {x:Type telerik:RadBusyIndicator}}">
                <Setter Property="HorizontalAlignment" Value="Center" />
                <Setter Property="IsBusy" Value="{Binding IsVisibleResult, ElementName=me, Converter={StaticResource InvertedBooleanConverter}}"/>
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsVisibleResult, ElementName=me}" Value="True">
                        <Setter Property="Visibility" Value="Hidden"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsVisibleResult, ElementName=me}" Value="False">
                        <Setter Property="Visibility" Value="Visible"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="LabelStyle" TargetType="telerik:Label" BasedOn="{StaticResource {x:Type telerik:Label}}">
                <Setter Property="VerticalAlignment" Value="Center" />
                <Setter Property="HorizontalAlignment" Value="Center" />
                <Setter Property="Visibility" Value="Hidden" />
                <Style.Triggers>
                    <DataTrigger Binding="{Binding Path=IsVisibleResult, ElementName=me}" Value="True">
                        <Setter Property="Visibility" Value="Visible" />

                    </DataTrigger>
                </Style.Triggers>
            </Style>


        </ResourceDictionary>
    </Popup.Resources>

    <Grid Background="{telerik:Windows11Resource ResourceKey=OverlayBrush}">
        <telerik:RadBusyIndicator Style="{StaticResource ProgressRingStyle}" BusyContent="{telerik:LocalizableResource Key=Loading}" Grid.ZIndex="1" IsIndeterminate="True"/>
        <telerik:Label x:Name="LabelMu" Style="{StaticResource LabelStyle}" FontSize="17"/>
    </Grid>

</Popup>



