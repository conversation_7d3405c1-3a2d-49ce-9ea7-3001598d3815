<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="About" xml:space="preserve">
    <value>Sobre o programa</value>
  </data>
  <data name="AvailableNewVersion" xml:space="preserve">
    <value>Nova versão disponível, reinicie o aplicativo</value>
  </data>
  <data name="ClearButton" xml:space="preserve">
    <value>Claro</value>
  </data>
  <data name="FirstStartWithEvaluate" xml:space="preserve">
    <value>Período de teste ativado por 40 dias, todos os recursos incluídos</value>
  </data>
  <data name="CloseHeaderButton" xml:space="preserve">
    <value>Fechar</value>
  </data>
  <data name="CopyButton" xml:space="preserve">
    <value>Cópia</value>
  </data>
  <data name="CopyButtonHtml" xml:space="preserve">
    <value>Copiar HTML</value>
  </data>
  <data name="CopyButtonRtf" xml:space="preserve">
    <value>Copiar RTF</value>
  </data>
  <data name="FastActionIndex" xml:space="preserve">
    <value>Clique no número para inserir texto</value>
  </data>
  <data name="BreakInterButton" xml:space="preserve">
    <value>Dividir texto em caractere de nova linha</value>
  </data>
  <data name="BreakSpaceButton" xml:space="preserve">
    <value>Dividir texto por espaço</value>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>Saída</value>
  </data>
  <data name="FixWindow" xml:space="preserve">
    <value>Janela congelada</value>
  </data>
  <data name="FromLang" xml:space="preserve">
    <value>Do idioma:</value>
  </data>
  <data name="ShowHistoryButton" xml:space="preserve">
    <value>Mostrar histórico</value>
  </data>
  <data name="HideHeaderButton" xml:space="preserve">
    <value>Colapso</value>
  </data>
  <data name="MaxHeaderButton" xml:space="preserve">
    <value>Expandir</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Sim</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Não</value>
  </data>
  <data name="PastButton" xml:space="preserve">
    <value>Inserir</value>
  </data>
  <data name="GoToMainWindowButton" xml:space="preserve">
    <value>Abra a janela principal com tradução</value>
  </data>
  <data name="SiteSourceTextButton" xml:space="preserve">
    <value>Abra o site do serviço de tradução</value>
  </data>
  <data name="OpenMainWindowShortcut" xml:space="preserve">
    <value>Atalho de teclado para abrir a janela principal</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Configurações</value>
  </data>
  <data name="ListOnMainWindow" xml:space="preserve">
    <value>Abrir lista na janela principal</value>
  </data>
  <data name="StopWorkingShortcut" xml:space="preserve">
    <value>Atalho de teclado para desativar todos os recursos</value>
  </data>
  <data name="WorkingOff" xml:space="preserve">
    <value>Desative o programa</value>
  </data>
  <data name="WorkingOn" xml:space="preserve">
    <value>Habilitar programa</value>
  </data>
  <data name="WorkingOffTitle" xml:space="preserve">
    <value>O programa está desabilitado</value>
  </data>
  <data name="StayOnTopButton" xml:space="preserve">
    <value>Não feche</value>
  </data>
  <data name="LatinButton" xml:space="preserve">
    <value>Em latim</value>
  </data>
  <data name="ToLang" xml:space="preserve">
    <value>Língua:</value>
  </data>
  <data name="TranslateButton" xml:space="preserve">
    <value>Traduzir</value>
  </data>
  <data name="TranslateError" xml:space="preserve">
    <value>Ocorreu um erro durante o processo de tradução. Tente novamente ou selecione outro serviço de tradução</value>
  </data>
  <data name="SoundButton" xml:space="preserve">
    <value>Ouvir</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Atualizar</value>
  </data>
  <data name="SettingHeaderButton" xml:space="preserve">
    <value>CONFIGURAÇÕES</value>
  </data>
  <data name="HistoryHeaderButton" xml:space="preserve">
    <value>HISTÓRIA</value>
  </data>
  <data name="ClipboardHeaderButton" xml:space="preserve">
    <value>PRANCHETA</value>
  </data>
  <data name="DiaryHeaderButton" xml:space="preserve">
    <value>DIÁRIO</value>
  </data>
  <data name="SnippetsHeaderButton" xml:space="preserve">
    <value>TRECHOS</value>
  </data>
  <data name="ProHeaderButton" xml:space="preserve">
    <value>Ativar PRO</value>
  </data>
  <data name="HistoryMenuItem" xml:space="preserve">
    <value>História</value>
  </data>
  <data name="ClipboardMenuItem" xml:space="preserve">
    <value>Área de transferência</value>
  </data>
  <data name="HotkeysMenuItem" xml:space="preserve">
    <value>Teclas de atalho</value>
  </data>
  <data name="DiaryMenuItem" xml:space="preserve">
    <value>Diário</value>
  </data>
  <data name="ErrorHotkey" xml:space="preserve">
    <value>Não é possível registrar a combinação de teclas</value>
  </data>
  <data name="UpdateAvailable" xml:space="preserve">
    <value>Atualização disponível, reinicie o aplicativo</value>
  </data>
  <data name="MinimizedText" xml:space="preserve">
    <value>O aplicativo está em execução e minimizado</value>
  </data>
  <data name="AppUpdated" xml:space="preserve">
    <value>O aplicativo foi atualizado, a lista de alterações está no site</value>
  </data>
  <data name="AppNotAsAdmin" xml:space="preserve">
    <value>Para que o programa funcione corretamente com todos os aplicativos, você deve executá-lo como administrador</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="ErrorText" xml:space="preserve">
    <value>Ocorreu um erro e o aplicativo será encerrado. Por favor, envie texto com <NAME_EMAIL></value>
  </data>
  <data name="InputLanguagesError" xml:space="preserve">
    <value>Seu computador possui mais de um layout de teclado instalado para alguns idiomas, o que pode afetar negativamente a operação correta do recurso de troca de layout.</value>
  </data>
  <data name="SetFont" xml:space="preserve">
    <value>Fonte</value>
  </data>
  <data name="SearchText" xml:space="preserve">
    <value>Encontrar...</value>
  </data>
  <data name="OnlyPro" xml:space="preserve">
    <value>(versão PRO)</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Mudar</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Salvar</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Excluir</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>Criar</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Todos</value>
  </data>
  <data name="AllApp" xml:space="preserve">
    <value>Todos os programas</value>
  </data>
  <data name="OnlyInProVersion" xml:space="preserve">
    <value>Somente na versão PRO</value>
  </data>
  <data name="SystemTrayHide" xml:space="preserve">
    <value>O programa é minimizado para a bandeja</value>
  </data>
  <data name="StartWindowTitle" xml:space="preserve">
    <value>Antes de usar o programa, leia a documentação</value>
  </data>
  <data name="FastActionTextWindowSearch" xml:space="preserve">
    <value>Guia - Pesquisa.  Esc - Cancelar e limpar</value>
  </data>
  <data name="noun" xml:space="preserve">
    <value>Substantivo</value>
  </data>
  <data name="pronoun" xml:space="preserve">
    <value>Pronome</value>
  </data>
  <data name="adjective" xml:space="preserve">
    <value>Adjetivo</value>
  </data>
  <data name="verb" xml:space="preserve">
    <value>Verbo</value>
  </data>
  <data name="adverb" xml:space="preserve">
    <value>Advérbio</value>
  </data>
  <data name="preposition" xml:space="preserve">
    <value>Pretexto</value>
  </data>
  <data name="conjunction" xml:space="preserve">
    <value>União</value>
  </data>
  <data name="interjection" xml:space="preserve">
    <value>Interjeição</value>
  </data>
  <data name="participle" xml:space="preserve">
    <value>Comunhão</value>
  </data>
  <data name="auxiliary verb" xml:space="preserve">
    <value>Verbo auxiliar</value>
  </data>
  <data name="parenthetic" xml:space="preserve">
    <value>Palavra introdutória</value>
  </data>
  <data name="SpellCheckHeader" xml:space="preserve">
    <value>Verificação ortográfica</value>
  </data>
  <data name="LabelNoErrors" xml:space="preserve">
    <value>Sem erros</value>
  </data>
  <data name="LangNotCorrect" xml:space="preserve">
    <value>Idioma não suportado</value>
  </data>
  <data name="LabelError" xml:space="preserve">
    <value>Ocorreu um erro</value>
  </data>
  <data name="LabelTextTooLong" xml:space="preserve">
    <value>O texto é muito longo</value>
  </data>
  <data name="ButtonClose" xml:space="preserve">
    <value>Fechar</value>
  </data>
  <data name="LabelOptions" xml:space="preserve">
    <value>Opções:</value>
  </data>
  <data name="LabelOptionsNo" xml:space="preserve">
    <value>Sem opções</value>
  </data>
  <data name="LabelOptionsEnd" xml:space="preserve">
    <value>Verificação concluída</value>
  </data>
  <data name="bSkip" xml:space="preserve">
    <value>Pular</value>
  </data>
  <data name="bSkipAll" xml:space="preserve">
    <value>Pular tudo</value>
  </data>
  <data name="bReplace" xml:space="preserve">
    <value>Substituir</value>
  </data>
  <data name="bReplaceAll" xml:space="preserve">
    <value>Substitua tudo</value>
  </data>
  <data name="bReplaceText" xml:space="preserve">
    <value>Inserir texto</value>
  </data>
  <data name="bCopy" xml:space="preserve">
    <value>Cópia</value>
  </data>
  <data name="buttonBack" xml:space="preserve">
    <value>Retornar</value>
  </data>
  <data name="SetDefaultSetting" xml:space="preserve">
    <value>Redefinir configurações</value>
  </data>
  <data name="InterKeyboardShortcuts" xml:space="preserve">
    <value>Pressione a combinação de teclas</value>
  </data>
  <data name="Russian" xml:space="preserve">
    <value>russo</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>Inglês</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>Francês</value>
  </data>
  <data name="Italian" xml:space="preserve">
    <value>italiano</value>
  </data>
  <data name="Ukrainian" xml:space="preserve">
    <value>ucraniano</value>
  </data>
  <data name="AboutSettingsHeader" xml:space="preserve">
    <value>SOBRE O PROGRAMA</value>
  </data>
  <data name="AboutSettingsLicense" xml:space="preserve">
    <value>Contrato de licença</value>
  </data>
  <data name="AboutSettingsDesc" xml:space="preserve">
    <value>Um assistente universal para trabalhar com texto em diferentes idiomas</value>
  </data>
  <data name="AboutSettingsVersion" xml:space="preserve">
    <value>Versão:</value>
  </data>
  <data name="AboutSettingsUpdateAvailable" xml:space="preserve">
    <value>Nova versão do programa está disponível</value>
  </data>
  <data name="AboutSettingsUpdate" xml:space="preserve">
    <value>Atualizar</value>
  </data>
  <data name="AboutSettingsContactHeader" xml:space="preserve">
    <value>OPINIÃO</value>
  </data>
  <data name="AboutSettingsContactText" xml:space="preserve">
    <value>Escreva seus comentários e/ou perguntas</value>
  </data>
  <data name="AboutResetSettings" xml:space="preserve">
    <value>Redefinir todas as configurações do programa</value>
  </data>
  <data name="AboutOpenStartWindow" xml:space="preserve">
    <value>Abrir janela de boas-vindas</value>
  </data>
  <data name="AboutResetSettingsQuestion" xml:space="preserve">
    <value>Redefinir todas as configurações e dados do programa?</value>
  </data>
  <data name="AboutSettingsContactLink" xml:space="preserve">
    <value>Formulário de contato</value>
  </data>
  <data name="AboutSettingsIsAdmin" xml:space="preserve">
    <value>O programa está sendo executado como administrador</value>
  </data>
  <data name="AboutSettingsIsNotAdmin" xml:space="preserve">
    <value>O programa não está sendo executado como administrador</value>
  </data>
  <data name="AboutSettingsAckowledgementsHeader" xml:space="preserve">
    <value>COMPONENTES</value>
  </data>
  <data name="SwitcherSettingsHeader" xml:space="preserve">
    <value>Mudando o layout</value>
  </data>
  <data name="SwitcherSettingsKeyboardShortcutsSwitch" xml:space="preserve">
    <value>Mudar o layout da última palavra</value>
  </data>
  <data name="SwitcherSettingsIsUseBreak" xml:space="preserve">
    <value>Usar pausa</value>
  </data>
  <data name="SwitcherSettingsKeyboardShortcutsSwitchSelected" xml:space="preserve">
    <value>Alterar o layout do texto selecionado</value>
  </data>
  <data name="SwitcherSettingsMethodSelect" xml:space="preserve">
    <value>Selecionando um método de troca de layout</value>
  </data>
  <data name="SwitcherSettingsSwitchMethod1" xml:space="preserve">
    <value>Emulação de teclas para troca de layout</value>
  </data>
  <data name="SwitcherSettingsSwitchMethod2" xml:space="preserve">
    <value>Executando um comando do Windows</value>
  </data>
  <data name="SwitcherSettingsLeaveTextSelectedAfterSwitch" xml:space="preserve">
    <value>Manter o texto selecionado após mudar de layout</value>
  </data>
  <data name="SwitcherSettingsIsOn" xml:space="preserve">
    <value>Troca de layout ativada</value>
  </data>
  <data name="SwitcherSettingsSwitcherCtrlNumberIsOn" xml:space="preserve">
    <value>Use Ctrl+(número) para mudar para um idioma específico</value>
  </data>
  <data name="SwitcherSettingsIsUseShift" xml:space="preserve">
    <value>Use a tecla Shift de clique duplo</value>
  </data>
  <data name="SwitcherSettingsIsOnInsert" xml:space="preserve">
    <value>Mudar do início da linha</value>
  </data>
  <data name="SwitcherSettingsIsUseScrollLock" xml:space="preserve">
    <value>Use o botão ScrollLock clicando duas vezes</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOn" xml:space="preserve">
    <value>Alternar layout por botão</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnStandart" xml:space="preserve">
    <value>Configurações do sistema</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRCtrl" xml:space="preserve">
    <value>Ctrl direito</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLCtrl" xml:space="preserve">
    <value>Ctrl esquerdo</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRShift" xml:space="preserve">
    <value>Mudança para a direita</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLShift" xml:space="preserve">
    <value>Deslocamento Esquerdo</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLRCtrl" xml:space="preserve">
    <value>Ctrl direito ou esquerdo</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLRShift" xml:space="preserve">
    <value>Shift direito ou esquerdo</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRCtrlOrCapsLock" xml:space="preserve">
    <value>Ctrl direito ou CapsLock</value>
  </data>
  <data name="SwitcherSettingsToolTipForCurrentSwitchOnKey" xml:space="preserve">
    <value>Para ativar ou desativar a função CapsLock, pressione Shift direito e esquerdo simultaneamente</value>
  </data>
  <data name="SwitcherSettingsSwitcherSountIsOn" xml:space="preserve">
    <value>Som de mudança de layout</value>
  </data>
  <data name="SwitcherSettingsSoundEdit" xml:space="preserve">
    <value>Configurando o som para alternar layouts</value>
  </data>
  <data name="SwitcherSettingsTrueListOfLang" xml:space="preserve">
    <value>Lista de idiomas para os quais o layout mudará</value>
  </data>
  <data name="SwitcherLangAndKeysForSwitch" xml:space="preserve">
    <value>Configurando as teclas para mudar para um idioma específico</value>
  </data>
  <data name="SwitcherSettingsAskToDeactivateAutoswitcherOff" xml:space="preserve">
    <value>Desativar a troca automática de layout?</value>
  </data>
  <data name="SwitcherSettingsAskToDeactivateAutoswitcherOn" xml:space="preserve">
    <value>Ativar a troca automática de layout?</value>
  </data>
  <data name="AutoSwitcherSettingsHeader" xml:space="preserve">
    <value>Troca automática de layout</value>
  </data>
  <data name="SwitcherSettingsHeaderAuto" xml:space="preserve">
    <value>Troca automática habilitada</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnTwoUpperCaseLetters" xml:space="preserve">
    <value>Corrija duas letras maiúsculas no início de uma palavra</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnFixWrongUpperCase" xml:space="preserve">
    <value>Corrigir pressionamento acidental de CapsLock</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnUpperCaseNotSwitch" xml:space="preserve">
    <value>Não mude se todas as letras de uma palavra estiverem em maiúsculas</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnByEnter" xml:space="preserve">
    <value>Corrija o layout após pressionar a tecla Enter</value>
  </data>
  <data name="AutoSwitcherSettingsIsSwitchOneLetter" xml:space="preserve">
    <value>Adicione palavras de uma letra às regras</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnAddingRule" xml:space="preserve">
    <value>Adicionar regras de troca automaticamente</value>
  </data>
  <data name="AutoSwitcherSettingsDisableAutoSwitchAfterManualSwitch" xml:space="preserve">
    <value>Não corrija o layout se ele foi alterado manualmente anteriormente</value>
  </data>
  <data name="AutoSwitcherSettingsOnlyAfterSeparator" xml:space="preserve">
    <value>Mudar de layout somente após inserir uma palavra inteira</value>
  </data>
  <data name="AutoSwitcherSettingsAfterPause" xml:space="preserve">
    <value>Mudar de layout depois de parar de digitar</value>
  </data>
  <data name="AutoSwitcherSettingsResetRule" xml:space="preserve">
    <value>Exclua todas as regras de troca automática</value>
  </data>
  <data name="AutoSwitcherSettingsCombination" xml:space="preserve">
    <value>Texto</value>
  </data>
  <data name="AutoSwitcherSettingsAllLayouts" xml:space="preserve">
    <value>Todos os layouts</value>
  </data>
  <data name="AutoSwitcherSettingsAction" xml:space="preserve">
    <value>Ação</value>
  </data>
  <data name="AutoSwitcherSettingsShowAcceptWindow" xml:space="preserve">
    <value>Adicione regras somente após confirmação</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionConvert" xml:space="preserve">
    <value>Trocar</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionNotConvert" xml:space="preserve">
    <value>Não mude</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionIntermediate" xml:space="preserve">
    <value>Candidato</value>
  </data>
  <data name="AutoSwitcherSettingsHelpWindowTitle" xml:space="preserve">
    <value>Regras de troca automática</value>
  </data>
  <data name="AutoSwitcherSettingsTrueListOfLang" xml:space="preserve">
    <value>Lista de idiomas para os quais a troca automática funcionará</value>
  </data>
  <data name="AutoSwitcherSettingsListRulesHeader" xml:space="preserve">
    <value>Lista de regras para troca automática</value>
  </data>
  <data name="AutoSwitcherSettingsListRulesHeaderShowAll" xml:space="preserve">
    <value>Mostrar candidatos</value>
  </data>
  <data name="AutoSwitcherSettingsOpenRulesList" xml:space="preserve">
    <value>Abra a lista de regras de troca automática</value>
  </data>
  <data name="AutoSwitcherSettingsCountCheckRule" xml:space="preserve">
    <value>Número de trocas manuais do layout de palavras para inclusão nas regras</value>
  </data>
  <data name="AutoSwitchAcceptText" xml:space="preserve">
    <value>Adicionar uma palavra às regras de troca automática? Digite - SIM</value>
  </data>
  <data name="IsLangInfoWindowShowForMouse" xml:space="preserve">
    <value>Idioma de entrada atual no ponteiro do mouse</value>
  </data>
  <data name="IsLangInfoWindowShowForCaret" xml:space="preserve">
    <value>Idioma de entrada atual no cursor de texto</value>
  </data>
  <data name="IsLangInfoWindowShowLargeWindow" xml:space="preserve">
    <value>Janela separada do indicador de idioma</value>
  </data>
  <data name="IsLangInfoInTray" xml:space="preserve">
    <value>Idioma atual na bandeja do sistema</value>
  </data>
  <data name="IsLangInfoWindowShowForCaretEx" xml:space="preserve">
    <value>Recursos avançados</value>
  </data>
  <data name="IsLangInfoShowIconsImage" xml:space="preserve">
    <value>Mostrar bandeira do país</value>
  </data>
  <data name="IsLangInfoShowIconsText" xml:space="preserve">
    <value>Mostrar nome do idioma</value>
  </data>
  <data name="OpacityIconLangInfo" xml:space="preserve">
    <value>Transparência do indicador</value>
  </data>
  <data name="SizeIconLangInfo" xml:space="preserve">
    <value>Aumentando o tamanho do indicador como uma porcentagem</value>
  </data>
  <data name="IsIndicateCurrentLangInKeyboardLed" xml:space="preserve">
    <value>Indicação do idioma atual no teclado</value>
  </data>
  <data name="PosMouse" xml:space="preserve">
    <value>Posição do indicador no ponteiro do mouse</value>
  </data>
  <data name="IsHideIndicateInFullScreenApp" xml:space="preserve">
    <value>Ocultar o indicador em programas em tela cheia</value>
  </data>
  <data name="IsIndicateCapsLockState" xml:space="preserve">
    <value>Mostrar status do CapsLock</value>
  </data>
  <data name="StatusButtonCapsLockIsOn" xml:space="preserve">
    <value>CapsLock ativado</value>
  </data>
  <data name="PosCarret" xml:space="preserve">
    <value>Posição do indicador no cursor de texto</value>
  </data>
  <data name="SpellcheckingSettingsHeader" xml:space="preserve">
    <value>Verificação ortográfica</value>
  </data>
  <data name="SpellcheckingSettingsIsOn" xml:space="preserve">
    <value>Verificação ortográfica ativada</value>
  </data>
  <data name="SpellcheckingSettingsWhileTyping" xml:space="preserve">
    <value>Verifique a ortografia enquanto digita</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingSoundOn" xml:space="preserve">
    <value>Som de verificação ortográfica ao digitar</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingUseNumber" xml:space="preserve">
    <value>Use números para substituição rápida</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingSoundEdit" xml:space="preserve">
    <value>Configurações de som para verificação ortográfica</value>
  </data>
  <data name="SpellcheckingKeyboardShortcuts" xml:space="preserve">
    <value>Atalho de teclado para verificar a ortografia do texto selecionado</value>
  </data>
  <data name="SpellcheckingKeyboardShortcutsShort" xml:space="preserve">
    <value>Verifique a ortografia do texto selecionado</value>
  </data>
  <data name="SpellcheckingSettingsCloseByTimer" xml:space="preserve">
    <value>Feche a janela se não houver erros após 3 segundos</value>
  </data>
  <data name="ClipboardSettingsHeader" xml:space="preserve">
    <value>Gerenciador de área de transferência</value>
  </data>
  <data name="ClipboardKeyboardShortcuts" xml:space="preserve">
    <value>Cole o texto sem formatação e cole o caminho do arquivo copiado</value>
  </data>
  <data name="ClipboardOn" xml:space="preserve">
    <value>Gerenciador da área de transferência ativado</value>
  </data>
  <data name="ClipboardKeyboardViewShortcuts" xml:space="preserve">
    <value>Abra o histórico da área de transferência usando o atalho do teclado</value>
  </data>
  <data name="ClipboardKeyboardRoundShortcuts" xml:space="preserve">
    <value>Colando sequencialmente texto do histórico da área de transferência para a janela atual</value>
  </data>
  <data name="ClipboardKeyboardRoundShortcutsShort" xml:space="preserve">
    <value>Colando sequencialmente texto do histórico da área de transferência</value>
  </data>
  <data name="ClipboardSettingsPasteByIndexIsOn" xml:space="preserve">
    <value>Cole o texto com Ctrl+Shift+(número) - número 1, 2, 3, 4, 5, 6, 7, 8, 9 - índice de entrada no histórico da área de transferência</value>
  </data>
  <data name="ClipboardSettingsSaveFilePath" xml:space="preserve">
    <value>Salve o caminho do arquivo copiado no histórico da área de transferência</value>
  </data>
  <data name="ClipboardSettingsSaveImage" xml:space="preserve">
    <value>Salvar histórico de buffer de imagem</value>
  </data>
  <data name="ClipboardSettingsReplaceWithoutChangeClipboard" xml:space="preserve">
    <value>Ao colar texto do histórico, substitua o valor atual na área de transferência</value>
  </data>
  <data name="ClipboardMaxClipboardItems" xml:space="preserve">
    <value>Tamanho do histórico da área de transferência</value>
  </data>
  <data name="ClipboardSound" xml:space="preserve">
    <value>Som de alteração da área de transferência</value>
  </data>
  <data name="ConverterSettingsHeader" xml:space="preserve">
    <value>Conversor de texto</value>
  </data>
  <data name="ConverterSettingsConvertDependsOnKeyboardLayout" xml:space="preserve">
    <value>Converter com base no layout atual do teclado</value>
  </data>
  <data name="ConverterSettingsExpression" xml:space="preserve">
    <value>Converta números e datas em strings, avalie expressões</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchCapsSettings" xml:space="preserve">
    <value>Configurar teclas de atalho</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsInvert" xml:space="preserve">
    <value>Inverter maiúsculas e minúsculas do texto selecionado</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsUp" xml:space="preserve">
    <value>Converter o texto selecionado em maiúsculas</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsDown" xml:space="preserve">
    <value>Texto selecionado em letras minúsculas</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsFirstLetterToDown" xml:space="preserve">
    <value>Coloque em minúscula o primeiro caractere da palavra sob o cursor</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsFirstLetterToUp" xml:space="preserve">
    <value>Coloque em maiúscula o primeiro caractere da palavra sob o cursor</value>
  </data>
  <data name="ConverterSettingsTransliteration" xml:space="preserve">
    <value>Transliterar texto selecionado</value>
  </data>
  <data name="ConverterSettingsCamelCase" xml:space="preserve">
    <value>Converter texto para estilo camelCase</value>
  </data>
  <data name="ConverterReplaceSelText" xml:space="preserve">
    <value>Encontre e substitua o texto no texto selecionado</value>
  </data>
  <data name="ConverterSettingsEncloseTextQuotationMarks" xml:space="preserve">
    <value>Enquadrar o texto selecionado com símbolos (exemplo)</value>
  </data>
  <data name="ConverterAdd" xml:space="preserve">
    <value>Adicionar</value>
  </data>
  <data name="ConverterStart" xml:space="preserve">
    <value>Esquerda</value>
  </data>
  <data name="ConverterEnd" xml:space="preserve">
    <value>Certo</value>
  </data>
  <data name="ConverterDelete" xml:space="preserve">
    <value>Excluir</value>
  </data>
  <data name="ConverterSampleText" xml:space="preserve">
    <value>texto de exemplo</value>
  </data>
  <data name="TransCloseHeaderButton" xml:space="preserve">
    <value>Fechar ESC</value>
  </data>
  <data name="TransReplaceTextButton" xml:space="preserve">
    <value>Substitua o texto ENTER</value>
  </data>
  <data name="TransSettingsHeader" xml:space="preserve">
    <value>Tradução</value>
  </data>
  <data name="TransSettingsNativeLanguage" xml:space="preserve">
    <value>Idioma padrão para traduzir</value>
  </data>
  <data name="TransSettingsLanguageFromTranslate" xml:space="preserve">
    <value>Idioma padrão para tradução</value>
  </data>
  <data name="TransSettingsMainLanguageForTheTranslation" xml:space="preserve">
    <value>Idioma principal para tradução</value>
  </data>
  <data name="TransSettingsProviderOfTranslation" xml:space="preserve">
    <value>Serviço de tradução</value>
  </data>
  <data name="TransSettingsKeyboardShortcuts" xml:space="preserve">
    <value>Atalho de teclado para traduzir o texto selecionado</value>
  </data>
  <data name="TransSettingsInterKeyboardShortcuts" xml:space="preserve">
    <value>Pressione a combinação de teclas</value>
  </data>
  <data name="TransSettingsUseDoubleCtrl" xml:space="preserve">
    <value>Clicar duas vezes no botão Ctrl</value>
  </data>
  <data name="TransSettingsTranslationIsAlways" xml:space="preserve">
    <value>Traduzir ao selecionar texto com o mouse</value>
  </data>
  <data name="TransSettingsIsOn" xml:space="preserve">
    <value>Tradução incluída</value>
  </data>
  <data name="GeneralSettingsHeader" xml:space="preserve">
    <value>Configurações gerais</value>
  </data>
  <data name="GeneralSettingsLanguageProgram" xml:space="preserve">
    <value>Linguagem da interface do programa</value>
  </data>
  <data name="GeneralSettingsLanguageProgramRestart" xml:space="preserve">
    <value>Reinicie o aplicativo para alterar o idioma?</value>
  </data>
  <data name="GeneralSettingsOther" xml:space="preserve">
    <value>Variado</value>
  </data>
  <data name="GeneralSettingsMinimizeToTray" xml:space="preserve">
    <value>Minimizar para a bandeja</value>
  </data>
  <data name="GeneralSettingsStartUpWithWindows" xml:space="preserve">
    <value>Correndo pelas janelas</value>
  </data>
  <data name="GeneralSettingsStartAdmin" xml:space="preserve">
    <value>Execute com direitos de administrador</value>
  </data>
  <data name="GeneralSettingsIsCheckUpdate" xml:space="preserve">
    <value>Verifique se há atualizações</value>
  </data>
  <data name="GeneralSettingsIsCheckUpdateBeta" xml:space="preserve">
    <value>Atualização para versão beta</value>
  </data>
  <data name="GeneralSettingsTheme" xml:space="preserve">
    <value>Assunto</value>
  </data>
  <data name="GeneralSettingsThemeDayNight" xml:space="preserve">
    <value>Dia ou noite</value>
  </data>
  <data name="GeneralSettingsThemeAccent" xml:space="preserve">
    <value>Estilos de design</value>
  </data>
  <data name="GeneralSettingsDataFilePath" xml:space="preserve">
    <value>Pasta para salvar as configurações do programa</value>
  </data>
  <data name="GeneralSettingsIsProxyUseIE" xml:space="preserve">
    <value>Use as configurações de proxy do sistema</value>
  </data>
  <data name="GeneralSettingsProxyServer" xml:space="preserve">
    <value>Digite o endereço do servidor</value>
  </data>
  <data name="GeneralSettingsProxyPort" xml:space="preserve">
    <value>Insira a porta</value>
  </data>
  <data name="GeneralSettingsProxyUsername" xml:space="preserve">
    <value>Digite seu nome de usuário</value>
  </data>
  <data name="GeneralSettingsProxyPassword" xml:space="preserve">
    <value>Digite sua senha</value>
  </data>
  <data name="GeneralSettingsSaveProxy" xml:space="preserve">
    <value>Salvar configurações de proxy</value>
  </data>
  <data name="GeneralSettingsProxy" xml:space="preserve">
    <value>Configurações de proxy</value>
  </data>
  <data name="GeneralSettingsProxyError" xml:space="preserve">
    <value>Erro nas configurações do servidor proxy, altere as configurações</value>
  </data>
  <data name="GeneralSettingsUseNightTheme" xml:space="preserve">
    <value>Use um tema sombrio à noite</value>
  </data>
  <data name="GeneralSettingsUseNightThemeStart" xml:space="preserve">
    <value>Horário noturno de</value>
  </data>
  <data name="GeneralSettingsUseNightThemeEnd" xml:space="preserve">
    <value>Horas da noite até</value>
  </data>
  <data name="GeneralSettingsIsStopWorkingFullScreen" xml:space="preserve">
    <value>Desative todas as funções em programas executados em modo de tela inteira</value>
  </data>
  <data name="GeneralSettingsImport" xml:space="preserve">
    <value>Importar configurações</value>
  </data>
  <data name="GeneralSettingsExport" xml:space="preserve">
    <value>Exportar configurações</value>
  </data>
  <data name="GeneralTab" xml:space="preserve">
    <value>Configurações básicas</value>
  </data>
  <data name="TranslationTab" xml:space="preserve">
    <value>Tradutor</value>
  </data>
  <data name="CheckSpellingTab" xml:space="preserve">
    <value>Verificação ortográfica</value>
  </data>
  <data name="LangFlagTab" xml:space="preserve">
    <value>Indicador de layout</value>
  </data>
  <data name="ProTabs" xml:space="preserve">
    <value>Recursos PRO</value>
  </data>
  <data name="ClipboardTab" xml:space="preserve">
    <value>Área de transferência</value>
  </data>
  <data name="DiareTab" xml:space="preserve">
    <value>Diário</value>
  </data>
  <data name="ConverterTab" xml:space="preserve">
    <value>Conversor de texto</value>
  </data>
  <data name="SwitcherTab" xml:space="preserve">
    <value>Alternando layouts</value>
  </data>
  <data name="AutoSwitcherTab" xml:space="preserve">
    <value>Interruptor automático</value>
  </data>
  <data name="ProgramsExceptionsTab" xml:space="preserve">
    <value>Programas de exceção</value>
  </data>
  <data name="ProgramsSetLayoutTab" xml:space="preserve">
    <value>Layouts padrão</value>
  </data>
  <data name="UniversalWindowTab" xml:space="preserve">
    <value>SmartClick</value>
  </data>
  <data name="AboutTab" xml:space="preserve">
    <value>Sobre o programa</value>
  </data>
  <data name="ProTab" xml:space="preserve">
    <value>Licença</value>
  </data>
  <data name="AutochangeTab" xml:space="preserve">
    <value>Trechos</value>
  </data>
  <data name="UniTranslate" xml:space="preserve">
    <value>Traduzir</value>
  </data>
  <data name="UniCopy" xml:space="preserve">
    <value>Cópia</value>
  </data>
  <data name="UniSpellCheck" xml:space="preserve">
    <value>Verifique a ortografia</value>
  </data>
  <data name="UniSearch" xml:space="preserve">
    <value>Pesquisa de chamadas</value>
  </data>
  <data name="UniLink" xml:space="preserve">
    <value>Abrir link no navegador</value>
  </data>
  <data name="UniLinkTranslate" xml:space="preserve">
    <value>Traduza o site usando o link</value>
  </data>
  <data name="UniLinkShorter" xml:space="preserve">
    <value>Gerando um link curto</value>
  </data>
  <data name="UniEmail" xml:space="preserve">
    <value>Crie uma mensagem de e-mail</value>
  </data>
  <data name="UniPaste" xml:space="preserve">
    <value>Inserir texto</value>
  </data>
  <data name="UniPasteUnf" xml:space="preserve">
    <value>Colar texto sem formatação</value>
  </data>
  <data name="UniClipboardHistory" xml:space="preserve">
    <value>Abrir histórico da área de transferência</value>
  </data>
  <data name="UniTranslateHistory" xml:space="preserve">
    <value>Abrir histórico de tradução</value>
  </data>
  <data name="UniDiaryHistory" xml:space="preserve">
    <value>Diário aberto</value>
  </data>
  <data name="UniUppercase" xml:space="preserve">
    <value>Alterar a caixa do texto selecionado</value>
  </data>
  <data name="UniAutochange" xml:space="preserve">
    <value>Abrir lista de snippets</value>
  </data>
  <data name="UniConverter" xml:space="preserve">
    <value>Conversor de texto</value>
  </data>
  <data name="UniDownCase" xml:space="preserve">
    <value>Texto selecionado em letras minúsculas</value>
  </data>
  <data name="UniUpCase" xml:space="preserve">
    <value>Converter o texto selecionado em maiúsculas</value>
  </data>
  <data name="UniInvertCase" xml:space="preserve">
    <value>Inverter maiúsculas e minúsculas do texto selecionado</value>
  </data>
  <data name="UniEnclose" xml:space="preserve">
    <value>Enquadrar o texto selecionado com símbolos</value>
  </data>
  <data name="UniTranslit" xml:space="preserve">
    <value>Transliterar texto selecionado</value>
  </data>
  <data name="UniConvertExpressions" xml:space="preserve">
    <value>Converta números e datas em strings, avalie expressões</value>
  </data>
  <data name="UniTextConverter" xml:space="preserve">
    <value>Conversor</value>
  </data>
  <data name="UniTextTranslate" xml:space="preserve">
    <value>Tradução</value>
  </data>
  <data name="UniTextCopy" xml:space="preserve">
    <value>Cópia</value>
  </data>
  <data name="UniTextSpellCheck" xml:space="preserve">
    <value>Ortografia</value>
  </data>
  <data name="UniTextSearch" xml:space="preserve">
    <value>Procurar</value>
  </data>
  <data name="UniTextLink" xml:space="preserve">
    <value>Link</value>
  </data>
  <data name="UniTextLinkTranslate" xml:space="preserve">
    <value>Tradução de sites</value>
  </data>
  <data name="UniTextLinkShorter" xml:space="preserve">
    <value>URL mais curto</value>
  </data>
  <data name="UniTextEmail" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="UniTextPaste" xml:space="preserve">
    <value>Inserir</value>
  </data>
  <data name="UniTextPasteUnf1" xml:space="preserve">
    <value>Inserção sem formato.</value>
  </data>
  <data name="UniTextPasteUnf2" xml:space="preserve">
    <value>sem formato.</value>
  </data>
  <data name="UniTextClipboardHistory1" xml:space="preserve">
    <value>Histórico de buffer</value>
  </data>
  <data name="UniTextClipboardHistory2" xml:space="preserve">
    <value>buffer</value>
  </data>
  <data name="UniTextDiaryHistory" xml:space="preserve">
    <value>Diário</value>
  </data>
  <data name="UniTextAutochange" xml:space="preserve">
    <value>Trechos</value>
  </data>
  <data name="UniTextDownCase" xml:space="preserve">
    <value>Registre-se</value>
  </data>
  <data name="UniTextUpCase" xml:space="preserve">
    <value>Cadastre-se</value>
  </data>
  <data name="UniTextInvertCase" xml:space="preserve">
    <value>Inverter registro</value>
  </data>
  <data name="UniTextEnclose" xml:space="preserve">
    <value>Enquadramento de texto</value>
  </data>
  <data name="UniTextTranslit" xml:space="preserve">
    <value>Transliteração</value>
  </data>
  <data name="UniTextConvertExpressions" xml:space="preserve">
    <value>Esteira. expressões</value>
  </data>
  <data name="HistoryDateStart" xml:space="preserve">
    <value>Começar:</value>
  </data>
  <data name="HistoryDateEnd" xml:space="preserve">
    <value>Fim:</value>
  </data>
  <data name="HistoryDelSelected" xml:space="preserve">
    <value>Excluir</value>
  </data>
  <data name="HistoryClear" xml:space="preserve">
    <value>Claro</value>
  </data>
  <data name="HistoryToggleSwitch" xml:space="preserve">
    <value>Incluído</value>
  </data>
  <data name="DiaryHeaderText" xml:space="preserve">
    <value>TEXTO</value>
  </data>
  <data name="DiaryHeaderApp" xml:space="preserve">
    <value>APLICATIVO</value>
  </data>
  <data name="ProgramsExceptionsHeader" xml:space="preserve">
    <value>Programas de exceção</value>
  </data>
  <data name="ProgramsExceptionsAddNew" xml:space="preserve">
    <value>Adicionar</value>
  </data>
  <data name="ProgramsExceptionsAddNewExeFile" xml:space="preserve">
    <value>Adicionar arquivo exe</value>
  </data>
  <data name="ProgramsExceptionsAddNewFilesFromFolder" xml:space="preserve">
    <value>Adicionar pasta</value>
  </data>
  <data name="ProgramsExceptionsAddNewTitle" xml:space="preserve">
    <value>Adicionar por título da janela</value>
  </data>
  <data name="ProgramsExceptionsIsOnLayoutFlag" xml:space="preserve">
    <value>Indicador de layout</value>
  </data>
  <data name="ProgramsExceptionsIsOnLayoutSwitcher" xml:space="preserve">
    <value>Alternando layouts</value>
  </data>
  <data name="ProgramsExceptionsIsOnSmartClick" xml:space="preserve">
    <value>SmartClick</value>
  </data>
  <data name="ProgramsExceptionsIsOnAutochange" xml:space="preserve">
    <value>Trechos</value>
  </data>
  <data name="ProgramsExceptionsIsOnHotKeys" xml:space="preserve">
    <value>Teclas de atalho</value>
  </data>
  <data name="ProgramsExceptionsIsOnAutoSwitch" xml:space="preserve">
    <value>Troca automática de idioma</value>
  </data>
  <data name="ProgramsExceptionsIsOnDiary" xml:space="preserve">
    <value>Diário de entrada de texto</value>
  </data>
  <data name="ProgramsExceptionsIsOnConverter" xml:space="preserve">
    <value>Conversor e caixa de texto</value>
  </data>
  <data name="ProgramsExceptionsIsOnClipboard" xml:space="preserve">
    <value>Histórico da área de transferência</value>
  </data>
  <data name="ProgramsExceptionsIsOnClipboardImage" xml:space="preserve">
    <value>Salvar imagens</value>
  </data>
  <data name="ProgramsExceptionsDelete" xml:space="preserve">
    <value>Excluir</value>
  </data>
  <data name="ProgramsExceptionsAddNewHelp" xml:space="preserve">
    <value>Clique no programa desejado</value>
  </data>
  <data name="ProgramsExceptionsCurrentInfoTooltip" xml:space="preserve">
    <value>Marque as funções que funcionarão para o programa</value>
  </data>
  <data name="ProgramsExceptionsProgramsList" xml:space="preserve">
    <value>Adicionando da lista de programas</value>
  </data>
  <data name="ProgramsExceptionsFromPoint" xml:space="preserve">
    <value>Adicionando com o cursor</value>
  </data>
  <data name="ProgramsSetLayoutHeader" xml:space="preserve">
    <value>Linguagens de programa</value>
  </data>
  <data name="ProgramsSetLayoutTabHeader" xml:space="preserve">
    <value>Idioma padrão para programas selecionados</value>
  </data>
  <data name="ProSettingsHeader" xml:space="preserve">
    <value>Ativação de funções PRO</value>
  </data>
  <data name="ProSettingsStatus" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="ProSettingsInput" xml:space="preserve">
    <value>Código de ativação</value>
  </data>
  <data name="ProSettingsActivation" xml:space="preserve">
    <value>Ativar</value>
  </data>
  <data name="ProSettingsEvaluation" xml:space="preserve">
    <value>Experimente</value>
  </data>
  <data name="ProSettingsStatusOk" xml:space="preserve">
    <value>PRO ativado</value>
  </data>
  <data name="ProSettingsStatusEvaluate" xml:space="preserve">
    <value>Período de teste por 40 dias</value>
  </data>
  <data name="ProSettingsLicenseInfo" xml:space="preserve">
    <value>Informações sobre licença</value>
  </data>
  <data name="ProSettingsLicenseActivateDate" xml:space="preserve">
    <value>Data de ativação:</value>
  </data>
  <data name="ProSettingsLicenseExpiryDate" xml:space="preserve">
    <value>Período de validade da licença:</value>
  </data>
  <data name="ProSettingsLicenseEmail" xml:space="preserve">
    <value>E-mail:</value>
  </data>
  <data name="ProSettingsLicenseUserName" xml:space="preserve">
    <value>Proprietário:</value>
  </data>
  <data name="ProSettingsGetData" xml:space="preserve">
    <value>Informações sobre licença</value>
  </data>
  <data name="ProSettingsLicenseUsersCount" xml:space="preserve">
    <value>Número de assentos:</value>
  </data>
  <data name="ProSettingsLicenseCountReact" xml:space="preserve">
    <value>Número de reativações disponíveis:</value>
  </data>
  <data name="ProSettingsLicenseCountFreeSeats" xml:space="preserve">
    <value>Assentos disponíveis:</value>
  </data>
  <data name="ProSettingsLicenseIsEnterprise" xml:space="preserve">
    <value>Tipo de licença:</value>
  </data>
  <data name="ProSettingsLicenseNotPro" xml:space="preserve">
    <value>PRO não ativado</value>
  </data>
  <data name="ProSettingsIsEvaluation" xml:space="preserve">
    <value>PRO ativado para período de teste</value>
  </data>
  <data name="ProSettingsPurchase" xml:space="preserve">
    <value>Comprar</value>
  </data>
  <data name="ProSettingsEmail" xml:space="preserve">
    <value>Digite o e-mail</value>
  </data>
  <data name="ProSettingsCode" xml:space="preserve">
    <value>Insira o código</value>
  </data>
  <data name="ProSendEmailLic" xml:space="preserve">
    <value>As informações da sua licença foram enviadas para:</value>
  </data>
  <data name="ProSettingsDelete" xml:space="preserve">
    <value>Remover licença</value>
  </data>
  <data name="ProSettingsCodeEmail" xml:space="preserve">
    <value>Digite o código e o e-mail</value>
  </data>
  <data name="ProSettingsActivationOk" xml:space="preserve">
    <value>Ativação concluída com sucesso</value>
  </data>
  <data name="ProSettingsActivationError" xml:space="preserve">
    <value>Ativação concluída com erro</value>
  </data>
  <data name="ProSettingsActivationBlocked" xml:space="preserve">
    <value>Ativação concluída com erro, sua licença está bloqueada</value>
  </data>
  <data name="ProSettingsActivationReactivated" xml:space="preserve">
    <value>O programa foi ativado no novo local de trabalho, mas foi desativado no computador</value>
  </data>
  <data name="ProSettingsActivationInternetError" xml:space="preserve">
    <value>Ativação concluída com erro, verifique sua conexão com a internet</value>
  </data>
  <data name="ProSettingsActivationErrorEmail" xml:space="preserve">
    <value>Ativação concluída com erro, um e-mail incorreto pode ter sido inserido</value>
  </data>
  <data name="ProSettingsExpiredEva" xml:space="preserve">
    <value>O período de teste expirou</value>
  </data>
  <data name="ProSettingsExpired" xml:space="preserve">
    <value>A licença expirou</value>
  </data>
  <data name="ProSettingsBlockedByMonthActivateLimit" xml:space="preserve">
    <value>A ativação não é possível porque o limite de reativação foi excedido. Adquira licenças adicionais para esta licença</value>
  </data>
  <data name="ProSettingsBlockedByEarlyActivation" xml:space="preserve">
    <value>A ativação não é possível devido à reativação desta licença em outro computador</value>
  </data>
  <data name="ProSettingsNewLic" xml:space="preserve">
    <value>Seu código de ativação foi atualizado</value>
  </data>
  <data name="ProSettingsNoExpiry" xml:space="preserve">
    <value>Licença perpétua</value>
  </data>
  <data name="ProBlockedByEarlyActivation" xml:space="preserve">
    <value>Você excedeu o limite do número de licenças, todos os recursos PRO serão desativados</value>
  </data>
  <data name="ProBlockedExp" xml:space="preserve">
    <value>A versão PRO expirou, uma nova licença pode ser adquirida no site EVERYLANG.NET</value>
  </data>
  <data name="ProBlockedExpToDays" xml:space="preserve">
    <value>A versão PRO expira em breve</value>
  </data>
  <data name="ProBlockedByMonthActivateLimit" xml:space="preserve">
    <value>O limite de reativação da licença dos últimos 30 dias foi excedido, todos os recursos PRO serão desativados</value>
  </data>
  <data name="ProBlocked" xml:space="preserve">
    <value>Sua licença está bloqueada, todos os recursos PRO serão desativados</value>
  </data>
  <data name="UniversalWindowSettingsHeader" xml:space="preserve">
    <value>SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsUniversalWindowIsOn" xml:space="preserve">
    <value>SmartClick ativado</value>
  </data>
  <data name="UniversalWindowSettingsSearchServices" xml:space="preserve">
    <value>Selecione um serviço de pesquisa para SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsItemsCheck" xml:space="preserve">
    <value>Confira os recursos que estarão disponíveis</value>
  </data>
  <data name="UniversalWindowSettingsShowOnPressLeftAndRightMouseButtons" xml:space="preserve">
    <value>Abra pressionando o botão esquerdo e depois o botão direito do mouse</value>
  </data>
  <data name="UniversalWindowSettingsShowOnDoubleMiddle" xml:space="preserve">
    <value>Abra clicando duas vezes no botão do meio do mouse</value>
  </data>
  <data name="UniversalWindowSettingsShowOnPressHotKeys" xml:space="preserve">
    <value>Use teclas de atalho para abrir</value>
  </data>
  <data name="SmartClickShortcutSettingsHeader" xml:space="preserve">
    <value>Teclas de atalho para abrir a janela SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsShowMiniOn" xml:space="preserve">
    <value>Mostrar janela de ajuda após seleção de texto</value>
  </data>
  <data name="SmartClickMiniSize" xml:space="preserve">
    <value>Tamanho da janela</value>
  </data>
  <data name="SmartClickMiniPos" xml:space="preserve">
    <value>Posição da janela em relação ao ponteiro do mouse</value>
  </data>
  <data name="DiarySettingsHeader" xml:space="preserve">
    <value>Diário</value>
  </data>
  <data name="DiaryShortcuts" xml:space="preserve">
    <value>Diário aberto</value>
  </data>
  <data name="DiaryIsOn" xml:space="preserve">
    <value>Diário incluído</value>
  </data>
  <data name="DiaryPassword" xml:space="preserve">
    <value>Senha do diário</value>
  </data>
  <data name="DiaryPasswordOld" xml:space="preserve">
    <value>Digite a senha do seu antigo diário</value>
  </data>
  <data name="DiaryPasswordSaved" xml:space="preserve">
    <value>Nova senha salva</value>
  </data>
  <data name="DiaryPasswordReset" xml:space="preserve">
    <value>Redefinição de senha</value>
  </data>
  <data name="DiaryMaxItems" xml:space="preserve">
    <value>Número de entradas no diário</value>
  </data>
  <data name="DiaryOldPasswordWrong" xml:space="preserve">
    <value>A senha inserida está incorreta. Redefinir a senha atual? Neste caso, todos os dados do diário serão excluídos</value>
  </data>
  <data name="IsSaveOneWordSentences" xml:space="preserve">
    <value>Mantenha frases de uma palavra</value>
  </data>
  <data name="AutochangeHelperFromText" xml:space="preserve">
    <value>Texto a substituir (opcional):</value>
  </data>
  <data name="AutochangeHelperToText" xml:space="preserve">
    <value>Texto do trecho:</value>
  </data>
  <data name="AutochangeHelperLangListDesc" xml:space="preserve">
    <value>Para qual idioma devo mudar o layout?</value>
  </data>
  <data name="AutochangeHelperLangListNoSwitch" xml:space="preserve">
    <value>Não mude</value>
  </data>
  <data name="AutochangeHelperTags" xml:space="preserve">
    <value>Tags (separadas por espaços):</value>
  </data>
  <data name="AutochangeHelperTagsWatermark" xml:space="preserve">
    <value>Etiquetas</value>
  </data>
  <data name="AutochangeHelperDesc" xml:space="preserve">
    <value>Descrição:</value>
  </data>
  <data name="AutochangeHelperOk" xml:space="preserve">
    <value>Salvar</value>
  </data>
  <data name="AutochangeHelperCancel" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="AutochangeHelperSaveCursorPosition" xml:space="preserve">
    <value>Manter a posição do cursor</value>
  </data>
  <data name="AutochangeHelperChangeAtOnce" xml:space="preserve">
    <value>Substitua ao digitar</value>
  </data>
  <data name="AutochangeTextHeader" xml:space="preserve">
    <value>Trechos</value>
  </data>
  <data name="ToReplacerButton" xml:space="preserve">
    <value>Novo trecho</value>
  </data>
  <data name="AutochangeDelWithTag" xml:space="preserve">
    <value>Excluir todos os snippets com esta tag?</value>
  </data>
  <data name="AutochangeOnInSnippetsList" xml:space="preserve">
    <value>Snippetst ativado</value>
  </data>
  <data name="AutochangeSnippetsList" xml:space="preserve">
    <value>Editar trechos</value>
  </data>
  <data name="AutochangeHeaderFromText" xml:space="preserve">
    <value>O que substituir</value>
  </data>
  <data name="AutochangeHeaderToText" xml:space="preserve">
    <value>O que substituir</value>
  </data>
  <data name="AutochangeAddNew" xml:space="preserve">
    <value>Adicionar</value>
  </data>
  <data name="AutochangeEdit" xml:space="preserve">
    <value>Mudar</value>
  </data>
  <data name="AutochangeDelete" xml:space="preserve">
    <value>Excluir</value>
  </data>
  <data name="AutochangeOtherLayout" xml:space="preserve">
    <value>Substitua ao digitar em outro layout</value>
  </data>
  <data name="AutochangeCaseLetters" xml:space="preserve">
    <value>Combinar letras maiúsculas e minúsculas</value>
  </data>
  <data name="AutochangeShowMiniWindow" xml:space="preserve">
    <value>Mostrar dica ao digitar</value>
  </data>
  <data name="AutochangeIsEnabledCountUsage" xml:space="preserve">
    <value>Classifique por frequência de uso</value>
  </data>
  <data name="AutochangeChangeMethods" xml:space="preserve">
    <value>Substitua por:</value>
  </data>
  <data name="AutochangeIsOn" xml:space="preserve">
    <value>Trechos ativados</value>
  </data>
  <data name="AutochangeKeyboardShortcuts" xml:space="preserve">
    <value>Abrir lista de snippets para inserir</value>
  </data>
  <data name="AutochangeKeyboardShortcutsAddNew" xml:space="preserve">
    <value>Adicione texto destacado aos snippets</value>
  </data>
  <data name="AutochangeOnTab" xml:space="preserve">
    <value>Tecla de tabulação</value>
  </data>
  <data name="AutochangeOnInter" xml:space="preserve">
    <value>Digite a chave</value>
  </data>
  <data name="AutochangeOnTabOrInter" xml:space="preserve">
    <value>Tab ou Enter</value>
  </data>
  <data name="AutochangeOnSpace" xml:space="preserve">
    <value>Espaço</value>
  </data>
  <data name="AutochangeOnSpaceOrInter" xml:space="preserve">
    <value>Espaço ou Enter</value>
  </data>
  <data name="HotKeyUseHotkey" xml:space="preserve">
    <value>Teclas de atalho</value>
  </data>
  <data name="HotKeyUseDoubleKeyDown" xml:space="preserve">
    <value>Pressão dupla de teclas</value>
  </data>
  <data name="HotKeyUseMouseXKey" xml:space="preserve">
    <value>Clicando em um botão do mouse</value>
  </data>
  <data name="HotKeyIsON" xml:space="preserve">
    <value>Teclas de atalho ativadas</value>
  </data>
  <data name="HotKeyWithoutShortcutNull" xml:space="preserve">
    <value>Ausente</value>
  </data>
  <data name="HotKeyWithoutPressShortcut" xml:space="preserve">
    <value>Pressione teclas de atalho</value>
  </data>
  <data name="HotKeyShortcut" xml:space="preserve">
    <value>Combinação</value>
  </data>
  <data name="HotKeyDoubleKeyDown" xml:space="preserve">
    <value>Toque duas vezes</value>
  </data>
  <data name="HotKeyMouse" xml:space="preserve">
    <value>Clicando em um botão do mouse</value>
  </data>
  <data name="HotKeyDoubleKeyDownSelectKey" xml:space="preserve">
    <value>Selecione uma tecla para pressionar duas vezes</value>
  </data>
  <data name="HotKeyMouseSelectKey" xml:space="preserve">
    <value>Selecione um botão adicional do mouse</value>
  </data>
  <data name="HotKeyIsOff" xml:space="preserve">
    <value>Teclas de atalho desativadas</value>
  </data>
  <data name="HotKeyDoubleLeftOrRightCtrl" xml:space="preserve">
    <value>Ctrl Esquerdo ou Direito</value>
  </data>
  <data name="HotKeyDoubleLeftCtrl" xml:space="preserve">
    <value>Ctrl esquerdo</value>
  </data>
  <data name="HotKeyDoubleRightCtrl" xml:space="preserve">
    <value>Ctrl direito</value>
  </data>
  <data name="HotKeyDoubleLeftOrRightShift" xml:space="preserve">
    <value>Deslocamento para esquerda ou direita</value>
  </data>
  <data name="HotKeyDoubleLeftShift" xml:space="preserve">
    <value>Deslocamento Esquerdo</value>
  </data>
  <data name="HotKeyDoubleRightShift" xml:space="preserve">
    <value>Mudança para a direita</value>
  </data>
  <data name="HotKeyDoubleLeftAlt" xml:space="preserve">
    <value>Alt esquerdo</value>
  </data>
  <data name="HotKeyDoubleRightAlt" xml:space="preserve">
    <value>Alt direito</value>
  </data>
  <data name="SoundOnOff" xml:space="preserve">
    <value>Som ativado</value>
  </data>
  <data name="SoundFormVolume" xml:space="preserve">
    <value>Volume</value>
  </data>
  <data name="SoundFormSelectTrack" xml:space="preserve">
    <value>Selecione um som</value>
  </data>
  <data name="StartPageHeader" xml:space="preserve">
    <value>Antes de usar o programa, leia a documentação</value>
  </data>
  <data name="StartPageHelp" xml:space="preserve">
    <value>Explore os recursos do programa</value>
  </data>
  <data name="StartPageVideo" xml:space="preserve">
    <value>Assista ao vídeo de apresentação</value>
  </data>
  <data name="StartPageSite" xml:space="preserve">
    <value>Site do programa</value>
  </data>
  <data name="StartPageLicense" xml:space="preserve">
    <value>Compre uma licença</value>
  </data>
  <data name="OcrHeader" xml:space="preserve">
    <value>Reconhecimento de texto</value>
  </data>
  <data name="OcrKeyboardShortcuts" xml:space="preserve">
    <value>Atalho de teclado para iniciar o OCR</value>
  </data>
  <data name="OcrDescDefault" xml:space="preserve">
    <value>Selecione os idiomas padrão</value>
  </data>
  <data name="OcrDescLang" xml:space="preserve">
    <value>Selecione idiomas</value>
  </data>
  <data name="OcrDescNotSup" xml:space="preserve">
    <value>O uso de idiomas europeus e asiáticos não é compatível</value>
  </data>
  <data name="OcrEuropeanLang" xml:space="preserve">
    <value>Línguas europeias:</value>
  </data>
  <data name="OcrAsianLang" xml:space="preserve">
    <value>Línguas asiáticas:</value>
  </data>
  <data name="OcrCaptureArea" xml:space="preserve">
    <value>Selecione a área da tela</value>
  </data>
  <data name="OcrOpenImageOrPDFFile" xml:space="preserve">
    <value>Abrir imagem</value>
  </data>
  <data name="OcrRecognize" xml:space="preserve">
    <value>Reconhecer</value>
  </data>
  <data name="OcrRecognizeBarcode" xml:space="preserve">
    <value>Reconhecer código de barras</value>
  </data>
  <data name="OcrSelectLanguages" xml:space="preserve">
    <value>Selecione idiomas</value>
  </data>
  <data name="OcrStartText" xml:space="preserve">
    <value>Selecione uma área na tela ou carregue um arquivo para reconhecimento</value>
  </data>
  <data name="OcrTab" xml:space="preserve">
    <value>Reconhecimento de texto</value>
  </data>
  <data name="OcrInit" xml:space="preserve">
    <value>Aguarde, o módulo está carregando</value>
  </data>
  <data name="OcrLoadLibs" xml:space="preserve">
    <value>Clique para baixar o módulo</value>
  </data>
  <data name="OcrWaitResult" xml:space="preserve">
    <value>Texto copiado</value>
  </data>
  <data name="OcrWaitResultFail" xml:space="preserve">
    <value>Texto não reconhecido</value>
  </data>
  <data name="OcrCopyImage" xml:space="preserve">
    <value>Copiar imagem</value>
  </data>
  <data name="OcrCopyText" xml:space="preserve">
    <value>Copiar texto</value>
  </data>
  <data name="ReplaceTo" xml:space="preserve">
    <value>Substitua por:</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Procurar:</value>
  </data>
  <data name="TotalMatchFound" xml:space="preserve">
    <value>Total de correspondências encontradas:</value>
  </data>
  <data name="NoMatchFound" xml:space="preserve">
    <value>Nenhuma correspondência encontrada!</value>
  </data>
  <data name="TotalMatchFoundReplace" xml:space="preserve">
    <value>Total de substituições realizadas:</value>
  </data>
  <data name="DiaryHeaderDate" xml:space="preserve">
    <value>DATA</value>
  </data>
  <data name="DiaryHeaderFormat" xml:space="preserve">
    <value>TIPO</value>
  </data>
  <data name="GridViewClearFilter" xml:space="preserve">
    <value>Limpar filtro</value>
  </data>
  <data name="GridViewColumnsSelectionButtonTooltip" xml:space="preserve">
    <value>Selecione Colunas</value>
  </data>
  <data name="GridViewFilter" xml:space="preserve">
    <value>Filtro</value>
  </data>
  <data name="GridViewFilterAnd" xml:space="preserve">
    <value>E</value>
  </data>
  <data name="GridViewFilterContains" xml:space="preserve">
    <value>Contém</value>
  </data>
  <data name="GridViewFilterDoesNotContain" xml:space="preserve">
    <value>Não contém</value>
  </data>
  <data name="GridViewFilterEndsWith" xml:space="preserve">
    <value>Termina com</value>
  </data>
  <data name="GridViewFilterIsContainedIn" xml:space="preserve">
    <value>Contido em</value>
  </data>
  <data name="GridViewFilterIsEmpty" xml:space="preserve">
    <value>Vazio</value>
  </data>
  <data name="GridViewFilterIsEqualTo" xml:space="preserve">
    <value>Igual</value>
  </data>
  <data name="GridViewFilterIsGreaterThan" xml:space="preserve">
    <value>Mais do que</value>
  </data>
  <data name="GridViewFilterIsGreaterThanOrEqualTo" xml:space="preserve">
    <value>Maior ou igual a</value>
  </data>
  <data name="GridViewFilterIsLessThan" xml:space="preserve">
    <value>Menor que</value>
  </data>
  <data name="GridViewFilterIsLessThanOrEqualTo" xml:space="preserve">
    <value>Menor ou igual a</value>
  </data>
  <data name="GridViewFilterIsNotContainedIn" xml:space="preserve">
    <value>Não contido em</value>
  </data>
  <data name="GridViewFilterIsNotEmpty" xml:space="preserve">
    <value>não vazio</value>
  </data>
  <data name="GridViewFilterIsNotEqualTo" xml:space="preserve">
    <value>não é igual</value>
  </data>
  <data name="GridViewFilterIsNotNull" xml:space="preserve">
    <value>Não é nulo</value>
  </data>
  <data name="GridViewFilterIsNull" xml:space="preserve">
    <value>Nulo</value>
  </data>
  <data name="GridViewFilterMatchCase" xml:space="preserve">
    <value>Maiúsculas e minúsculas</value>
  </data>
  <data name="GridViewFilterOr" xml:space="preserve">
    <value>Ou</value>
  </data>
  <data name="GridViewFilterSelectAll" xml:space="preserve">
    <value>Selecionar tudo</value>
  </data>
  <data name="GridViewFilterShowRowsWithValueThat" xml:space="preserve">
    <value>Mostrar linhas com valor que</value>
  </data>
  <data name="GridViewFilterStartsWith" xml:space="preserve">
    <value>Começa com</value>
  </data>
  <data name="GridViewGroupPanelText" xml:space="preserve">
    <value>Arraste o cabeçalho de uma coluna e solte-o aqui para agrupar por esta coluna</value>
  </data>
  <data name="GridViewGroupPanelTopText" xml:space="preserve">
    <value>Cabeçalho do grupo</value>
  </data>
  <data name="GridViewGroupPanelTopTextGrouped" xml:space="preserve">
    <value>Agrupado por:</value>
  </data>
  <data name="GridViewSearchPanelTopText" xml:space="preserve">
    <value>Pesquisa de texto completo</value>
  </data>
  <data name="DiaryOff" xml:space="preserve">
    <value>O diário está desativado</value>
  </data>
  <data name="DiaryOn" xml:space="preserve">
    <value>Diário incluído</value>
  </data>
  <data name="ClipboardOff" xml:space="preserve">
    <value>O gerenciador da área de transferência está desativado</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>Desabilitado</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>Incluído</value>
  </data>
  <data name="SwitcherSettingsIsOff" xml:space="preserve">
    <value>Troca de layout desativada</value>
  </data>
  <data name="AutoSwitchSettingsIsOff" xml:space="preserve">
    <value>A troca automática de layout está desativada</value>
  </data>
  <data name="AutoSwitchSettingsIsOn" xml:space="preserve">
    <value>Troca automática de layout ativada</value>
  </data>
  <data name="GridViewAlwaysVisibleNewRow" xml:space="preserve">
    <value>Clique aqui para adicionar um novo item</value>
  </data>
  <data name="CopyTranslatedText" xml:space="preserve">
    <value>Copiar texto traduzido</value>
  </data>
  <data name="ClearAll" xml:space="preserve">
    <value>Limpar tudo</value>
  </data>
  <data name="SiteSourceButton" xml:space="preserve">
    <value>Abrir no navegador</value>
  </data>
  <data name="CloseQuestion" xml:space="preserve">
    <value>Fechar o programa?</value>
  </data>
  <data name="TransSettingsFavoriteLanguages" xml:space="preserve">
    <value>Idiomas em destaque</value>
  </data>
  <data name="TransSettingsChooseYourFavoriteLanguages" xml:space="preserve">
    <value>Selecione seus idiomas favoritos</value>
  </data>
  <data name="TransSettingsHistoryIsOn" xml:space="preserve">
    <value>Armazenar histórico de tradução</value>
  </data>
  <data name="TransSettingsClearAllHistory" xml:space="preserve">
    <value>Limpar histórico de tradução</value>
  </data>
  <data name="GeneralSettingsFont" xml:space="preserve">
    <value>Fonte</value>
  </data>
  <data name="ProgramsExceptionsCurrentInfo" xml:space="preserve">
    <value>Funções</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Carregando</value>
  </data>
  <data name="ImageEditor_CanvasResize" xml:space="preserve">
    <value>Alterando o tamanho da tela</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Fechar</value>
  </data>
  <data name="ImageEditor_Adjust" xml:space="preserve">
    <value>Correção</value>
  </data>
  <data name="ImageEditor_Amount" xml:space="preserve">
    <value>Soma</value>
  </data>
  <data name="ImageEditor_Auto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="ImageEditor_Background" xml:space="preserve">
    <value>Fundo:</value>
  </data>
  <data name="ImageEditor_BorderColor" xml:space="preserve">
    <value>Cor da borda:</value>
  </data>
  <data name="ImageEditor_BorderThickness" xml:space="preserve">
    <value>Espessura da borda:</value>
  </data>
  <data name="ImageEditor_CanvasSize" xml:space="preserve">
    <value>Tamanho da tela</value>
  </data>
  <data name="ImageEditor_ColorPicker_NoColorText_White" xml:space="preserve">
    <value>branco</value>
  </data>
  <data name="ImageEditor_Crop" xml:space="preserve">
    <value>Aparar</value>
  </data>
  <data name="ImageEditor_DrawText" xml:space="preserve">
    <value>Texto da imagem</value>
  </data>
  <data name="ImageEditor_DrawText_YourTextHere" xml:space="preserve">
    <value>Seu texto</value>
  </data>
  <data name="ImageEditor_DrawTool" xml:space="preserve">
    <value>Empate</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushColor" xml:space="preserve">
    <value>Cor do pincel:</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushSize" xml:space="preserve">
    <value>Tamanho do pincel:</value>
  </data>
  <data name="ImageEditor_Effect_Blur" xml:space="preserve">
    <value>Borrão</value>
  </data>
  <data name="ImageEditor_Effect_Brightness" xml:space="preserve">
    <value>Brilho</value>
  </data>
  <data name="ImageEditor_Effect_ContrastAdjust" xml:space="preserve">
    <value>Contraste</value>
  </data>
  <data name="ImageEditor_Effect_HueShift" xml:space="preserve">
    <value>Mudando matiz</value>
  </data>
  <data name="ImageEditor_Effect_InvertColors" xml:space="preserve">
    <value>Inverter cores</value>
  </data>
  <data name="ImageEditor_Effect_Saturation" xml:space="preserve">
    <value>Saturação</value>
  </data>
  <data name="ImageEditor_Effect_Sharpen" xml:space="preserve">
    <value>Afiado</value>
  </data>
  <data name="ImageEditor_Effects" xml:space="preserve">
    <value>Modificação</value>
  </data>
  <data name="ImageEditor_FlipHorizontal" xml:space="preserve">
    <value>Virar horizontalmente</value>
  </data>
  <data name="ImageEditor_FlipVertical" xml:space="preserve">
    <value>Virar verticalmente</value>
  </data>
  <data name="ImageEditor_FontSize" xml:space="preserve">
    <value>Tamanho da fonte</value>
  </data>
  <data name="ImageEditor_Height" xml:space="preserve">
    <value>Altura:</value>
  </data>
  <data name="ImageEditor_HorizontalPosition" xml:space="preserve">
    <value>Posição horizontal</value>
  </data>
  <data name="ImageEditor_ImageAlignment" xml:space="preserve">
    <value>Alinhamento de imagem</value>
  </data>
  <data name="ImageEditor_ImagePreview" xml:space="preserve">
    <value>Visualização da imagem</value>
  </data>
  <data name="ImageEditor_ImageSize" xml:space="preserve">
    <value>Tamanho da imagem</value>
  </data>
  <data name="ImageEditor_Open" xml:space="preserve">
    <value>Abrir</value>
  </data>
  <data name="ImageEditor_Options" xml:space="preserve">
    <value>Opções</value>
  </data>
  <data name="ImageEditor_PreserveAspectRatio" xml:space="preserve">
    <value>Manter a proporção original</value>
  </data>
  <data name="ImageEditor_Radius" xml:space="preserve">
    <value>Raio:</value>
  </data>
  <data name="ImageEditor_Redo" xml:space="preserve">
    <value>Retornar</value>
  </data>
  <data name="ImageEditor_RelativeSize" xml:space="preserve">
    <value>Tamanho relativo</value>
  </data>
  <data name="ImageEditor_Resize" xml:space="preserve">
    <value>Redimensionar</value>
  </data>
  <data name="ImageEditor_Rotate180" xml:space="preserve">
    <value>Girar 180°</value>
  </data>
  <data name="ImageEditor_Rotate270" xml:space="preserve">
    <value>Girar 270°</value>
  </data>
  <data name="ImageEditor_Rotate90" xml:space="preserve">
    <value>Girar 90°</value>
  </data>
  <data name="ImageEditor_Rotation" xml:space="preserve">
    <value>Vez</value>
  </data>
  <data name="ImageEditor_RoundCorners" xml:space="preserve">
    <value>Cantos arredondados</value>
  </data>
  <data name="ImageEditor_Save" xml:space="preserve">
    <value>Salvar</value>
  </data>
  <data name="ImageEditor_Shape" xml:space="preserve">
    <value>Figura</value>
  </data>
  <data name="ImageEditor_Shapes_Ellipse" xml:space="preserve">
    <value>Elipse</value>
  </data>
  <data name="ImageEditor_Shapes_Line" xml:space="preserve">
    <value>Agendar</value>
  </data>
  <data name="ImageEditor_Shapes_Rectangle" xml:space="preserve">
    <value>Retângulo</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderColor" xml:space="preserve">
    <value>Cor da borda</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderThickness" xml:space="preserve">
    <value>Espessura da borda</value>
  </data>
  <data name="ImageEditor_ShapeTool_FillShape" xml:space="preserve">
    <value>Formulário de enchimento de tubos</value>
  </data>
  <data name="ImageEditor_ShapeTool_LockRatio" xml:space="preserve">
    <value>Bloquear proporções</value>
  </data>
  <data name="ImageEditor_ShapeTool_Shape" xml:space="preserve">
    <value>Figura</value>
  </data>
  <data name="ImageEditor_ShapeTool_ShapeFill" xml:space="preserve">
    <value>Preenchendo uma forma</value>
  </data>
  <data name="ImageEditor_Text" xml:space="preserve">
    <value>Texto</value>
  </data>
  <data name="ImageEditor_TextColor" xml:space="preserve">
    <value>Cor do texto</value>
  </data>
  <data name="ImageEditor_TheFileCannotBeOpened" xml:space="preserve">
    <value>O arquivo não pode ser aberto.</value>
  </data>
  <data name="ImageEditor_TheFileIsLocked" xml:space="preserve">
    <value>O arquivo não pode ser aberto. Isso pode estar bloqueado por outro aplicativo.</value>
  </data>
  <data name="ImageEditor_Transform" xml:space="preserve">
    <value>Converter</value>
  </data>
  <data name="ImageEditor_UnableToSaveFile" xml:space="preserve">
    <value>Falha ao salvar o arquivo.</value>
  </data>
  <data name="ImageEditor_Undo" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="ImageEditor_UnsupportedFileFormat" xml:space="preserve">
    <value>Este formato de arquivo não é suportado.</value>
  </data>
  <data name="ImageEditor_VerticalPosition" xml:space="preserve">
    <value>Posição vertical</value>
  </data>
  <data name="ImageEditor_Width" xml:space="preserve">
    <value>Largura:</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Reiniciar</value>
  </data>
  <data name="ResetAll" xml:space="preserve">
    <value>Redefinir tudo</value>
  </data>
  <data name="OcrFromClipboard" xml:space="preserve">
    <value>Da área de transferência</value>
  </data>
  <data name="OcrEditImage" xml:space="preserve">
    <value>Editar imagem</value>
  </data>
  <data name="PasteButtonWithoutClipboard" xml:space="preserve">
    <value>Colar entrada de emulação</value>
  </data>
  <data name="AutochangeEditor" xml:space="preserve">
    <value>Editor de trechos</value>
  </data>
  <data name="AutochangeHelperFromTextTootlTip" xml:space="preserve">
    <value>Texto de substituição:</value>
  </data>
  <data name="OcrModuleNotLoaded" xml:space="preserve">
    <value>O módulo de reconhecimento de texto não está carregado</value>
  </data>
  <data name="AppearanceTab" xml:space="preserve">
    <value>Aparência</value>
  </data>
  <data name="OrderFunctionsTab" xml:space="preserve">
    <value>Ordem das funções</value>
  </data>
  <data name="TranslateOnlyFavoriteLanguages" xml:space="preserve">
    <value>Mostrar apenas os idiomas selecionados</value>
  </data>
  <data name="TranslateSowAll" xml:space="preserve">
    <value>Mostrar tudo</value>
  </data>
  <data name="GeneralSettingsThemeProgramRestart" xml:space="preserve">
    <value>Reinicie o programa para mudar o tema?</value>
  </data>
  <data name="CommonWindowPressKeyForPast" xml:space="preserve">
    <value>Para inserir texto, pressione a tecla</value>
  </data>
  <data name="MiminoteTab" xml:space="preserve">
    <value>Notas</value>
  </data>
  <data name="NoteTab" xml:space="preserve">
    <value>Notas</value>
  </data>
  <data name="NotesShow" xml:space="preserve">
    <value>Mostrar notas</value>
  </data>
  <data name="NotesToArchive" xml:space="preserve">
    <value>Para o arquivo</value>
  </data>
  <data name="NotesAddNew" xml:space="preserve">
    <value>Adicione uma nota</value>
  </data>
  <data name="NotesList" xml:space="preserve">
    <value>Lista de notas</value>
  </data>
  <data name="NoteColor" xml:space="preserve">
    <value>Cor da nota</value>
  </data>
  <data name="NoteConvertToNote" xml:space="preserve">
    <value>Converter em nota</value>
  </data>
  <data name="NoteConvertToTaskList" xml:space="preserve">
    <value>Converter em lista de tarefas</value>
  </data>
  <data name="NotePasteAsPlainText" xml:space="preserve">
    <value>Colar como texto normal</value>
  </data>
  <data name="NotePasteAsText" xml:space="preserve">
    <value>Colar como texto</value>
  </data>
  <data name="NoteArchiveList" xml:space="preserve">
    <value>Arquivo de notas</value>
  </data>
  <data name="NoteRestore" xml:space="preserve">
    <value>Restaurar</value>
  </data>
  <data name="NoteFontFamilyAndSize" xml:space="preserve">
    <value>Família de fontes e tamanho das notas</value>
  </data>
  <data name="NoteTransparencyForInactiveNotes" xml:space="preserve">
    <value>Transparência de notas inativas</value>
  </data>
  <data name="UniversalWindowSettingsUniversalWindowIsOff" xml:space="preserve">
    <value>SmartClick desativado</value>
  </data>
  <data name="DiaryIsOff" xml:space="preserve">
    <value>Diário desativado</value>
  </data>
  <data name="AutochangeIsOff" xml:space="preserve">
    <value>Trechos desativados</value>
  </data>
  <data name="GeneralSettingsCanClose" xml:space="preserve">
    <value>Mostrar botão Fechar</value>
  </data>
  <data name="SwitcherPauseTimeForKeysSend" xml:space="preserve">
    <value>Tempo limite para emulação de teclas</value>
  </data>
  <data name="OcrImage" xml:space="preserve">
    <value>Imagem</value>
  </data>
  <data name="OcrRecognizedText" xml:space="preserve">
    <value>Texto reconhecido</value>
  </data>
  <data name="UpdateError" xml:space="preserve">
    <value>A atualização não foi concluída, atualize manualmente.</value>
  </data>
  <data name="UpdateErrorTitle" xml:space="preserve">
    <value>Erro de atualização do EveryLang</value>
  </data>
  <data name="NoteCheckMarket" xml:space="preserve">
    <value>Marcado</value>
  </data>
  <data name="NotesIsShowing" xml:space="preserve">
    <value>Incluído</value>
  </data>
  <data name="SearchHelperText" xml:space="preserve">
    <value>Digite o texto para pesquisar</value>
  </data>
  <data name="TransLangAuto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="NotesFromArchive" xml:space="preserve">
    <value>Do arquivo</value>
  </data>
  <data name="NotesIsHiding" xml:space="preserve">
    <value>Desabilitado</value>
  </data>
  <data name="NoteInArchive" xml:space="preserve">
    <value>No arquivo</value>
  </data>
  <data name="KeyboardLayoutTab" xml:space="preserve">
    <value>Layout do teclado</value>
  </data>
  <data name="CapsTab" xml:space="preserve">
    <value>Caso de texto</value>
  </data>
  <data name="IsIndicateNumLockState" xml:space="preserve">
    <value>Mostrar status do NumLock</value>
  </data>
  <data name="StatusButtonNumLockIsOff" xml:space="preserve">
    <value>NumLock está desabilitado</value>
  </data>
  <data name="StatusButtonNumLockIsOn" xml:space="preserve">
    <value>NumLock ativado</value>
  </data>
  <data name="ConverterSettingsOpenWindow" xml:space="preserve">
    <value>Abra uma janela com funções de conversor</value>
  </data>
  <data name="UniFirstLetterUp" xml:space="preserve">
    <value>Maiúscula a primeira letra</value>
  </data>
  <data name="UniTextFirstLetterUp" xml:space="preserve">
    <value>Primeiro</value>
  </data>
  <data name="UniFirstLetterDown" xml:space="preserve">
    <value>Primeira letra minúscula</value>
  </data>
  <data name="UniTextFirstLetterDown" xml:space="preserve">
    <value>Primeira descida</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsCapsOpenWindow" xml:space="preserve">
    <value>Abra uma janela com funções de mudança de caso</value>
  </data>
  <data name="ConverterSettingsSnakeCase" xml:space="preserve">
    <value>Converter texto para estilo Snake_case</value>
  </data>
  <data name="ConverterSettingsKebabCase" xml:space="preserve">
    <value>Convertendo texto para estilo kebab-case</value>
  </data>
  <data name="ConverterSettingsPascalCase" xml:space="preserve">
    <value>Convertendo texto para estilo PascalCase</value>
  </data>
  <data name="UniCase" xml:space="preserve">
    <value>Caso de texto</value>
  </data>
  <data name="AboutSettingsUpdatePressForUpdate" xml:space="preserve">
    <value>Clique para baixar</value>
  </data>
</root>