﻿using Everylang.App.Callback;
using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.SettingsApp;
using Everylang.App.SwitcherLang;
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Interop;
using Telerik.Windows.Controls;
using Vanara.PInvoke;

namespace Everylang.App.View.Controls.Common.CommonWindow
{
    /// <summary>
    /// Interaction logic for FastActionCommonWindow.xaml
    /// </summary>
    internal partial class FastActionCommonWindow
    {
        internal static readonly DependencyProperty IsStayOnTopProperty = DependencyProperty.Register("IsStayOnTop", typeof(bool), typeof(FastActionCommonWindow), new FrameworkPropertyMetadata(default(bool)));

        internal event Action<GlobalKeyEventArgs>? GlobalKeyEvent;
        internal bool IsStayOnTop
        {
            get => (bool)GetValue(IsStayOnTopProperty);
            set => SetValue(IsStayOnTopProperty, value);
        }

        internal static readonly DependencyProperty TitleTextProperty =
            DependencyProperty.Register(nameof(TitleText),
                typeof(string),
                typeof(FastActionCommonWindow),
                new FrameworkPropertyMetadata(""));

        internal string TitleText
        {
            get { return (string)GetValue(TitleTextProperty); }
            set { SetValue(TitleTextProperty, value); }
        }

        private static FastActionCommonWindow? _instance;
        internal static FastActionCommonWindow? Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new FastActionCommonWindow();
                }
                return _instance;
            }
            set => _instance = value;
        }

        internal bool MenuIsOpen { get; set; }

        private FastActionClipboard? _fastActionClipboard;
        private FastActionDiary? _fastActionDiary;
        private FastActionSnippets? _fastActionSnippets;
        private IFastActionComponent? _currentComponent;
        private bool _isFindNow;

        internal FastActionCommonWindow()
        {
            InitializeComponent();
            DataContext = this;
            HookCallBackKeyDown.CallbackEventHandler += HookManagerKeyDown;
            HookCallBackMouseDown.CallbackEventHandler += HookManagerMouseDown;
            Opened += OnOpened;
        }

        private void OnOpened(object? sender, EventArgs e)
        {
            if (PresentationSource.FromVisual(this.Child) is HwndSource source)
            {
                IntPtr handle = source.Handle;
                User32.SetActiveWindow(handle);
            }
        }

        internal void Show(int type)
        {
            MuNavigationView.SelectedIndex = type;
            NavigationViewOnSelectionChanged(null, null);
            IsOpen = true;
        }

        internal void Hide()
        {
            if (!IsStayOnTop)
            {
                if (_currentComponent != null) _currentComponent.Close();
                HookCallBackKeyDown.CallbackEventHandler -= HookManagerKeyDown;
                HookCallBackMouseDown.CallbackEventHandler -= HookManagerMouseDown;
                IsOpen = false;
                Instance = null;
            }
        }

        private void HookManagerMouseDown(GlobalMouseEventArgs e)
        {
            if (SettingsManager.IsStopWorking)
            {
                return;
            }
            if (!TextBoxSearch.IsFocused)
            {
                _isFindNow = false;
            }
            if (!IsMouseOver)
            {
                if (MenuIsOpen)
                {
                    return;
                }
                Hide();
            }
        }

        private void HookManagerKeyDown(GlobalKeyEventArgs e)
        {
            if (_currentComponent == null)
            {
                return;
            }
            if (SettingsManager.IsStopWorking)
            {
                return;
            }

            if (GlobalKeyEvent != null && _currentComponent.GetType().Equals(typeof(FastActionSnippets)))
            {
                GlobalKeyEvent(e);
            }
            if (_isFindNow)
            {
                if (e.KeyCode == VirtualKeycodes.Backspace && TextBoxSearch.Text != "")
                {
                    TextBoxSearch.Text = TextBoxSearch.Text.Remove(TextBoxSearch.Text.Length - 1);
                    e.Handled = true;
                    return;
                }
                string c = KeyboardLayoutMethods.CodeToString(e);
                if (!string.IsNullOrEmpty(c) && (!char.IsSurrogate(c[0]) && !char.IsControl(c[0])))
                {
                    TextBoxSearch.Text += c;
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == VirtualKeycodes.Esc)
                {
                    _isFindNow = false;
                    TextBoxSearch.Text = "";
                    _currentComponent.Init();
                    e.Handled = true;
                    return;
                }
            }
            if (e.KeyCode == VirtualKeycodes.Tab)
            {
                if (e.Control != ModifierKeySide.None)
                {
                    if (MuNavigationView.SelectedIndex == MuNavigationView.Items.Count - 1)
                    {
                        MuNavigationView.SelectedIndex = 0;
                    }
                    else
                    {
                        MuNavigationView.SelectedIndex += 1;
                    }
                    e.Handled = true;
                    return;
                }
                else if (e.Control == ModifierKeySide.None &&
                         e.Shift == ModifierKeySide.None &&
                         e.Alt == ModifierKeySide.None)
                {
                    _isFindNow = true;

                    TextBoxSearch.Focus();
                    TextBoxSearch.UpdateLayout();
                    e.Handled = true;
                    return;
                }
            }
            if (e.KeyCode == VirtualKeycodes.Esc && !IsStayOnTop)
            {
                e.Handled = true;
                Hide();
            }
            if (e.KeyCode == VirtualKeycodes.Enter && !IsStayOnTop)
            {
                e.Handled = true;
                _currentComponent.Replace();
            }
            int i;
            if (int.TryParse(KeyboardLayoutMethods.CodeToString(e), out i) && !IsStayOnTop)
            {
                if (i > 0 && i < 10)
                {
                    e.Handled = true;
                    _currentComponent.Replace(i - 1);

                }
            }
            if (e.KeyCode == VirtualKeycodes.C && e.Control != ModifierKeySide.None && !IsStayOnTop)
            {
                e.Handled = true;
                _currentComponent.Copy();
            }

            if (e.KeyCode == VirtualKeycodes.Insert && e.Control != ModifierKeySide.None && !IsStayOnTop)
            {
                e.Handled = true;
                _currentComponent.Copy();
            }
            if (e.KeyCode == VirtualKeycodes.A && e.Control != ModifierKeySide.None && !IsStayOnTop)
            {
                e.Handled = true;
                _currentComponent.SelectAll();
            }
            if (e.KeyCode == VirtualKeycodes.Delete && !IsStayOnTop)
            {
                _currentComponent.Delete();
                e.Handled = true;
            }

            if ((e.KeyCode == VirtualKeycodes.UpArrow || e.KeyCode == VirtualKeycodes.DownArrow) && !IsStayOnTop)
            {

                if (e.KeyCode == VirtualKeycodes.UpArrow)
                {
                    _currentComponent.SelectPrev(e.Shift != ModifierKeySide.None);
                }
                if (e.KeyCode == VirtualKeycodes.DownArrow)
                {
                    _currentComponent.SelectNext(e.Shift != ModifierKeySide.None);
                }
                e.Handled = true;
            }
        }

        private void OnDragDelta(object sender, DragDeltaEventArgs e)
        {
            HorizontalOffset += e.HorizontalChange;
            VerticalOffset += e.VerticalChange;
        }

        private void ThumbDragDelta(object sender, DragDeltaEventArgs e)
        {
            if (sender is Thumb t)
            {
                if (t.Name == "ThumbRight" || t.Name == "ThumbRightBottom")
                {
                    Width = Math.Min(MaxWidth,
                        Math.Max(Width + e.HorizontalChange,
                            MinWidth));
                }

                if (t.Name == "ThumbBottom" || t.Name == "ThumbRightBottom")
                {
                    Height = Math.Min(MaxHeight,
                        Math.Max(Height + e.VerticalChange,
                            MinHeight));
                }

                if (t.Name == "ThumbLeft")
                {
                    if ((e.HorizontalChange < 0 && Width < MaxWidth) || (e.HorizontalChange > 0 && Width > MinWidth))
                    {
                        HorizontalOffset += e.HorizontalChange;
                        Width = Math.Min(MaxWidth,
                            Math.Max(Width + (e.HorizontalChange * -1),
                                MinWidth));
                    }
                }
                if (t.Name == "ThumbTop")
                {
                    if ((e.VerticalChange < 0 && Height < MaxHeight) || (e.VerticalChange > 0 && Height > MinHeight))
                    {
                        VerticalOffset += e.VerticalChange;
                        Height = Math.Min(MaxHeight,
                            Math.Max(Height + (e.VerticalChange * -1),
                                MinHeight));
                    }
                }
            }

        }

        private void WindowMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (Math.Abs(this.Width - this.MaxWidth) < 5)
            {
                this.Width = this.MinWidth;
                this.Height = this.MinHeight;
            }
            else
            {
                this.Width = this.MaxWidth;
                this.Height = this.MaxHeight;
            }
        }

        private void NavigationViewOnSelectionChanged(object? sender, SelectionChangedEventArgs? e)
        {
            if (MuNavigationView.SelectedIndex == 0)
            {
                if (_fastActionClipboard == null)
                {
                    _fastActionClipboard = new FastActionClipboard();
                }
                MuNavigationView.Content = _fastActionClipboard;
                TitleText = LocalizationManager.GetString("ClipboardTab");
                _currentComponent = _fastActionClipboard;
            }
            if (MuNavigationView.SelectedIndex == 1)
            {
                if (_fastActionDiary == null)
                {
                    _fastActionDiary = new FastActionDiary();
                }
                MuNavigationView.Content = _fastActionDiary;
                TitleText = LocalizationManager.GetString("DiareTab");
                _currentComponent = _fastActionDiary;
            }
            if (MuNavigationView.SelectedIndex == 2)
            {
                if (_fastActionSnippets == null)
                {
                    _fastActionSnippets = new FastActionSnippets();
                }
                MuNavigationView.Content = _fastActionSnippets;
                TitleText = LocalizationManager.GetString("AutochangeTab");
                _currentComponent = _fastActionSnippets;
            }
            //_currentComponent.Start();
        }

        private void ButtonClickClose(object sender, RoutedEventArgs e)
        {
            IsStayOnTop = false;
            Hide();
        }

        private void ClickSetStayOnTop(object sender, RoutedEventArgs e)
        {
            IsStayOnTop = !IsStayOnTop;
        }

        private void ClickOpenSettings(object sender, RoutedEventArgs e)
        {
            IsStayOnTop = false;
            if (MuNavigationView.SelectedIndex == 0) GlobalEventsApp.OnEventOpenSettingsClipboard();
            if (MuNavigationView.SelectedIndex == 1) GlobalEventsApp.OnEventOpenSettingsDiary();
            if (MuNavigationView.SelectedIndex == 2) GlobalEventsApp.OnEventOpenSnippetsSettings();
            Hide();
        }

        private void ClickOpenList(object sender, RoutedEventArgs e)
        {
            IsStayOnTop = false;
            if (MuNavigationView.SelectedIndex == 0) GlobalEventsApp.OnEventOpenListClipboard();
            if (MuNavigationView.SelectedIndex == 1) GlobalEventsApp.OnEventOpenListDiary();
            if (MuNavigationView.SelectedIndex == 2) GlobalEventsApp.OnEventOpenSnippetsList();
            Hide();
        }

        private void TextBoxSearch_OnTextChanged(object sender, TextChangedEventArgs e)
        {
            TextBoxSearch.CaretIndex = TextBoxSearch.Text.Length;
            _currentComponent?.FindText(TextBoxSearch.Text);
        }

        private void TextBoxSearch_OnPreviewMouseDown(object sender, MouseButtonEventArgs e)
        {
            _isFindNow = true;

            TextBoxSearch.Focus();
            TextBoxSearch.UpdateLayout();
        }
    }
}
