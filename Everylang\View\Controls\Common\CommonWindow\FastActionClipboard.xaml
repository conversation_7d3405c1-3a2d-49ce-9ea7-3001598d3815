﻿<UserControl
    mc:Ignorable="d"
    x:Class="Everylang.App.View.Controls.Common.CommonWindow.FastActionClipboard"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:commonWindow="clr-namespace:Everylang.App.View.Controls.Common.CommonWindow"
    xmlns:helpers="clr-namespace:Everylang.App.View.Helpers"
    xmlns:wpf="clr-namespace:Material.Icons.WPF;assembly=Material.Icons.WPF" x:ClassModifier="internal">
    <UserControl.Resources>
        <commonWindow:ViewIndexConverter x:Key="ViewIndexConverter" />
        <commonWindow:ViewIndexTooltipConverter x:Key="ViewIndexTooltipConverter" />
    </UserControl.Resources>
    <Grid Background="{telerik:Windows11Resource ResourceKey=AlternativeBrush}">
        <telerik:RadListBox
            Background="{telerik:Windows11Resource ResourceKey=AlternativeBrush}"
            BorderThickness="0"
            IsScrollIntoViewEnabled="True"
            Name="LvFastAction"
            PreviewMouseDoubleClick="LvFastAction_OnPreviewMouseDoubleClick"
            ScrollViewer.HorizontalScrollBarVisibility="Disabled"
            SelectedIndex="0"
            SelectionMode="Extended">
            <telerik:RadListBox.ItemTemplate>
                <DataTemplate>
                    <Border
                        BorderThickness="1"
                        CornerRadius="2"
                        Margin="5"
                        Padding="5">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="10" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <TextBlock
                                FontSize="10"
                                Text="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type telerik:RadListBoxItem}}, Mode=OneWay, Converter={StaticResource ViewIndexConverter}}"
                                ToolTip="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type telerik:RadListBoxItem}}, Mode=OneWay, Converter={StaticResource ViewIndexTooltipConverter}}"
                                VerticalAlignment="Center" />
                            <helpers:TextBlockWithSelection
                                Background="Transparent"
                                FontSize="12"
                                Grid.Column="1"
                                HorizontalAlignment="Stretch"
                                Text="{Binding ShortText}"
                                TextSelected="TextBlockSelectionChanged"
                                TextWrapping="Wrap"
                                VerticalAlignment="Center">
                                <helpers:TextBlockWithSelection.ToolTip>
                                    <StackPanel>
                                        <TextBlock Text="{Binding TextPrev}" />
                                        <Image Margin="0,0,0,2" Source="{Binding ImagePrev}" />
                                    </StackPanel>
                                </helpers:TextBlockWithSelection.ToolTip>
                            </helpers:TextBlockWithSelection>
                            <Border
                                Background="Transparent"
                                Cursor="Hand"
                                Grid.Column="2"
                                Margin="0,0,0,0"
                                MouseUp="OpenMenuMouseDown"
                                VerticalAlignment="Center">
                                <wpf:MaterialIcon Kind="DotsVertical" />
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type telerik:RadListBoxItem}}, Path=IsMouseOver}" Value="True">
                                                <Setter Property="Visibility" Value="Visible" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type telerik:RadListBoxItem}}, Path=IsMouseOver}" Value="False">
                                                <Setter Property="Visibility" Value="Hidden" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>
                            </Border>
                        </Grid>
                    </Border>
                </DataTemplate>
            </telerik:RadListBox.ItemTemplate>
            <telerik:RadListBox.ItemContainerStyle>
                <Style BasedOn="{StaticResource {x:Type telerik:RadListBoxItem}}" TargetType="telerik:RadListBoxItem">
                    <Setter Property="Padding" Value="0" />
                    <Setter Property="Margin" Value="0" />
                    <Setter Property="MinHeight" Value="0" />
                    <Setter Property="Background" Value="{telerik:Windows11Resource ResourceKey=OverlayBrush}" />
                </Style>
            </telerik:RadListBox.ItemContainerStyle>
        </telerik:RadListBox>
    </Grid>
</UserControl>
