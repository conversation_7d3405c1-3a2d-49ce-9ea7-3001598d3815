﻿using Everylang.App.Callback;
using Everylang.App.HookManager;
using Everylang.App.HookManager.GlobalHooks;
using Everylang.App.SwitcherLang;
using Everylang.App.Utilities;
using Everylang.App.ViewModels;
using System;
using System.Threading;
using System.Windows;
using System.Windows.Threading;
using Vanara.PInvoke;

namespace Everylang.App.Clipboard
{
    static class ClipboardKeyHookManager
    {
        internal static void Start()
        {
            ClipboardHookManager.Instance.KeyCombinationPressedClipboardPasteWithoutFormatting += KeyCombinationPressedClipboardPasteWithoutFormattingPressed;
            ClipboardHookManager.Instance.KeyCombinationPressedClipboardView += KeyCombinationPressedClipboardViewPressed;
            ClipboardHookManager.Instance.KeyCombinationPressedClipboardRoundPaste += KeyCombinationPressedClipboardRoundPastePressed;
            ClipboardHookManager.Instance.KeyCombinationPressedClipboardPasteByIndex += KeyCombinationPressedClipboardPasteByIndexPressed;
            HookCallBackMouseUp.CallbackEventHandler += HookManagerMouseClick;
        }

        private static void HookManagerMouseClick(GlobalMouseEventArgs globalMouseEventArgs)
        {
            if (CallCounter != -1)
            {
                var currentWindow = User32.GetForegroundWindow().DangerousGetHandle();
                if (currentWindow != _currentWindow)
                {
                    CallCounter = -1;
                }
                _currentWindow = currentWindow;
            }
        }

        // последовательная вставка текста
        private static IntPtr _currentWindow;
        internal static int CallCounter;
        private static void KeyCombinationPressedClipboardRoundPastePressed()
        {
            var currentWindow = User32.GetForegroundWindow().DangerousGetHandle();
            if (currentWindow != _currentWindow)
            {
                CallCounter = 0;
            }
            else
            {
                CallCounter += 1;
            }
            _currentWindow = currentWindow;
            try
            {
                if (VMContainer.Instance.ClipboardViewModel.AllClipboardItems.Count > CallCounter)
                {
                    ClipboardMonitorWorker.IgnoreLast = true;
                    SendText.SendStringByPaste(VMContainer.Instance.ClipboardViewModel.AllClipboardItems[CallCounter].Text, false, true);
                }
            }
            catch (Exception)
            {
                CallCounter = 0;
            }
        }

        // показать историю буфера
        private static void KeyCombinationPressedClipboardViewPressed()
        {
            GlobalEventsApp.OnEventClipboardView();
        }


        // вставка текста без форматирования
        internal static void KeyCombinationPressedClipboardPasteWithoutFormattingPressed()
        {
            DispatcherHelper.RunAsStaThread(PasteTextWithoutFormatting);
        }

        private static bool _pasteTextWithoutFormattingStarting;

        internal static void PasteTextWithoutFormatting()
        {
            if (_pasteTextWithoutFormattingStarting)
            {
                return;
            }
            _pasteTextWithoutFormattingStarting = true;

            using var clipboardState = new ClipboardStateManager();
            clipboardState.Initialize();

            KeyboardState.ReleaseAllKeys();
            Thread.Sleep(50);

            var text = ClipboardOperations.GetText();
            if (text != "")
            {
                ClipboardOperations.SetText(text);
                Application.Current.Dispatcher.Invoke(DispatcherPriority.Background,
                    new Action(delegate { }));

                ClipboardOperations.SendPasteText();

                Thread.Sleep(50);
                Application.Current.Dispatcher.Invoke(DispatcherPriority.Background,
                    new Action(delegate { }));
            }
            _pasteTextWithoutFormattingStarting = false;
        }

        // Вставка текста по индексу
        private static void KeyCombinationPressedClipboardPasteByIndexPressed(int index)
        {
            if (VMContainer.Instance.ClipboardViewModel.AllClipboardItems.Count > index)
            {
                ClipboardMonitorWorker.IgnoreLast = true;
                ClipboardOperations.SetText(VMContainer.Instance.ClipboardViewModel.AllClipboardItems[index].Text);
                Application.Current.Dispatcher.Invoke(DispatcherPriority.Background,
                    new Action(delegate { }));
                ClipboardOperations.SendPasteText();
            }
        }
    }
}
