﻿using Everylang.Common.LogManager;
using LiteDB;
using System;
using System.IO;

namespace Everylang.App.Data
{
    internal static class DataBaseManager
    {
        internal static LiteDatabase LiteDb = null!;

        internal static string? DbPath { get; set; }

        internal static void Init()
        {
            LiteDb = new LiteDatabase(DbConStr);
        }

        internal static void DeleteAllData()
        {
            try
            {
                LiteDb.Dispose();
                if (DbPath != null) File.Delete(DbPath);
                LiteDb = new LiteDatabase(DbConStr);
            }
            catch (Exception e)
            {
                Logger.LogTo.Error(e, e.Message);
            }
        }

        internal static ConnectionString DbConStr
        {
            get
            {
                if (DbPath == null) return null!;
                var connString = new ConnectionString
                {
                    Filename = DbPath,
                    Connection = ConnectionType.Direct,
                    Password = "fm33f9h34f348rl9j",
                    Upgrade = true
                };
                return connString;
            }
        }

        internal static void Dispose()
        {
            LiteDb.Dispose();
        }
    }
}
