﻿using Everylang.App.Clipboard;
using Everylang.App.OCR;
using Everylang.App.SettingsApp;
using Everylang.App.Utilities;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows;
using Telerik.Windows.Controls;

namespace Everylang.App.View.Controls.Ocr
{
    /// <summary>
    /// Логика взаимодействия для OcrWaitWindow.xaml
    /// </summary>
    internal partial class OcrWaitWindow
    {

        internal static readonly DependencyProperty IsVisibleProgressProperty =
            DependencyProperty.Register("IsVisibleProgress",
                typeof(bool),
                typeof(OcrWaitWindow),
                new FrameworkPropertyMetadata());

        internal bool IsVisibleProgress
        {
            get { return (bool)GetValue(IsVisibleProgressProperty); }
            set
            {
                SetValue(IsVisibleProgressProperty, value);
            }
        }

        internal static readonly DependencyProperty IsVisibleResultProperty =
            DependencyProperty.Register("IsVisibleResult",
                typeof(bool),
                typeof(OcrWaitWindow),
                new FrameworkPropertyMetadata());

        internal bool IsVisibleResult
        {
            get { return (bool)GetValue(IsVisibleResultProperty); }
            set
            {
                SetValue(IsVisibleResultProperty, value);
            }
        }

        internal OcrWaitWindow()
        {
            InitializeComponent();
            IsVisibleProgress = true;
            IsVisibleResult = false;
        }

        internal async void Show(Bitmap? bitmapArea)
        {
            if (bitmapArea == null)
            {
                return;
            }
            IsOpen = true;
            System.Drawing.Point centrePos = WindowLocation.GetReallyCenterToScreen();
            HorizontalOffset = centrePos.X - this.Width / 2;
            VerticalOffset = centrePos.Y - this.Height / 2;
            IsVisibleProgress = true;
            OcrWorker ocrEngine = new OcrWorker();
            var text = await ocrEngine.ReadImage(bitmapArea, SettingsManager.Settings.OcrLangsList);
            await Task.Delay(300);
            if (!string.IsNullOrEmpty(text))
            {
                ClipboardOperations.SetText(text);
                LabelMu.Content = LocalizationManager.GetString("OcrWaitResult");
            }
            else
            {
                LabelMu.Content = LocalizationManager.GetString("OcrWaitResultFail");
            }
            IsVisibleProgress = false;
            IsVisibleResult = true;
            await Task.Delay(1800);
            IsOpen = false;
        }
    }
}
