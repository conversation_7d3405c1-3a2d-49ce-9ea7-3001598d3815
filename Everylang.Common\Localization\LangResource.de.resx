<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="About" xml:space="preserve">
    <value>Über das Programm</value>
  </data>
  <data name="AvailableNewVersion" xml:space="preserve">
    <value>Neue Version verfügbar, bitte starten Sie die Anwendung neu</value>
  </data>
  <data name="ClearButton" xml:space="preserve">
    <value>Klar</value>
  </data>
  <data name="FirstStartWithEvaluate" xml:space="preserve">
    <value>Testzeitraum für 40 Tage aktiviert, alle Funktionen inklusive</value>
  </data>
  <data name="CloseHeaderButton" xml:space="preserve">
    <value>Schließen</value>
  </data>
  <data name="CopyButton" xml:space="preserve">
    <value>Kopie</value>
  </data>
  <data name="CopyButtonHtml" xml:space="preserve">
    <value>HTML kopieren</value>
  </data>
  <data name="CopyButtonRtf" xml:space="preserve">
    <value>Rtf kopieren</value>
  </data>
  <data name="FastActionIndex" xml:space="preserve">
    <value>Klicken Sie auf die Zahl, um Text einzufügen</value>
  </data>
  <data name="BreakInterButton" xml:space="preserve">
    <value>Text am Zeilenumbruchzeichen teilen</value>
  </data>
  <data name="BreakSpaceButton" xml:space="preserve">
    <value>Text nach Leerzeichen aufteilen</value>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>Ausfahrt</value>
  </data>
  <data name="FixWindow" xml:space="preserve">
    <value>Fenster einfrieren</value>
  </data>
  <data name="FromLang" xml:space="preserve">
    <value>Aus der Sprache:</value>
  </data>
  <data name="ShowHistoryButton" xml:space="preserve">
    <value>Geschichte anzeigen</value>
  </data>
  <data name="HideHeaderButton" xml:space="preserve">
    <value>Zusammenbruch</value>
  </data>
  <data name="MaxHeaderButton" xml:space="preserve">
    <value>Expandieren</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Ja</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>NEIN</value>
  </data>
  <data name="PastButton" xml:space="preserve">
    <value>Einfügen</value>
  </data>
  <data name="GoToMainWindowButton" xml:space="preserve">
    <value>Öffnen Sie das Hauptfenster mit der Übersetzung</value>
  </data>
  <data name="SiteSourceTextButton" xml:space="preserve">
    <value>Öffnen Sie die Website des Übersetzungsdienstes</value>
  </data>
  <data name="OpenMainWindowShortcut" xml:space="preserve">
    <value>Tastenkombination zum Öffnen des Hauptfensters</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Einstellungen</value>
  </data>
  <data name="ListOnMainWindow" xml:space="preserve">
    <value>Liste im Hauptfenster öffnen</value>
  </data>
  <data name="StopWorkingShortcut" xml:space="preserve">
    <value>Tastenkombination zum Deaktivieren aller Funktionen</value>
  </data>
  <data name="WorkingOff" xml:space="preserve">
    <value>Deaktivieren Sie das Programm</value>
  </data>
  <data name="WorkingOn" xml:space="preserve">
    <value>Programm aktivieren</value>
  </data>
  <data name="WorkingOffTitle" xml:space="preserve">
    <value>Das Programm ist deaktiviert</value>
  </data>
  <data name="StayOnTopButton" xml:space="preserve">
    <value>Nicht schließen</value>
  </data>
  <data name="LatinButton" xml:space="preserve">
    <value>Auf Latein</value>
  </data>
  <data name="ToLang" xml:space="preserve">
    <value>Zunge:</value>
  </data>
  <data name="TranslateButton" xml:space="preserve">
    <value>Übersetzen</value>
  </data>
  <data name="TranslateError" xml:space="preserve">
    <value>Während des Übersetzungsvorgangs ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut oder wählen Sie einen anderen Übersetzungsdienst aus</value>
  </data>
  <data name="SoundButton" xml:space="preserve">
    <value>Hören</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Aktualisieren</value>
  </data>
  <data name="SettingHeaderButton" xml:space="preserve">
    <value>EINSTELLUNGEN</value>
  </data>
  <data name="HistoryHeaderButton" xml:space="preserve">
    <value>GESCHICHTE</value>
  </data>
  <data name="ClipboardHeaderButton" xml:space="preserve">
    <value>ZWISCHENABLAGE</value>
  </data>
  <data name="DiaryHeaderButton" xml:space="preserve">
    <value>TAGEBUCH</value>
  </data>
  <data name="SnippetsHeaderButton" xml:space="preserve">
    <value>SCHNITTE</value>
  </data>
  <data name="ProHeaderButton" xml:space="preserve">
    <value>Aktivieren Sie PRO</value>
  </data>
  <data name="HistoryMenuItem" xml:space="preserve">
    <value>Geschichte</value>
  </data>
  <data name="ClipboardMenuItem" xml:space="preserve">
    <value>Zwischenablage</value>
  </data>
  <data name="HotkeysMenuItem" xml:space="preserve">
    <value>Hotkeys</value>
  </data>
  <data name="DiaryMenuItem" xml:space="preserve">
    <value>Tagebuch</value>
  </data>
  <data name="ErrorHotkey" xml:space="preserve">
    <value>Tastenkombination kann nicht registriert werden</value>
  </data>
  <data name="UpdateAvailable" xml:space="preserve">
    <value>Update verfügbar, starten Sie die Anwendung neu</value>
  </data>
  <data name="MinimizedText" xml:space="preserve">
    <value>Die Anwendung wird ausgeführt und minimiert</value>
  </data>
  <data name="AppUpdated" xml:space="preserve">
    <value>Die Anwendung wurde aktualisiert, die Liste der Änderungen finden Sie auf der Website</value>
  </data>
  <data name="AppNotAsAdmin" xml:space="preserve">
    <value>Damit das Programm mit allen Anwendungen ordnungsgemäß funktioniert, müssen Sie es als Administrator ausführen</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Stornieren</value>
  </data>
  <data name="ErrorText" xml:space="preserve">
    <value>Es ist ein Fehler aufgetreten und die Anwendung wird beendet. Bitte senden Sie Text mit <NAME_EMAIL></value>
  </data>
  <data name="InputLanguagesError" xml:space="preserve">
    <value>Auf Ihrem Computer ist für einige Sprachen mehr als ein Tastaturlayout installiert, was sich negativ auf die ordnungsgemäße Funktion der Funktion zum Wechseln des Layouts auswirken kann.</value>
  </data>
  <data name="SetFont" xml:space="preserve">
    <value>Schriftart</value>
  </data>
  <data name="SearchText" xml:space="preserve">
    <value>Finden...</value>
  </data>
  <data name="OnlyPro" xml:space="preserve">
    <value>(PRO-Version)</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Ändern</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>Erstellen</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Alle</value>
  </data>
  <data name="AllApp" xml:space="preserve">
    <value>Alle Programme</value>
  </data>
  <data name="OnlyInProVersion" xml:space="preserve">
    <value>Nur in der PRO-Version</value>
  </data>
  <data name="SystemTrayHide" xml:space="preserve">
    <value>Das Programm wird auf die Taskleiste minimiert</value>
  </data>
  <data name="StartWindowTitle" xml:space="preserve">
    <value>Lesen Sie vor der Verwendung des Programms die Dokumentation</value>
  </data>
  <data name="FastActionTextWindowSearch" xml:space="preserve">
    <value>Registerkarte – Suche.  Esc – Abbrechen und löschen</value>
  </data>
  <data name="noun" xml:space="preserve">
    <value>Substantiv</value>
  </data>
  <data name="pronoun" xml:space="preserve">
    <value>Pronomen</value>
  </data>
  <data name="adjective" xml:space="preserve">
    <value>Adjektiv</value>
  </data>
  <data name="verb" xml:space="preserve">
    <value>Verb</value>
  </data>
  <data name="adverb" xml:space="preserve">
    <value>Adverb</value>
  </data>
  <data name="preposition" xml:space="preserve">
    <value>Vorwand</value>
  </data>
  <data name="conjunction" xml:space="preserve">
    <value>Union</value>
  </data>
  <data name="interjection" xml:space="preserve">
    <value>Zwischenruf</value>
  </data>
  <data name="participle" xml:space="preserve">
    <value>Gemeinschaft</value>
  </data>
  <data name="auxiliary verb" xml:space="preserve">
    <value>Hilfsverb</value>
  </data>
  <data name="parenthetic" xml:space="preserve">
    <value>Einleitendes Wort</value>
  </data>
  <data name="SpellCheckHeader" xml:space="preserve">
    <value>Rechtschreibprüfung</value>
  </data>
  <data name="LabelNoErrors" xml:space="preserve">
    <value>Keine Fehler</value>
  </data>
  <data name="LangNotCorrect" xml:space="preserve">
    <value>Sprache wird nicht unterstützt</value>
  </data>
  <data name="LabelError" xml:space="preserve">
    <value>Es ist ein Fehler aufgetreten</value>
  </data>
  <data name="LabelTextTooLong" xml:space="preserve">
    <value>Der Text ist zu lang</value>
  </data>
  <data name="ButtonClose" xml:space="preserve">
    <value>Schließen</value>
  </data>
  <data name="LabelOptions" xml:space="preserve">
    <value>Optionen:</value>
  </data>
  <data name="LabelOptionsNo" xml:space="preserve">
    <value>Keine Optionen</value>
  </data>
  <data name="LabelOptionsEnd" xml:space="preserve">
    <value>Verifizierung abgeschlossen</value>
  </data>
  <data name="bSkip" xml:space="preserve">
    <value>Überspringen</value>
  </data>
  <data name="bSkipAll" xml:space="preserve">
    <value>Alles überspringen</value>
  </data>
  <data name="bReplace" xml:space="preserve">
    <value>Ersetzen</value>
  </data>
  <data name="bReplaceAll" xml:space="preserve">
    <value>Alles ersetzen</value>
  </data>
  <data name="bReplaceText" xml:space="preserve">
    <value>Text einfügen</value>
  </data>
  <data name="bCopy" xml:space="preserve">
    <value>Kopie</value>
  </data>
  <data name="buttonBack" xml:space="preserve">
    <value>Zurückkehren</value>
  </data>
  <data name="SetDefaultSetting" xml:space="preserve">
    <value>Einstellungen zurücksetzen</value>
  </data>
  <data name="InterKeyboardShortcuts" xml:space="preserve">
    <value>Drücken Sie die Tastenkombination</value>
  </data>
  <data name="Russian" xml:space="preserve">
    <value>Russisch</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>Englisch</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>Französisch</value>
  </data>
  <data name="Italian" xml:space="preserve">
    <value>Italienisch</value>
  </data>
  <data name="Ukrainian" xml:space="preserve">
    <value>ukrainisch</value>
  </data>
  <data name="AboutSettingsHeader" xml:space="preserve">
    <value>ÜBER DAS PROGRAMM</value>
  </data>
  <data name="AboutSettingsLicense" xml:space="preserve">
    <value>Lizenzvereinbarung</value>
  </data>
  <data name="AboutSettingsDesc" xml:space="preserve">
    <value>Ein universeller Assistent für die Arbeit mit Texten in verschiedenen Sprachen</value>
  </data>
  <data name="AboutSettingsVersion" xml:space="preserve">
    <value>Version:</value>
  </data>
  <data name="AboutSettingsUpdateAvailable" xml:space="preserve">
    <value>Neue Version des Programms ist verfügbar</value>
  </data>
  <data name="AboutSettingsUpdate" xml:space="preserve">
    <value>Aktualisieren</value>
  </data>
  <data name="AboutSettingsContactHeader" xml:space="preserve">
    <value>RÜCKMELDUNG</value>
  </data>
  <data name="AboutSettingsContactText" xml:space="preserve">
    <value>Schreiben Sie Ihre Kommentare und/oder Fragen</value>
  </data>
  <data name="AboutResetSettings" xml:space="preserve">
    <value>Setzen Sie alle Programmeinstellungen zurück</value>
  </data>
  <data name="AboutOpenStartWindow" xml:space="preserve">
    <value>Willkommensfenster öffnen</value>
  </data>
  <data name="AboutResetSettingsQuestion" xml:space="preserve">
    <value>Alle Einstellungen und Programmdaten zurücksetzen?</value>
  </data>
  <data name="AboutSettingsContactLink" xml:space="preserve">
    <value>Kontaktformular</value>
  </data>
  <data name="AboutSettingsIsAdmin" xml:space="preserve">
    <value>Das Programm wird als Administrator ausgeführt</value>
  </data>
  <data name="AboutSettingsIsNotAdmin" xml:space="preserve">
    <value>Das Programm wird nicht als Administrator ausgeführt</value>
  </data>
  <data name="AboutSettingsAckowledgementsHeader" xml:space="preserve">
    <value>KOMPONENTEN</value>
  </data>
  <data name="SwitcherSettingsHeader" xml:space="preserve">
    <value>Ändern des Layouts</value>
  </data>
  <data name="SwitcherSettingsKeyboardShortcutsSwitch" xml:space="preserve">
    <value>Layout des letzten Wortes ändern</value>
  </data>
  <data name="SwitcherSettingsIsUseBreak" xml:space="preserve">
    <value>Verwenden Sie Pause</value>
  </data>
  <data name="SwitcherSettingsKeyboardShortcutsSwitchSelected" xml:space="preserve">
    <value>Ändern Sie das Layout des ausgewählten Textes</value>
  </data>
  <data name="SwitcherSettingsMethodSelect" xml:space="preserve">
    <value>Auswählen einer Layout-Umschaltmethode</value>
  </data>
  <data name="SwitcherSettingsSwitchMethod1" xml:space="preserve">
    <value>Emulation von Tasten zum Wechseln von Layouts</value>
  </data>
  <data name="SwitcherSettingsSwitchMethod2" xml:space="preserve">
    <value>Ausführen eines Windows-Befehls</value>
  </data>
  <data name="SwitcherSettingsLeaveTextSelectedAfterSwitch" xml:space="preserve">
    <value>Lassen Sie den Text nach dem Layoutwechsel ausgewählt</value>
  </data>
  <data name="SwitcherSettingsIsOn" xml:space="preserve">
    <value>Layoutumschaltung aktiviert</value>
  </data>
  <data name="SwitcherSettingsSwitcherCtrlNumberIsOn" xml:space="preserve">
    <value>Verwenden Sie Strg+(Ziffer), um zu einer bestimmten Sprache zu wechseln</value>
  </data>
  <data name="SwitcherSettingsIsUseShift" xml:space="preserve">
    <value>Doppelklicken Sie mit der Umschalttaste</value>
  </data>
  <data name="SwitcherSettingsIsOnInsert" xml:space="preserve">
    <value>Vom Zeilenanfang wechseln</value>
  </data>
  <data name="SwitcherSettingsIsUseScrollLock" xml:space="preserve">
    <value>Doppelklicken Sie auf die ScrollLock-Taste</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOn" xml:space="preserve">
    <value>Wechseln Sie das Layout per Schaltfläche</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnStandart" xml:space="preserve">
    <value>Systemeinstellungen</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRCtrl" xml:space="preserve">
    <value>Rechte Strg-Taste</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLCtrl" xml:space="preserve">
    <value>Linke Strg-Taste</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRShift" xml:space="preserve">
    <value>Rechtsverschiebung</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLShift" xml:space="preserve">
    <value>Linksverschiebung</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLRCtrl" xml:space="preserve">
    <value>Rechte oder linke Strg-Taste</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLRShift" xml:space="preserve">
    <value>Rechts- oder Linksverschiebung</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRCtrlOrCapsLock" xml:space="preserve">
    <value>Rechte Strg-Taste oder Feststelltaste</value>
  </data>
  <data name="SwitcherSettingsToolTipForCurrentSwitchOnKey" xml:space="preserve">
    <value>Um die CapsLock-Funktion zu aktivieren oder zu deaktivieren, drücken Sie gleichzeitig die rechte und linke Umschalttaste</value>
  </data>
  <data name="SwitcherSettingsSwitcherSountIsOn" xml:space="preserve">
    <value>Ton beim Layoutwechsel</value>
  </data>
  <data name="SwitcherSettingsSoundEdit" xml:space="preserve">
    <value>Einstellen des Tons zum Wechseln des Layouts</value>
  </data>
  <data name="SwitcherSettingsTrueListOfLang" xml:space="preserve">
    <value>Liste der Sprachen, auf die das Layout umgestellt wird</value>
  </data>
  <data name="SwitcherLangAndKeysForSwitch" xml:space="preserve">
    <value>Festlegen der Tasten zum Umschalten auf eine bestimmte Sprache</value>
  </data>
  <data name="SwitcherSettingsAskToDeactivateAutoswitcherOff" xml:space="preserve">
    <value>Automatischen Layoutwechsel deaktivieren?</value>
  </data>
  <data name="SwitcherSettingsAskToDeactivateAutoswitcherOn" xml:space="preserve">
    <value>Automatischen Layoutwechsel aktivieren?</value>
  </data>
  <data name="AutoSwitcherSettingsHeader" xml:space="preserve">
    <value>Automatischer Layoutwechsel</value>
  </data>
  <data name="SwitcherSettingsHeaderAuto" xml:space="preserve">
    <value>Automatische Umschaltung aktiviert</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnTwoUpperCaseLetters" xml:space="preserve">
    <value>Korrigieren Sie zwei Großbuchstaben am Anfang eines Wortes</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnFixWrongUpperCase" xml:space="preserve">
    <value>Behebung eines versehentlichen Drückens der Feststelltaste</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnUpperCaseNotSwitch" xml:space="preserve">
    <value>Wechseln Sie nicht, wenn alle Buchstaben in einem Wort groß geschrieben sind</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnByEnter" xml:space="preserve">
    <value>Korrigieren Sie das Layout, nachdem Sie die Eingabetaste gedrückt haben</value>
  </data>
  <data name="AutoSwitcherSettingsIsSwitchOneLetter" xml:space="preserve">
    <value>Fügen Sie den Regeln Wörter mit einem Buchstaben hinzu</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnAddingRule" xml:space="preserve">
    <value>Schaltregeln automatisch hinzufügen</value>
  </data>
  <data name="AutoSwitcherSettingsDisableAutoSwitchAfterManualSwitch" xml:space="preserve">
    <value>Korrigieren Sie das Layout nicht, wenn es zuvor manuell geändert wurde</value>
  </data>
  <data name="AutoSwitcherSettingsOnlyAfterSeparator" xml:space="preserve">
    <value>Wechseln Sie das Layout erst, nachdem Sie ein ganzes Wort eingegeben haben</value>
  </data>
  <data name="AutoSwitcherSettingsAfterPause" xml:space="preserve">
    <value>Wechseln Sie das Layout, nachdem Sie mit der Eingabe aufgehört haben</value>
  </data>
  <data name="AutoSwitcherSettingsResetRule" xml:space="preserve">
    <value>Löschen Sie alle Regeln für die automatische Umschaltung</value>
  </data>
  <data name="AutoSwitcherSettingsCombination" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="AutoSwitcherSettingsAllLayouts" xml:space="preserve">
    <value>Alle Layouts</value>
  </data>
  <data name="AutoSwitcherSettingsAction" xml:space="preserve">
    <value>Aktion</value>
  </data>
  <data name="AutoSwitcherSettingsShowAcceptWindow" xml:space="preserve">
    <value>Fügen Sie Regeln erst nach Bestätigung hinzu</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionConvert" xml:space="preserve">
    <value>Schalten</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionNotConvert" xml:space="preserve">
    <value>Nicht wechseln</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionIntermediate" xml:space="preserve">
    <value>Kandidat</value>
  </data>
  <data name="AutoSwitcherSettingsHelpWindowTitle" xml:space="preserve">
    <value>Regeln für den automatischen Wechsel</value>
  </data>
  <data name="AutoSwitcherSettingsTrueListOfLang" xml:space="preserve">
    <value>Liste der Sprachen, für die die automatische Umschaltung funktioniert</value>
  </data>
  <data name="AutoSwitcherSettingsListRulesHeader" xml:space="preserve">
    <value>Liste der Regeln für die automatische Umschaltung</value>
  </data>
  <data name="AutoSwitcherSettingsListRulesHeaderShowAll" xml:space="preserve">
    <value>Kandidaten anzeigen</value>
  </data>
  <data name="AutoSwitcherSettingsOpenRulesList" xml:space="preserve">
    <value>Öffnen Sie die Liste der Regeln für die automatische Umschaltung</value>
  </data>
  <data name="AutoSwitcherSettingsCountCheckRule" xml:space="preserve">
    <value>Anzahl der manuellen Änderungen des Wortlayouts zur Aufnahme in die Regeln</value>
  </data>
  <data name="AutoSwitchAcceptText" xml:space="preserve">
    <value>Ein Wort zu den Auto-Switch-Regeln hinzufügen? Geben Sie ein - JA</value>
  </data>
  <data name="IsLangInfoWindowShowForMouse" xml:space="preserve">
    <value>Aktuelle Eingabesprache auf dem Mauszeiger</value>
  </data>
  <data name="IsLangInfoWindowShowForCaret" xml:space="preserve">
    <value>Aktuelle Eingabesprache im Textcursor</value>
  </data>
  <data name="IsLangInfoWindowShowLargeWindow" xml:space="preserve">
    <value>Separates Sprachanzeigefenster</value>
  </data>
  <data name="IsLangInfoInTray" xml:space="preserve">
    <value>Aktuelle Sprache in der Taskleiste</value>
  </data>
  <data name="IsLangInfoWindowShowForCaretEx" xml:space="preserve">
    <value>Erweiterte Funktionen</value>
  </data>
  <data name="IsLangInfoShowIconsImage" xml:space="preserve">
    <value>Landesflagge zeigen</value>
  </data>
  <data name="IsLangInfoShowIconsText" xml:space="preserve">
    <value>Sprachnamen anzeigen</value>
  </data>
  <data name="OpacityIconLangInfo" xml:space="preserve">
    <value>Indikatortransparenz</value>
  </data>
  <data name="SizeIconLangInfo" xml:space="preserve">
    <value>Erhöhung der Größe des Indikators in Prozent</value>
  </data>
  <data name="IsIndicateCurrentLangInKeyboardLed" xml:space="preserve">
    <value>Anzeige der aktuellen Sprache auf der Tastatur</value>
  </data>
  <data name="PosMouse" xml:space="preserve">
    <value>Position des Indikators auf dem Mauszeiger</value>
  </data>
  <data name="IsHideIndicateInFullScreenApp" xml:space="preserve">
    <value>Blenden Sie die Anzeige in Programmen aus, die im Vollbildmodus ausgeführt werden</value>
  </data>
  <data name="IsIndicateCapsLockState" xml:space="preserve">
    <value>CapsLock-Status anzeigen</value>
  </data>
  <data name="StatusButtonCapsLockIsOn" xml:space="preserve">
    <value>CapsLock aktiviert</value>
  </data>
  <data name="PosCarret" xml:space="preserve">
    <value>Position des Indikators im Textcursor</value>
  </data>
  <data name="SpellcheckingSettingsHeader" xml:space="preserve">
    <value>Rechtschreibprüfung</value>
  </data>
  <data name="SpellcheckingSettingsIsOn" xml:space="preserve">
    <value>Rechtschreibprüfung aktiviert</value>
  </data>
  <data name="SpellcheckingSettingsWhileTyping" xml:space="preserve">
    <value>Überprüfen Sie beim Tippen die Rechtschreibung</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingSoundOn" xml:space="preserve">
    <value>Ton der Rechtschreibprüfung beim Tippen</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingUseNumber" xml:space="preserve">
    <value>Verwenden Sie Nummern für einen schnellen Austausch</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingSoundEdit" xml:space="preserve">
    <value>Toneinstellungen für die Rechtschreibprüfung</value>
  </data>
  <data name="SpellcheckingKeyboardShortcuts" xml:space="preserve">
    <value>Tastenkombination zum Überprüfen der Rechtschreibung des ausgewählten Textes</value>
  </data>
  <data name="SpellcheckingKeyboardShortcutsShort" xml:space="preserve">
    <value>Überprüfen Sie die Rechtschreibung des ausgewählten Textes</value>
  </data>
  <data name="SpellcheckingSettingsCloseByTimer" xml:space="preserve">
    <value>Schließen Sie das Fenster, wenn nach 3 Sekunden keine Fehler vorliegen</value>
  </data>
  <data name="ClipboardSettingsHeader" xml:space="preserve">
    <value>Zwischenablage-Manager</value>
  </data>
  <data name="ClipboardKeyboardShortcuts" xml:space="preserve">
    <value>Fügen Sie Text ohne Formatierung ein und fügen Sie den Pfad der kopierten Datei ein</value>
  </data>
  <data name="ClipboardOn" xml:space="preserve">
    <value>Zwischenablage-Manager aktiviert</value>
  </data>
  <data name="ClipboardKeyboardViewShortcuts" xml:space="preserve">
    <value>Öffnen Sie den Verlauf der Zwischenablage mit der Tastenkombination</value>
  </data>
  <data name="ClipboardKeyboardRoundShortcuts" xml:space="preserve">
    <value>Nacheinander Text aus dem Zwischenablageverlauf für das aktuelle Fenster einfügen</value>
  </data>
  <data name="ClipboardKeyboardRoundShortcutsShort" xml:space="preserve">
    <value>Nacheinander Text aus dem Zwischenablageverlauf einfügen</value>
  </data>
  <data name="ClipboardSettingsPasteByIndexIsOn" xml:space="preserve">
    <value>Text mit Strg+Umschalt+(Zahl) einfügen – Nummer 1, 2, 3, 4, 5, 6, 7, 8, 9 – Index des Eintrags im Verlauf der Zwischenablage</value>
  </data>
  <data name="ClipboardSettingsSaveFilePath" xml:space="preserve">
    <value>Speichern Sie den Pfad zur kopierten Datei im Verlauf der Zwischenablage</value>
  </data>
  <data name="ClipboardSettingsSaveImage" xml:space="preserve">
    <value>Speichern Sie den Bildpufferverlauf</value>
  </data>
  <data name="ClipboardSettingsReplaceWithoutChangeClipboard" xml:space="preserve">
    <value>Ersetzen Sie beim Einfügen von Text aus dem Verlauf den aktuellen Wert in der Zwischenablage</value>
  </data>
  <data name="ClipboardMaxClipboardItems" xml:space="preserve">
    <value>Größe des Zwischenablageverlaufs</value>
  </data>
  <data name="ClipboardSound" xml:space="preserve">
    <value>Ton in der Zwischenablage ändern</value>
  </data>
  <data name="ConverterSettingsHeader" xml:space="preserve">
    <value>Textkonverter</value>
  </data>
  <data name="ConverterSettingsConvertDependsOnKeyboardLayout" xml:space="preserve">
    <value>Konvertieren Sie basierend auf dem aktuellen Tastaturlayout</value>
  </data>
  <data name="ConverterSettingsExpression" xml:space="preserve">
    <value>Konvertieren Sie Zahlen und Datumsangaben in Zeichenfolgen und werten Sie Ausdrücke aus</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchCapsSettings" xml:space="preserve">
    <value>Hotkeys einrichten</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsInvert" xml:space="preserve">
    <value>Kehrt die Groß-/Kleinschreibung des ausgewählten Texts um</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsUp" xml:space="preserve">
    <value>Konvertieren Sie den ausgewählten Text in Großbuchstaben</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsDown" xml:space="preserve">
    <value>Ausgewählter Text in Kleinbuchstaben</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsFirstLetterToDown" xml:space="preserve">
    <value>Schreiben Sie das erste Zeichen des Worts unter dem Cursor in Kleinbuchstaben</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsFirstLetterToUp" xml:space="preserve">
    <value>Schreiben Sie das erste Zeichen des Worts unter dem Cursor in Großbuchstaben</value>
  </data>
  <data name="ConverterSettingsTransliteration" xml:space="preserve">
    <value>Ausgewählten Text transkribieren</value>
  </data>
  <data name="ConverterSettingsCamelCase" xml:space="preserve">
    <value>Konvertieren Sie Text in den CamelCase-Stil</value>
  </data>
  <data name="ConverterReplaceSelText" xml:space="preserve">
    <value>Suchen und ersetzen Sie Text im ausgewählten Text</value>
  </data>
  <data name="ConverterSettingsEncloseTextQuotationMarks" xml:space="preserve">
    <value>Ausgewählten Text mit Symbolen einrahmen (Beispiel)</value>
  </data>
  <data name="ConverterAdd" xml:space="preserve">
    <value>Hinzufügen</value>
  </data>
  <data name="ConverterStart" xml:space="preserve">
    <value>Links</value>
  </data>
  <data name="ConverterEnd" xml:space="preserve">
    <value>Rechts</value>
  </data>
  <data name="ConverterDelete" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="ConverterSampleText" xml:space="preserve">
    <value>Beispieltext</value>
  </data>
  <data name="TransCloseHeaderButton" xml:space="preserve">
    <value>ESC schließen</value>
  </data>
  <data name="TransReplaceTextButton" xml:space="preserve">
    <value>Ersetzen Sie den Text mit der EINGABETASTE</value>
  </data>
  <data name="TransSettingsHeader" xml:space="preserve">
    <value>Übersetzung</value>
  </data>
  <data name="TransSettingsNativeLanguage" xml:space="preserve">
    <value>Standardsprache für die Übersetzung</value>
  </data>
  <data name="TransSettingsLanguageFromTranslate" xml:space="preserve">
    <value>Standardsprache für die Übersetzung</value>
  </data>
  <data name="TransSettingsMainLanguageForTheTranslation" xml:space="preserve">
    <value>Hauptsprache für die Übersetzung</value>
  </data>
  <data name="TransSettingsProviderOfTranslation" xml:space="preserve">
    <value>Übersetzungsdienst</value>
  </data>
  <data name="TransSettingsKeyboardShortcuts" xml:space="preserve">
    <value>Tastenkombination zum Übersetzen von ausgewähltem Text</value>
  </data>
  <data name="TransSettingsInterKeyboardShortcuts" xml:space="preserve">
    <value>Drücken Sie die Tastenkombination</value>
  </data>
  <data name="TransSettingsUseDoubleCtrl" xml:space="preserve">
    <value>Doppelklicken Sie auf die Strg-Taste</value>
  </data>
  <data name="TransSettingsTranslationIsAlways" xml:space="preserve">
    <value>Übersetzen Sie, wenn Sie Text mit der Maus auswählen</value>
  </data>
  <data name="TransSettingsIsOn" xml:space="preserve">
    <value>Übersetzung inklusive</value>
  </data>
  <data name="GeneralSettingsHeader" xml:space="preserve">
    <value>Allgemeine Einstellungen</value>
  </data>
  <data name="GeneralSettingsLanguageProgram" xml:space="preserve">
    <value>Sprache der Programmoberfläche</value>
  </data>
  <data name="GeneralSettingsLanguageProgramRestart" xml:space="preserve">
    <value>Anwendung neu starten, um die Sprache zu ändern?</value>
  </data>
  <data name="GeneralSettingsOther" xml:space="preserve">
    <value>Verschiedenes</value>
  </data>
  <data name="GeneralSettingsMinimizeToTray" xml:space="preserve">
    <value>Auf das Fach minimieren</value>
  </data>
  <data name="GeneralSettingsStartUpWithWindows" xml:space="preserve">
    <value>Läuft von Windows aus</value>
  </data>
  <data name="GeneralSettingsStartAdmin" xml:space="preserve">
    <value>Mit Administratorrechten ausführen</value>
  </data>
  <data name="GeneralSettingsIsCheckUpdate" xml:space="preserve">
    <value>Suchen Sie nach Updates</value>
  </data>
  <data name="GeneralSettingsIsCheckUpdateBeta" xml:space="preserve">
    <value>Update auf Beta-Version</value>
  </data>
  <data name="GeneralSettingsTheme" xml:space="preserve">
    <value>Thema</value>
  </data>
  <data name="GeneralSettingsThemeDayNight" xml:space="preserve">
    <value>Tag oder Nacht</value>
  </data>
  <data name="GeneralSettingsThemeAccent" xml:space="preserve">
    <value>Designstile</value>
  </data>
  <data name="GeneralSettingsDataFilePath" xml:space="preserve">
    <value>Ordner zum Speichern der Programmeinstellungen</value>
  </data>
  <data name="GeneralSettingsIsProxyUseIE" xml:space="preserve">
    <value>Verwenden Sie System-Proxy-Einstellungen</value>
  </data>
  <data name="GeneralSettingsProxyServer" xml:space="preserve">
    <value>Geben Sie die Serveradresse ein</value>
  </data>
  <data name="GeneralSettingsProxyPort" xml:space="preserve">
    <value>Geben Sie den Port ein</value>
  </data>
  <data name="GeneralSettingsProxyUsername" xml:space="preserve">
    <value>Geben Sie Ihren Benutzernamen ein</value>
  </data>
  <data name="GeneralSettingsProxyPassword" xml:space="preserve">
    <value>Geben Sie Ihr Passwort ein</value>
  </data>
  <data name="GeneralSettingsSaveProxy" xml:space="preserve">
    <value>Proxy-Einstellungen speichern</value>
  </data>
  <data name="GeneralSettingsProxy" xml:space="preserve">
    <value>Proxy-Einstellungen</value>
  </data>
  <data name="GeneralSettingsProxyError" xml:space="preserve">
    <value>Fehler bei den Proxy-Server-Einstellungen, Einstellungen ändern</value>
  </data>
  <data name="GeneralSettingsUseNightTheme" xml:space="preserve">
    <value>Verwenden Sie in den Abendstunden ein dunkles Thema</value>
  </data>
  <data name="GeneralSettingsUseNightThemeStart" xml:space="preserve">
    <value>Abendstunden ab</value>
  </data>
  <data name="GeneralSettingsUseNightThemeEnd" xml:space="preserve">
    <value>Abendstunden bis</value>
  </data>
  <data name="GeneralSettingsIsStopWorkingFullScreen" xml:space="preserve">
    <value>Deaktivieren Sie alle Funktionen in Programmen, die im Vollbildmodus ausgeführt werden</value>
  </data>
  <data name="GeneralSettingsImport" xml:space="preserve">
    <value>Einstellungen importieren</value>
  </data>
  <data name="GeneralSettingsExport" xml:space="preserve">
    <value>Einstellungen exportieren</value>
  </data>
  <data name="GeneralTab" xml:space="preserve">
    <value>Grundeinstellungen</value>
  </data>
  <data name="TranslationTab" xml:space="preserve">
    <value>Übersetzer</value>
  </data>
  <data name="CheckSpellingTab" xml:space="preserve">
    <value>Rechtschreibprüfung</value>
  </data>
  <data name="LangFlagTab" xml:space="preserve">
    <value>Layout-Indikator</value>
  </data>
  <data name="ProTabs" xml:space="preserve">
    <value>PRO-Funktionen</value>
  </data>
  <data name="ClipboardTab" xml:space="preserve">
    <value>Zwischenablage</value>
  </data>
  <data name="DiareTab" xml:space="preserve">
    <value>Tagebuch</value>
  </data>
  <data name="ConverterTab" xml:space="preserve">
    <value>Textkonverter</value>
  </data>
  <data name="SwitcherTab" xml:space="preserve">
    <value>Layoutwechsel</value>
  </data>
  <data name="AutoSwitcherTab" xml:space="preserve">
    <value>Automatischer Schalter</value>
  </data>
  <data name="ProgramsExceptionsTab" xml:space="preserve">
    <value>Ausnahmeprogramme</value>
  </data>
  <data name="ProgramsSetLayoutTab" xml:space="preserve">
    <value>Standardlayouts</value>
  </data>
  <data name="UniversalWindowTab" xml:space="preserve">
    <value>SmartClick</value>
  </data>
  <data name="AboutTab" xml:space="preserve">
    <value>Über das Programm</value>
  </data>
  <data name="ProTab" xml:space="preserve">
    <value>Lizenz</value>
  </data>
  <data name="AutochangeTab" xml:space="preserve">
    <value>Ausschnitte</value>
  </data>
  <data name="UniTranslate" xml:space="preserve">
    <value>Übersetzen</value>
  </data>
  <data name="UniCopy" xml:space="preserve">
    <value>Kopie</value>
  </data>
  <data name="UniSpellCheck" xml:space="preserve">
    <value>Überprüfen Sie die Rechtschreibung</value>
  </data>
  <data name="UniSearch" xml:space="preserve">
    <value>Rufen Sie die Suche an</value>
  </data>
  <data name="UniLink" xml:space="preserve">
    <value>Link im Browser öffnen</value>
  </data>
  <data name="UniLinkTranslate" xml:space="preserve">
    <value>Übersetzen Sie die Website über den Link</value>
  </data>
  <data name="UniLinkShorter" xml:space="preserve">
    <value>Generieren eines Kurzlinks</value>
  </data>
  <data name="UniEmail" xml:space="preserve">
    <value>Erstellen Sie eine E-Mail-Nachricht</value>
  </data>
  <data name="UniPaste" xml:space="preserve">
    <value>Text einfügen</value>
  </data>
  <data name="UniPasteUnf" xml:space="preserve">
    <value>Text ohne Formatierung einfügen</value>
  </data>
  <data name="UniClipboardHistory" xml:space="preserve">
    <value>Verlauf der Zwischenablage öffnen</value>
  </data>
  <data name="UniTranslateHistory" xml:space="preserve">
    <value>Übersetzungsverlauf öffnen</value>
  </data>
  <data name="UniDiaryHistory" xml:space="preserve">
    <value>Offenes Tagebuch</value>
  </data>
  <data name="UniUppercase" xml:space="preserve">
    <value>Ändern Sie die Groß-/Kleinschreibung des ausgewählten Texts</value>
  </data>
  <data name="UniAutochange" xml:space="preserve">
    <value>Liste der Snippets öffnen</value>
  </data>
  <data name="UniConverter" xml:space="preserve">
    <value>Textkonverter</value>
  </data>
  <data name="UniDownCase" xml:space="preserve">
    <value>Ausgewählter Text in Kleinbuchstaben</value>
  </data>
  <data name="UniUpCase" xml:space="preserve">
    <value>Konvertieren Sie den ausgewählten Text in Großbuchstaben</value>
  </data>
  <data name="UniInvertCase" xml:space="preserve">
    <value>Kehrt die Groß-/Kleinschreibung des ausgewählten Texts um</value>
  </data>
  <data name="UniEnclose" xml:space="preserve">
    <value>Rahmen Sie den ausgewählten Text mit Symbolen ein</value>
  </data>
  <data name="UniTranslit" xml:space="preserve">
    <value>Ausgewählten Text transkribieren</value>
  </data>
  <data name="UniConvertExpressions" xml:space="preserve">
    <value>Konvertieren Sie Zahlen und Datumsangaben in Zeichenfolgen und werten Sie Ausdrücke aus</value>
  </data>
  <data name="UniTextConverter" xml:space="preserve">
    <value>Konverter</value>
  </data>
  <data name="UniTextTranslate" xml:space="preserve">
    <value>Übersetzung</value>
  </data>
  <data name="UniTextCopy" xml:space="preserve">
    <value>Kopie</value>
  </data>
  <data name="UniTextSpellCheck" xml:space="preserve">
    <value>Rechtschreibung</value>
  </data>
  <data name="UniTextSearch" xml:space="preserve">
    <value>Suchen</value>
  </data>
  <data name="UniTextLink" xml:space="preserve">
    <value>Link</value>
  </data>
  <data name="UniTextLinkTranslate" xml:space="preserve">
    <value>Website-Übersetzung</value>
  </data>
  <data name="UniTextLinkShorter" xml:space="preserve">
    <value>URL kürzer</value>
  </data>
  <data name="UniTextEmail" xml:space="preserve">
    <value>E-Mail</value>
  </data>
  <data name="UniTextPaste" xml:space="preserve">
    <value>Einfügen</value>
  </data>
  <data name="UniTextPasteUnf1" xml:space="preserve">
    <value>Einfügen ohne Format.</value>
  </data>
  <data name="UniTextPasteUnf2" xml:space="preserve">
    <value>ohne Format.</value>
  </data>
  <data name="UniTextClipboardHistory1" xml:space="preserve">
    <value>Pufferverlauf</value>
  </data>
  <data name="UniTextClipboardHistory2" xml:space="preserve">
    <value>Puffer</value>
  </data>
  <data name="UniTextDiaryHistory" xml:space="preserve">
    <value>Tagebuch</value>
  </data>
  <data name="UniTextAutochange" xml:space="preserve">
    <value>Ausschnitte</value>
  </data>
  <data name="UniTextDownCase" xml:space="preserve">
    <value>Registrieren Sie sich</value>
  </data>
  <data name="UniTextUpCase" xml:space="preserve">
    <value>Registrieren Sie sich</value>
  </data>
  <data name="UniTextInvertCase" xml:space="preserve">
    <value>Register umkehren</value>
  </data>
  <data name="UniTextEnclose" xml:space="preserve">
    <value>Rahmentext</value>
  </data>
  <data name="UniTextTranslit" xml:space="preserve">
    <value>Transliteration</value>
  </data>
  <data name="UniTextConvertExpressions" xml:space="preserve">
    <value>Matte. Ausdrücke</value>
  </data>
  <data name="HistoryDateStart" xml:space="preserve">
    <value>Start:</value>
  </data>
  <data name="HistoryDateEnd" xml:space="preserve">
    <value>Ende:</value>
  </data>
  <data name="HistoryDelSelected" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="HistoryClear" xml:space="preserve">
    <value>Klar</value>
  </data>
  <data name="HistoryToggleSwitch" xml:space="preserve">
    <value>Im Lieferumfang enthalten</value>
  </data>
  <data name="DiaryHeaderText" xml:space="preserve">
    <value>TEXT</value>
  </data>
  <data name="DiaryHeaderApp" xml:space="preserve">
    <value>ANWENDUNG</value>
  </data>
  <data name="ProgramsExceptionsHeader" xml:space="preserve">
    <value>Ausnahmeprogramme</value>
  </data>
  <data name="ProgramsExceptionsAddNew" xml:space="preserve">
    <value>Hinzufügen</value>
  </data>
  <data name="ProgramsExceptionsAddNewExeFile" xml:space="preserve">
    <value>Exe-Datei hinzufügen</value>
  </data>
  <data name="ProgramsExceptionsAddNewFilesFromFolder" xml:space="preserve">
    <value>Ordner hinzufügen</value>
  </data>
  <data name="ProgramsExceptionsAddNewTitle" xml:space="preserve">
    <value>Nach Fenstertitel hinzufügen</value>
  </data>
  <data name="ProgramsExceptionsIsOnLayoutFlag" xml:space="preserve">
    <value>Layout-Indikator</value>
  </data>
  <data name="ProgramsExceptionsIsOnLayoutSwitcher" xml:space="preserve">
    <value>Layoutwechsel</value>
  </data>
  <data name="ProgramsExceptionsIsOnSmartClick" xml:space="preserve">
    <value>SmartClick</value>
  </data>
  <data name="ProgramsExceptionsIsOnAutochange" xml:space="preserve">
    <value>Ausschnitte</value>
  </data>
  <data name="ProgramsExceptionsIsOnHotKeys" xml:space="preserve">
    <value>Hotkeys</value>
  </data>
  <data name="ProgramsExceptionsIsOnAutoSwitch" xml:space="preserve">
    <value>Automatische Sprachumschaltung</value>
  </data>
  <data name="ProgramsExceptionsIsOnDiary" xml:space="preserve">
    <value>Tagebuch zur Texteingabe</value>
  </data>
  <data name="ProgramsExceptionsIsOnConverter" xml:space="preserve">
    <value>Konverter und Textfall</value>
  </data>
  <data name="ProgramsExceptionsIsOnClipboard" xml:space="preserve">
    <value>Verlauf der Zwischenablage</value>
  </data>
  <data name="ProgramsExceptionsIsOnClipboardImage" xml:space="preserve">
    <value>Bilder speichern</value>
  </data>
  <data name="ProgramsExceptionsDelete" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="ProgramsExceptionsAddNewHelp" xml:space="preserve">
    <value>Klicken Sie auf das gewünschte Programm</value>
  </data>
  <data name="ProgramsExceptionsCurrentInfoTooltip" xml:space="preserve">
    <value>Markieren Sie die Funktionen, die für das Programm funktionieren</value>
  </data>
  <data name="ProgramsExceptionsProgramsList" xml:space="preserve">
    <value>Hinzufügen aus der Liste der Programme</value>
  </data>
  <data name="ProgramsExceptionsFromPoint" xml:space="preserve">
    <value>Hinzufügen mit dem Cursor</value>
  </data>
  <data name="ProgramsSetLayoutHeader" xml:space="preserve">
    <value>Programmsprachen</value>
  </data>
  <data name="ProgramsSetLayoutTabHeader" xml:space="preserve">
    <value>Standardsprache für ausgewählte Programme</value>
  </data>
  <data name="ProSettingsHeader" xml:space="preserve">
    <value>Aktivierung von PRO-Funktionen</value>
  </data>
  <data name="ProSettingsStatus" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="ProSettingsInput" xml:space="preserve">
    <value>Aktivierungscode</value>
  </data>
  <data name="ProSettingsActivation" xml:space="preserve">
    <value>Aktivieren</value>
  </data>
  <data name="ProSettingsEvaluation" xml:space="preserve">
    <value>Probieren Sie es aus</value>
  </data>
  <data name="ProSettingsStatusOk" xml:space="preserve">
    <value>PRO aktiviert</value>
  </data>
  <data name="ProSettingsStatusEvaluate" xml:space="preserve">
    <value>Probezeit für 40 Tage</value>
  </data>
  <data name="ProSettingsLicenseInfo" xml:space="preserve">
    <value>Lizenzinformationen</value>
  </data>
  <data name="ProSettingsLicenseActivateDate" xml:space="preserve">
    <value>Aktivierungsdatum:</value>
  </data>
  <data name="ProSettingsLicenseExpiryDate" xml:space="preserve">
    <value>Gültigkeitsdauer der Lizenz:</value>
  </data>
  <data name="ProSettingsLicenseEmail" xml:space="preserve">
    <value>E-Mail:</value>
  </data>
  <data name="ProSettingsLicenseUserName" xml:space="preserve">
    <value>Eigentümer:</value>
  </data>
  <data name="ProSettingsGetData" xml:space="preserve">
    <value>Lizenzinformationen</value>
  </data>
  <data name="ProSettingsLicenseUsersCount" xml:space="preserve">
    <value>Anzahl Sitzplätze:</value>
  </data>
  <data name="ProSettingsLicenseCountReact" xml:space="preserve">
    <value>Anzahl der verfügbaren Reaktivierungen:</value>
  </data>
  <data name="ProSettingsLicenseCountFreeSeats" xml:space="preserve">
    <value>Verfügbare Sitzplätze:</value>
  </data>
  <data name="ProSettingsLicenseIsEnterprise" xml:space="preserve">
    <value>Lizenztyp:</value>
  </data>
  <data name="ProSettingsLicenseNotPro" xml:space="preserve">
    <value>PRO nicht aktiviert</value>
  </data>
  <data name="ProSettingsIsEvaluation" xml:space="preserve">
    <value>PRO für den Testzeitraum aktiviert</value>
  </data>
  <data name="ProSettingsPurchase" xml:space="preserve">
    <value>Kaufen</value>
  </data>
  <data name="ProSettingsEmail" xml:space="preserve">
    <value>Geben Sie die E-Mail-Adresse ein</value>
  </data>
  <data name="ProSettingsCode" xml:space="preserve">
    <value>Code eingeben</value>
  </data>
  <data name="ProSendEmailLic" xml:space="preserve">
    <value>Ihre Lizenzinformationen wurden gesendet an:</value>
  </data>
  <data name="ProSettingsDelete" xml:space="preserve">
    <value>Lizenz entfernen</value>
  </data>
  <data name="ProSettingsCodeEmail" xml:space="preserve">
    <value>Geben Sie Code und E-Mail ein</value>
  </data>
  <data name="ProSettingsActivationOk" xml:space="preserve">
    <value>Die Aktivierung wurde erfolgreich abgeschlossen</value>
  </data>
  <data name="ProSettingsActivationError" xml:space="preserve">
    <value>Aktivierung mit Fehler abgeschlossen</value>
  </data>
  <data name="ProSettingsActivationBlocked" xml:space="preserve">
    <value>Aktivierung mit Fehler abgeschlossen, Ihre Lizenz ist gesperrt</value>
  </data>
  <data name="ProSettingsActivationReactivated" xml:space="preserve">
    <value>Am neuen Arbeitsplatz war das Programm aktiviert, auf dem Computer jedoch deaktiviert</value>
  </data>
  <data name="ProSettingsActivationInternetError" xml:space="preserve">
    <value>Die Aktivierung wurde mit einem Fehler abgeschlossen. Überprüfen Sie Ihre Internetverbindung</value>
  </data>
  <data name="ProSettingsActivationErrorEmail" xml:space="preserve">
    <value>Die Aktivierung wurde mit einem Fehler abgeschlossen. Möglicherweise wurde eine falsche E-Mail-Adresse eingegeben</value>
  </data>
  <data name="ProSettingsExpiredEva" xml:space="preserve">
    <value>Die Probezeit ist abgelaufen</value>
  </data>
  <data name="ProSettingsExpired" xml:space="preserve">
    <value>Die Lizenz ist abgelaufen</value>
  </data>
  <data name="ProSettingsBlockedByMonthActivateLimit" xml:space="preserve">
    <value>Eine Aktivierung ist aufgrund der Überschreitung des Reaktivierungslimits nicht möglich. Bitte erwerben Sie zusätzliche Plätze für diese Lizenz</value>
  </data>
  <data name="ProSettingsBlockedByEarlyActivation" xml:space="preserve">
    <value>Eine Aktivierung ist nicht möglich, da diese Lizenz auf einem anderen Computer erneut aktiviert wird</value>
  </data>
  <data name="ProSettingsNewLic" xml:space="preserve">
    <value>Ihr Aktivierungscode wurde aktualisiert</value>
  </data>
  <data name="ProSettingsNoExpiry" xml:space="preserve">
    <value>Unbefristete Lizenz</value>
  </data>
  <data name="ProBlockedByEarlyActivation" xml:space="preserve">
    <value>Sie haben die maximale Anzahl an Sitzplätzen für die Lizenz überschritten. Alle PRO-Funktionen werden deaktiviert</value>
  </data>
  <data name="ProBlockedExp" xml:space="preserve">
    <value>Die PRO-Version ist abgelaufen, eine neue Lizenz kann auf der EVERYLANG.NET-Website erworben werden</value>
  </data>
  <data name="ProBlockedExpToDays" xml:space="preserve">
    <value>Die PRO-Version läuft bald ab</value>
  </data>
  <data name="ProBlockedByMonthActivateLimit" xml:space="preserve">
    <value>Das Lizenz-Reaktivierungslimit der letzten 30 Tage wurde überschritten, alle PRO-Funktionen werden deaktiviert</value>
  </data>
  <data name="ProBlocked" xml:space="preserve">
    <value>Ihre Lizenz ist gesperrt, alle PRO-Funktionen werden deaktiviert</value>
  </data>
  <data name="UniversalWindowSettingsHeader" xml:space="preserve">
    <value>SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsUniversalWindowIsOn" xml:space="preserve">
    <value>SmartClick aktiviert</value>
  </data>
  <data name="UniversalWindowSettingsSearchServices" xml:space="preserve">
    <value>Wählen Sie einen Suchdienst für SmartClick aus</value>
  </data>
  <data name="UniversalWindowSettingsItemsCheck" xml:space="preserve">
    <value>Überprüfen Sie die verfügbaren Funktionen</value>
  </data>
  <data name="UniversalWindowSettingsShowOnPressLeftAndRightMouseButtons" xml:space="preserve">
    <value>Öffnen Sie es, indem Sie die linke und dann die rechte Maustaste drücken</value>
  </data>
  <data name="UniversalWindowSettingsShowOnDoubleMiddle" xml:space="preserve">
    <value>Öffnen Sie es durch einen Doppelklick mit der mittleren Maustaste</value>
  </data>
  <data name="UniversalWindowSettingsShowOnPressHotKeys" xml:space="preserve">
    <value>Verwenden Sie zum Öffnen Hotkeys</value>
  </data>
  <data name="SmartClickShortcutSettingsHeader" xml:space="preserve">
    <value>Hotkeys zum Öffnen des SmartClick-Fensters</value>
  </data>
  <data name="UniversalWindowSettingsShowMiniOn" xml:space="preserve">
    <value>Hilfefenster nach Textauswahl anzeigen</value>
  </data>
  <data name="SmartClickMiniSize" xml:space="preserve">
    <value>Fenstergröße</value>
  </data>
  <data name="SmartClickMiniPos" xml:space="preserve">
    <value>Fensterposition relativ zum Mauszeiger</value>
  </data>
  <data name="DiarySettingsHeader" xml:space="preserve">
    <value>Tagebuch</value>
  </data>
  <data name="DiaryShortcuts" xml:space="preserve">
    <value>Offenes Tagebuch</value>
  </data>
  <data name="DiaryIsOn" xml:space="preserve">
    <value>Tagebuch inklusive</value>
  </data>
  <data name="DiaryPassword" xml:space="preserve">
    <value>Tagebuch-Passwort</value>
  </data>
  <data name="DiaryPasswordOld" xml:space="preserve">
    <value>Geben Sie Ihr altes Tagebuch-Passwort ein</value>
  </data>
  <data name="DiaryPasswordSaved" xml:space="preserve">
    <value>Neues Passwort gespeichert</value>
  </data>
  <data name="DiaryPasswordReset" xml:space="preserve">
    <value>Passwort zurücksetzen</value>
  </data>
  <data name="DiaryMaxItems" xml:space="preserve">
    <value>Anzahl der Einträge im Tagebuch</value>
  </data>
  <data name="DiaryOldPasswordWrong" xml:space="preserve">
    <value>Das eingegebene Passwort ist falsch, aktuelles Passwort zurücksetzen? In diesem Fall werden alle Daten aus dem Tagebuch gelöscht</value>
  </data>
  <data name="IsSaveOneWordSentences" xml:space="preserve">
    <value>Behalten Sie Ein-Wort-Sätze bei</value>
  </data>
  <data name="AutochangeHelperFromText" xml:space="preserve">
    <value>Zu ersetzender Text (optional):</value>
  </data>
  <data name="AutochangeHelperToText" xml:space="preserve">
    <value>Snippet-Text:</value>
  </data>
  <data name="AutochangeHelperLangListDesc" xml:space="preserve">
    <value>Auf welche Sprache soll ich das Layout umstellen?</value>
  </data>
  <data name="AutochangeHelperLangListNoSwitch" xml:space="preserve">
    <value>Nicht wechseln</value>
  </data>
  <data name="AutochangeHelperTags" xml:space="preserve">
    <value>Tags (durch Leerzeichen getrennt):</value>
  </data>
  <data name="AutochangeHelperTagsWatermark" xml:space="preserve">
    <value>Schlagworte</value>
  </data>
  <data name="AutochangeHelperDesc" xml:space="preserve">
    <value>Beschreibung:</value>
  </data>
  <data name="AutochangeHelperOk" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="AutochangeHelperCancel" xml:space="preserve">
    <value>Stornieren</value>
  </data>
  <data name="AutochangeHelperSaveCursorPosition" xml:space="preserve">
    <value>Cursorposition beibehalten</value>
  </data>
  <data name="AutochangeHelperChangeAtOnce" xml:space="preserve">
    <value>Beim Tippen ersetzen</value>
  </data>
  <data name="AutochangeTextHeader" xml:space="preserve">
    <value>Ausschnitte</value>
  </data>
  <data name="ToReplacerButton" xml:space="preserve">
    <value>Neuer Ausschnitt</value>
  </data>
  <data name="AutochangeDelWithTag" xml:space="preserve">
    <value>Alle Snippets mit diesem Tag löschen?</value>
  </data>
  <data name="AutochangeOnInSnippetsList" xml:space="preserve">
    <value>Snippetst aktiviert</value>
  </data>
  <data name="AutochangeSnippetsList" xml:space="preserve">
    <value>Snippets bearbeiten</value>
  </data>
  <data name="AutochangeHeaderFromText" xml:space="preserve">
    <value>Was zu ersetzen ist</value>
  </data>
  <data name="AutochangeHeaderToText" xml:space="preserve">
    <value>Wodurch ersetzen</value>
  </data>
  <data name="AutochangeAddNew" xml:space="preserve">
    <value>Hinzufügen</value>
  </data>
  <data name="AutochangeEdit" xml:space="preserve">
    <value>Ändern</value>
  </data>
  <data name="AutochangeDelete" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="AutochangeOtherLayout" xml:space="preserve">
    <value>Ersetzen Sie, wenn Sie ein anderes Layout eingeben</value>
  </data>
  <data name="AutochangeCaseLetters" xml:space="preserve">
    <value>Passender Briefkasten</value>
  </data>
  <data name="AutochangeShowMiniWindow" xml:space="preserve">
    <value>Hinweis beim Tippen anzeigen</value>
  </data>
  <data name="AutochangeIsEnabledCountUsage" xml:space="preserve">
    <value>Sortieren Sie nach Nutzungshäufigkeit</value>
  </data>
  <data name="AutochangeChangeMethods" xml:space="preserve">
    <value>Ersetzen durch:</value>
  </data>
  <data name="AutochangeIsOn" xml:space="preserve">
    <value>Snippets aktiviert</value>
  </data>
  <data name="AutochangeKeyboardShortcuts" xml:space="preserve">
    <value>Liste der einzufügenden Snippets öffnen</value>
  </data>
  <data name="AutochangeKeyboardShortcutsAddNew" xml:space="preserve">
    <value>Fügen Sie hervorgehobenen Text zu Snippets hinzu</value>
  </data>
  <data name="AutochangeOnTab" xml:space="preserve">
    <value>Tab-Taste</value>
  </data>
  <data name="AutochangeOnInter" xml:space="preserve">
    <value>Eingabetaste</value>
  </data>
  <data name="AutochangeOnTabOrInter" xml:space="preserve">
    <value>Tab oder Enter</value>
  </data>
  <data name="AutochangeOnSpace" xml:space="preserve">
    <value>Raum</value>
  </data>
  <data name="AutochangeOnSpaceOrInter" xml:space="preserve">
    <value>Leertaste oder Eingabetaste</value>
  </data>
  <data name="HotKeyUseHotkey" xml:space="preserve">
    <value>Hotkeys</value>
  </data>
  <data name="HotKeyUseDoubleKeyDown" xml:space="preserve">
    <value>Doppelter Tastendruck</value>
  </data>
  <data name="HotKeyUseMouseXKey" xml:space="preserve">
    <value>Klicken einer Maustaste</value>
  </data>
  <data name="HotKeyIsON" xml:space="preserve">
    <value>Hotkeys aktiviert</value>
  </data>
  <data name="HotKeyWithoutShortcutNull" xml:space="preserve">
    <value>Abwesend</value>
  </data>
  <data name="HotKeyWithoutPressShortcut" xml:space="preserve">
    <value>Drücken Sie Hotkeys</value>
  </data>
  <data name="HotKeyShortcut" xml:space="preserve">
    <value>Kombination</value>
  </data>
  <data name="HotKeyDoubleKeyDown" xml:space="preserve">
    <value>Doppeltippen</value>
  </data>
  <data name="HotKeyMouse" xml:space="preserve">
    <value>Klicken einer Maustaste</value>
  </data>
  <data name="HotKeyDoubleKeyDownSelectKey" xml:space="preserve">
    <value>Wählen Sie eine Taste aus, die Sie zweimal drücken möchten</value>
  </data>
  <data name="HotKeyMouseSelectKey" xml:space="preserve">
    <value>Wählen Sie eine zusätzliche Maustaste</value>
  </data>
  <data name="HotKeyIsOff" xml:space="preserve">
    <value>Hotkeys deaktiviert</value>
  </data>
  <data name="HotKeyDoubleLeftOrRightCtrl" xml:space="preserve">
    <value>Linke oder rechte Strg-Taste</value>
  </data>
  <data name="HotKeyDoubleLeftCtrl" xml:space="preserve">
    <value>Linke Strg-Taste</value>
  </data>
  <data name="HotKeyDoubleRightCtrl" xml:space="preserve">
    <value>Rechte Strg-Taste</value>
  </data>
  <data name="HotKeyDoubleLeftOrRightShift" xml:space="preserve">
    <value>Links- oder Rechtsverschiebung</value>
  </data>
  <data name="HotKeyDoubleLeftShift" xml:space="preserve">
    <value>Linksverschiebung</value>
  </data>
  <data name="HotKeyDoubleRightShift" xml:space="preserve">
    <value>Rechtsverschiebung</value>
  </data>
  <data name="HotKeyDoubleLeftAlt" xml:space="preserve">
    <value>Links Alt</value>
  </data>
  <data name="HotKeyDoubleRightAlt" xml:space="preserve">
    <value>Rechts Alt</value>
  </data>
  <data name="SoundOnOff" xml:space="preserve">
    <value>Ton an</value>
  </data>
  <data name="SoundFormVolume" xml:space="preserve">
    <value>Volumen</value>
  </data>
  <data name="SoundFormSelectTrack" xml:space="preserve">
    <value>Wählen Sie einen Ton aus</value>
  </data>
  <data name="StartPageHeader" xml:space="preserve">
    <value>Lesen Sie vor der Verwendung des Programms die Dokumentation</value>
  </data>
  <data name="StartPageHelp" xml:space="preserve">
    <value>Entdecken Sie die Funktionen des Programms</value>
  </data>
  <data name="StartPageVideo" xml:space="preserve">
    <value>Sehen Sie sich die Videopräsentation an</value>
  </data>
  <data name="StartPageSite" xml:space="preserve">
    <value>Programm-Website</value>
  </data>
  <data name="StartPageLicense" xml:space="preserve">
    <value>Kaufen Sie eine Lizenz</value>
  </data>
  <data name="OcrHeader" xml:space="preserve">
    <value>Texterkennung</value>
  </data>
  <data name="OcrKeyboardShortcuts" xml:space="preserve">
    <value>Tastenkombination zum Starten von OCR</value>
  </data>
  <data name="OcrDescDefault" xml:space="preserve">
    <value>Wählen Sie Standardsprachen aus</value>
  </data>
  <data name="OcrDescLang" xml:space="preserve">
    <value>Wählen Sie Sprachen aus</value>
  </data>
  <data name="OcrDescNotSup" xml:space="preserve">
    <value>Die Verwendung sowohl europäischer als auch asiatischer Sprachen wird nicht unterstützt</value>
  </data>
  <data name="OcrEuropeanLang" xml:space="preserve">
    <value>Europäische Sprachen:</value>
  </data>
  <data name="OcrAsianLang" xml:space="preserve">
    <value>Asiatische Sprachen:</value>
  </data>
  <data name="OcrCaptureArea" xml:space="preserve">
    <value>Bildschirmbereich auswählen</value>
  </data>
  <data name="OcrOpenImageOrPDFFile" xml:space="preserve">
    <value>Bild öffnen</value>
  </data>
  <data name="OcrRecognize" xml:space="preserve">
    <value>Erkennen</value>
  </data>
  <data name="OcrRecognizeBarcode" xml:space="preserve">
    <value>Barcode erkennen</value>
  </data>
  <data name="OcrSelectLanguages" xml:space="preserve">
    <value>Wählen Sie Sprachen aus</value>
  </data>
  <data name="OcrStartText" xml:space="preserve">
    <value>Wählen Sie einen Bereich auf dem Bildschirm aus oder laden Sie eine Datei zur Erkennung hoch</value>
  </data>
  <data name="OcrTab" xml:space="preserve">
    <value>Texterkennung</value>
  </data>
  <data name="OcrInit" xml:space="preserve">
    <value>Bitte warten, das Modul wird geladen</value>
  </data>
  <data name="OcrLoadLibs" xml:space="preserve">
    <value>Klicken Sie hier, um das Modul herunterzuladen</value>
  </data>
  <data name="OcrWaitResult" xml:space="preserve">
    <value>Text kopiert</value>
  </data>
  <data name="OcrWaitResultFail" xml:space="preserve">
    <value>Text nicht erkannt</value>
  </data>
  <data name="OcrCopyImage" xml:space="preserve">
    <value>Bild kopieren</value>
  </data>
  <data name="OcrCopyText" xml:space="preserve">
    <value>Text kopieren</value>
  </data>
  <data name="ReplaceTo" xml:space="preserve">
    <value>Ersetzen durch:</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Suchen:</value>
  </data>
  <data name="TotalMatchFound" xml:space="preserve">
    <value>Insgesamt gefundene Übereinstimmungen:</value>
  </data>
  <data name="NoMatchFound" xml:space="preserve">
    <value>Keine Übereinstimmungen gefunden!</value>
  </data>
  <data name="TotalMatchFoundReplace" xml:space="preserve">
    <value>Insgesamt vorgenommene Ersetzungen:</value>
  </data>
  <data name="DiaryHeaderDate" xml:space="preserve">
    <value>DATUM</value>
  </data>
  <data name="DiaryHeaderFormat" xml:space="preserve">
    <value>TYP</value>
  </data>
  <data name="ClipboardOff" xml:space="preserve">
    <value>Der Zwischenablage-Manager ist deaktiviert</value>
  </data>
  <data name="GridViewClearFilter" xml:space="preserve">
    <value>Filter löschen</value>
  </data>
  <data name="GridViewFilter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="GridViewColumnsSelectionButtonTooltip" xml:space="preserve">
    <value>Wählen Sie Spalten aus</value>
  </data>
  <data name="GridViewSearchPanelTopText" xml:space="preserve">
    <value>Volltextsuche</value>
  </data>
  <data name="GridViewGroupPanelTopTextGrouped" xml:space="preserve">
    <value>Gruppiert nach:</value>
  </data>
  <data name="GridViewGroupPanelTopText" xml:space="preserve">
    <value>Gruppenkopf</value>
  </data>
  <data name="GridViewGroupPanelText" xml:space="preserve">
    <value>Ziehen Sie eine Spaltenüberschrift per Drag-and-Drop hierher, um sie nach dieser Spalte zu gruppieren</value>
  </data>
  <data name="GridViewFilterIsNotEmpty" xml:space="preserve">
    <value>nicht leer</value>
  </data>
  <data name="GridViewFilterIsEmpty" xml:space="preserve">
    <value>Leer</value>
  </data>
  <data name="GridViewFilterIsNotNull" xml:space="preserve">
    <value>Ist nicht null</value>
  </data>
  <data name="GridViewFilterIsNull" xml:space="preserve">
    <value>Null</value>
  </data>
  <data name="GridViewFilterStartsWith" xml:space="preserve">
    <value>Beginnt mit</value>
  </data>
  <data name="GridViewFilterShowRowsWithValueThat" xml:space="preserve">
    <value>Zeilen mit dem Wert anzeigen</value>
  </data>
  <data name="GridViewFilterSelectAll" xml:space="preserve">
    <value>Alles auswählen</value>
  </data>
  <data name="GridViewFilterOr" xml:space="preserve">
    <value>Oder</value>
  </data>
  <data name="GridViewFilterMatchCase" xml:space="preserve">
    <value>Groß- und Kleinschreibung beachten</value>
  </data>
  <data name="GridViewFilterIsNotEqualTo" xml:space="preserve">
    <value>ungleich</value>
  </data>
  <data name="GridViewFilterIsLessThanOrEqualTo" xml:space="preserve">
    <value>Kleiner oder gleich</value>
  </data>
  <data name="GridViewFilterIsLessThan" xml:space="preserve">
    <value>Weniger als</value>
  </data>
  <data name="GridViewFilterIsNotContainedIn" xml:space="preserve">
    <value>Nicht enthalten</value>
  </data>
  <data name="GridViewFilterIsGreaterThanOrEqualTo" xml:space="preserve">
    <value>Größer oder gleich</value>
  </data>
  <data name="GridViewFilterIsGreaterThan" xml:space="preserve">
    <value>Mehr als</value>
  </data>
  <data name="GridViewFilterIsEqualTo" xml:space="preserve">
    <value>Gleich</value>
  </data>
  <data name="GridViewFilterIsContainedIn" xml:space="preserve">
    <value>Enthalten in</value>
  </data>
  <data name="GridViewFilterEndsWith" xml:space="preserve">
    <value>Endet mit</value>
  </data>
  <data name="GridViewFilterDoesNotContain" xml:space="preserve">
    <value>Enthält nicht</value>
  </data>
  <data name="GridViewFilterContains" xml:space="preserve">
    <value>Enthält</value>
  </data>
  <data name="GridViewFilterAnd" xml:space="preserve">
    <value>UND</value>
  </data>
  <data name="DiaryOn" xml:space="preserve">
    <value>Tagebuch inklusive</value>
  </data>
  <data name="DiaryOff" xml:space="preserve">
    <value>Tagebuch ist ausgeschaltet</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>Im Lieferumfang enthalten</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>Deaktiviert</value>
  </data>
  <data name="SwitcherSettingsIsOff" xml:space="preserve">
    <value>Layoutumschaltung deaktiviert</value>
  </data>
  <data name="AutoSwitchSettingsIsOn" xml:space="preserve">
    <value>Automatische Layoutumschaltung aktiviert</value>
  </data>
  <data name="AutoSwitchSettingsIsOff" xml:space="preserve">
    <value>Die automatische Layoutumschaltung ist deaktiviert</value>
  </data>
  <data name="GridViewAlwaysVisibleNewRow" xml:space="preserve">
    <value>Klicken Sie hier, um ein neues Element hinzuzufügen</value>
  </data>
  <data name="TransSettingsClearAllHistory" xml:space="preserve">
    <value>Klarer Übersetzungsverlauf</value>
  </data>
  <data name="TransSettingsHistoryIsOn" xml:space="preserve">
    <value>Übersetzungsverlauf speichern</value>
  </data>
  <data name="CopyTranslatedText" xml:space="preserve">
    <value>Übersetzten Text kopieren</value>
  </data>
  <data name="ClearAll" xml:space="preserve">
    <value>Alles löschen</value>
  </data>
  <data name="SiteSourceButton" xml:space="preserve">
    <value>Im Browser öffnen</value>
  </data>
  <data name="CloseQuestion" xml:space="preserve">
    <value>Programm schließen?</value>
  </data>
  <data name="TransSettingsFavoriteLanguages" xml:space="preserve">
    <value>Ausgewählte Sprachen</value>
  </data>
  <data name="TransSettingsChooseYourFavoriteLanguages" xml:space="preserve">
    <value>Wählen Sie Ihre Lieblingssprachen aus</value>
  </data>
  <data name="GeneralSettingsFont" xml:space="preserve">
    <value>Schriftart</value>
  </data>
  <data name="ProgramsExceptionsCurrentInfo" xml:space="preserve">
    <value>Funktionen</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Laden</value>
  </data>
  <data name="ImageEditor_CanvasResize" xml:space="preserve">
    <value>Ändern der Leinwandgröße</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Schließen</value>
  </data>
  <data name="ImageEditor_Adjust" xml:space="preserve">
    <value>Korrektur</value>
  </data>
  <data name="ImageEditor_Amount" xml:space="preserve">
    <value>Summe</value>
  </data>
  <data name="ImageEditor_Auto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="ImageEditor_Background" xml:space="preserve">
    <value>Hintergrund:</value>
  </data>
  <data name="ImageEditor_BorderColor" xml:space="preserve">
    <value>Randfarbe:</value>
  </data>
  <data name="ImageEditor_BorderThickness" xml:space="preserve">
    <value>Randstärke:</value>
  </data>
  <data name="ImageEditor_CanvasSize" xml:space="preserve">
    <value>Leinwandgröße</value>
  </data>
  <data name="ImageEditor_ColorPicker_NoColorText_White" xml:space="preserve">
    <value>Weiß</value>
  </data>
  <data name="ImageEditor_Crop" xml:space="preserve">
    <value>Trimmen</value>
  </data>
  <data name="ImageEditor_DrawText" xml:space="preserve">
    <value>Bildtext</value>
  </data>
  <data name="ImageEditor_DrawText_YourTextHere" xml:space="preserve">
    <value>Dein Text</value>
  </data>
  <data name="ImageEditor_DrawTool" xml:space="preserve">
    <value>Ziehen</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushColor" xml:space="preserve">
    <value>Pinselfarbe:</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushSize" xml:space="preserve">
    <value>Pinselgröße:</value>
  </data>
  <data name="ImageEditor_Effect_Blur" xml:space="preserve">
    <value>Verwischen</value>
  </data>
  <data name="ImageEditor_Effect_Brightness" xml:space="preserve">
    <value>Helligkeit</value>
  </data>
  <data name="ImageEditor_Effect_ContrastAdjust" xml:space="preserve">
    <value>Kontrast</value>
  </data>
  <data name="ImageEditor_Effect_HueShift" xml:space="preserve">
    <value>Farbton ändern</value>
  </data>
  <data name="ImageEditor_Effect_InvertColors" xml:space="preserve">
    <value>Farben umkehren</value>
  </data>
  <data name="ImageEditor_Effect_Saturation" xml:space="preserve">
    <value>Sättigung</value>
  </data>
  <data name="ImageEditor_Effect_Sharpen" xml:space="preserve">
    <value>Schärfen</value>
  </data>
  <data name="ImageEditor_Effects" xml:space="preserve">
    <value>Änderung</value>
  </data>
  <data name="ImageEditor_FlipHorizontal" xml:space="preserve">
    <value>Horizontal spiegeln</value>
  </data>
  <data name="ImageEditor_FlipVertical" xml:space="preserve">
    <value>Vertikal spiegeln</value>
  </data>
  <data name="ImageEditor_FontSize" xml:space="preserve">
    <value>Schriftgröße</value>
  </data>
  <data name="ImageEditor_Height" xml:space="preserve">
    <value>Höhe:</value>
  </data>
  <data name="ImageEditor_HorizontalPosition" xml:space="preserve">
    <value>Horizontale Position</value>
  </data>
  <data name="ImageEditor_ImageAlignment" xml:space="preserve">
    <value>Bildausrichtung</value>
  </data>
  <data name="ImageEditor_ImagePreview" xml:space="preserve">
    <value>Bildvorschau</value>
  </data>
  <data name="ImageEditor_ImageSize" xml:space="preserve">
    <value>Bildgröße</value>
  </data>
  <data name="ImageEditor_Open" xml:space="preserve">
    <value>Offen</value>
  </data>
  <data name="ImageEditor_Options" xml:space="preserve">
    <value>Optionen</value>
  </data>
  <data name="ImageEditor_PreserveAspectRatio" xml:space="preserve">
    <value>Behalten Sie das ursprüngliche Seitenverhältnis bei</value>
  </data>
  <data name="ImageEditor_Radius" xml:space="preserve">
    <value>Radius:</value>
  </data>
  <data name="ImageEditor_Redo" xml:space="preserve">
    <value>Zurückkehren</value>
  </data>
  <data name="ImageEditor_RelativeSize" xml:space="preserve">
    <value>Relative Größe</value>
  </data>
  <data name="ImageEditor_Resize" xml:space="preserve">
    <value>Größe ändern</value>
  </data>
  <data name="ImageEditor_Rotate180" xml:space="preserve">
    <value>Um 180° drehen</value>
  </data>
  <data name="ImageEditor_Rotate270" xml:space="preserve">
    <value>Um 270° drehen</value>
  </data>
  <data name="ImageEditor_Rotate90" xml:space="preserve">
    <value>Um 90° drehen</value>
  </data>
  <data name="ImageEditor_Rotation" xml:space="preserve">
    <value>Drehen</value>
  </data>
  <data name="ImageEditor_RoundCorners" xml:space="preserve">
    <value>Abgerundete Ecken</value>
  </data>
  <data name="ImageEditor_Save" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="ImageEditor_Shape" xml:space="preserve">
    <value>Figur</value>
  </data>
  <data name="ImageEditor_Shapes_Ellipse" xml:space="preserve">
    <value>Ellipse</value>
  </data>
  <data name="ImageEditor_Shapes_Line" xml:space="preserve">
    <value>Zeitplan</value>
  </data>
  <data name="ImageEditor_Shapes_Rectangle" xml:space="preserve">
    <value>Rechteck</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderColor" xml:space="preserve">
    <value>Randfarbe</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderThickness" xml:space="preserve">
    <value>Randstärke</value>
  </data>
  <data name="ImageEditor_ShapeTool_FillShape" xml:space="preserve">
    <value>Rohrfüllformular</value>
  </data>
  <data name="ImageEditor_ShapeTool_LockRatio" xml:space="preserve">
    <value>Proportionen sperren</value>
  </data>
  <data name="ImageEditor_ShapeTool_Shape" xml:space="preserve">
    <value>Figur</value>
  </data>
  <data name="ImageEditor_ShapeTool_ShapeFill" xml:space="preserve">
    <value>Eine Form füllen</value>
  </data>
  <data name="ImageEditor_Text" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="ImageEditor_TextColor" xml:space="preserve">
    <value>Textfarbe</value>
  </data>
  <data name="ImageEditor_TheFileCannotBeOpened" xml:space="preserve">
    <value>Die Datei kann nicht geöffnet werden.</value>
  </data>
  <data name="ImageEditor_TheFileIsLocked" xml:space="preserve">
    <value>Die Datei kann nicht geöffnet werden. Dies kann durch eine andere Anwendung blockiert werden.</value>
  </data>
  <data name="ImageEditor_Transform" xml:space="preserve">
    <value>Konvertieren</value>
  </data>
  <data name="ImageEditor_UnableToSaveFile" xml:space="preserve">
    <value>Datei konnte nicht gespeichert werden.</value>
  </data>
  <data name="ImageEditor_Undo" xml:space="preserve">
    <value>Stornieren</value>
  </data>
  <data name="ImageEditor_UnsupportedFileFormat" xml:space="preserve">
    <value>Dieses Dateiformat wird nicht unterstützt.</value>
  </data>
  <data name="ImageEditor_VerticalPosition" xml:space="preserve">
    <value>Vertikale Position</value>
  </data>
  <data name="ImageEditor_Width" xml:space="preserve">
    <value>Breite:</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Zurücksetzen</value>
  </data>
  <data name="ResetAll" xml:space="preserve">
    <value>Alles zurücksetzen</value>
  </data>
  <data name="OcrFromClipboard" xml:space="preserve">
    <value>Aus der Zwischenablage</value>
  </data>
  <data name="OcrEditImage" xml:space="preserve">
    <value>Bild bearbeiten</value>
  </data>
  <data name="PasteButtonWithoutClipboard" xml:space="preserve">
    <value>Emulierte Eingabe einfügen</value>
  </data>
  <data name="AutochangeEditor" xml:space="preserve">
    <value>Snippet-Editor</value>
  </data>
  <data name="AutochangeHelperFromTextTootlTip" xml:space="preserve">
    <value>Ersatztext:</value>
  </data>
  <data name="OcrModuleNotLoaded" xml:space="preserve">
    <value>Das Texterkennungsmodul ist nicht geladen</value>
  </data>
  <data name="AppearanceTab" xml:space="preserve">
    <value>Aussehen</value>
  </data>
  <data name="OrderFunctionsTab" xml:space="preserve">
    <value>Reihenfolge der Funktionen</value>
  </data>
  <data name="TranslateOnlyFavoriteLanguages" xml:space="preserve">
    <value>Nur ausgewählte Sprachen anzeigen</value>
  </data>
  <data name="TranslateSowAll" xml:space="preserve">
    <value>Alle anzeigen</value>
  </data>
  <data name="GeneralSettingsThemeProgramRestart" xml:space="preserve">
    <value>Das Programm neu starten, um das Thema zu ändern?</value>
  </data>
  <data name="CommonWindowPressKeyForPast" xml:space="preserve">
    <value>Um Text einzufügen, drücken Sie die Taste</value>
  </data>
  <data name="MiminoteTab" xml:space="preserve">
    <value>Notizen</value>
  </data>
  <data name="NoteTab" xml:space="preserve">
    <value>Notizen</value>
  </data>
  <data name="NotesShow" xml:space="preserve">
    <value>Notizen anzeigen</value>
  </data>
  <data name="NotesToArchive" xml:space="preserve">
    <value>Zum Archiv</value>
  </data>
  <data name="NotesAddNew" xml:space="preserve">
    <value>Fügen Sie eine Notiz hinzu</value>
  </data>
  <data name="NotesList" xml:space="preserve">
    <value>Liste der Notizen</value>
  </data>
  <data name="NoteColor" xml:space="preserve">
    <value>Beachten Sie die Farbe</value>
  </data>
  <data name="NoteConvertToNote" xml:space="preserve">
    <value>In Notiz konvertieren</value>
  </data>
  <data name="NoteConvertToTaskList" xml:space="preserve">
    <value>In Aufgabenliste konvertieren</value>
  </data>
  <data name="NotePasteAsPlainText" xml:space="preserve">
    <value>Als normalen Text einfügen</value>
  </data>
  <data name="NotePasteAsText" xml:space="preserve">
    <value>Als Text einfügen</value>
  </data>
  <data name="NoteArchiveList" xml:space="preserve">
    <value>Notizenarchiv</value>
  </data>
  <data name="NoteRestore" xml:space="preserve">
    <value>Wiederherstellen</value>
  </data>
  <data name="NoteFontFamilyAndSize" xml:space="preserve">
    <value>Schriftfamilie und -größe für Notizen</value>
  </data>
  <data name="NoteTransparencyForInactiveNotes" xml:space="preserve">
    <value>Transparenz inaktiver Notizen</value>
  </data>
  <data name="UniversalWindowSettingsUniversalWindowIsOff" xml:space="preserve">
    <value>SmartClick deaktiviert</value>
  </data>
  <data name="DiaryIsOff" xml:space="preserve">
    <value>Tagebuch deaktiviert</value>
  </data>
  <data name="AutochangeIsOff" xml:space="preserve">
    <value>Snippets deaktiviert</value>
  </data>
  <data name="GeneralSettingsCanClose" xml:space="preserve">
    <value>Schließen-Button anzeigen</value>
  </data>
  <data name="SwitcherPauseTimeForKeysSend" xml:space="preserve">
    <value>Timeout für Tastenanschlagsemulation</value>
  </data>
  <data name="OcrImage" xml:space="preserve">
    <value>Bild</value>
  </data>
  <data name="OcrRecognizedText" xml:space="preserve">
    <value>Erkannter Text</value>
  </data>
  <data name="UpdateError" xml:space="preserve">
    <value>Das Update wurde nicht abgeschlossen. Bitte aktualisieren Sie manuell.</value>
  </data>
  <data name="UpdateErrorTitle" xml:space="preserve">
    <value>EveryLang-Updatefehler</value>
  </data>
  <data name="NoteCheckMarket" xml:space="preserve">
    <value>Markiert</value>
  </data>
  <data name="NotesIsShowing" xml:space="preserve">
    <value>Im Lieferumfang enthalten</value>
  </data>
  <data name="SearchHelperText" xml:space="preserve">
    <value>Geben Sie den zu suchenden Text ein</value>
  </data>
  <data name="TransLangAuto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="NotesFromArchive" xml:space="preserve">
    <value>Aus dem Archiv</value>
  </data>
  <data name="NotesIsHiding" xml:space="preserve">
    <value>Deaktiviert</value>
  </data>
  <data name="NoteInArchive" xml:space="preserve">
    <value>Im Archiv</value>
  </data>
  <data name="KeyboardLayoutTab" xml:space="preserve">
    <value>Tastaturlayout</value>
  </data>
  <data name="CapsTab" xml:space="preserve">
    <value>Textfall</value>
  </data>
  <data name="IsIndicateNumLockState" xml:space="preserve">
    <value>NumLock-Status anzeigen</value>
  </data>
  <data name="StatusButtonNumLockIsOff" xml:space="preserve">
    <value>NumLock ist deaktiviert</value>
  </data>
  <data name="StatusButtonNumLockIsOn" xml:space="preserve">
    <value>NumLock aktiviert</value>
  </data>
  <data name="ConverterSettingsOpenWindow" xml:space="preserve">
    <value>Öffnen Sie ein Fenster mit Konverterfunktionen</value>
  </data>
  <data name="UniFirstLetterUp" xml:space="preserve">
    <value>Geben Sie den ersten Buchstaben in Großbuchstaben ein</value>
  </data>
  <data name="UniTextFirstLetterUp" xml:space="preserve">
    <value>Als Erstes</value>
  </data>
  <data name="UniFirstLetterDown" xml:space="preserve">
    <value>Kleiner Anfangsbuchstabe</value>
  </data>
  <data name="UniTextFirstLetterDown" xml:space="preserve">
    <value>Zuerst runter</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsCapsOpenWindow" xml:space="preserve">
    <value>Öffnen Sie ein Fenster mit Fallwechselfunktionen</value>
  </data>
  <data name="ConverterSettingsSnakeCase" xml:space="preserve">
    <value>Konvertieren Sie Text in den Snake_case-Stil</value>
  </data>
  <data name="ConverterSettingsKebabCase" xml:space="preserve">
    <value>Konvertieren von Text in den Kebab-Case-Stil</value>
  </data>
  <data name="ConverterSettingsPascalCase" xml:space="preserve">
    <value>Konvertieren von Text in den PascalCase-Stil</value>
  </data>
  <data name="UniCase" xml:space="preserve">
    <value>Textfall</value>
  </data>
  <data name="AboutSettingsUpdatePressForUpdate" xml:space="preserve">
    <value>Klicken Sie zum Herunterladen</value>
  </data>
</root>