﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="About" xml:space="preserve">
    <value>О программе</value>
  </data>
  <data name="AvailableNewVersion" xml:space="preserve">
    <value>Доступна новая версия, перезапустите приложение</value>
  </data>
  <data name="ClearButton" xml:space="preserve">
    <value>Очистить</value>
  </data>
  <data name="FirstStartWithEvaluate" xml:space="preserve">
    <value>Активирован пробный период на 40 дней, включены все функции</value>
  </data>
  <data name="CloseHeaderButton" xml:space="preserve">
    <value>Закрыть</value>
  </data>
  <data name="CopyButton" xml:space="preserve">
    <value>Копировать</value>
  </data>
  <data name="CopyButtonHtml" xml:space="preserve">
    <value>Копировать Html</value>
  </data>
  <data name="CopyButtonRtf" xml:space="preserve">
    <value>Копировать Rtf</value>
  </data>
  <data name="FastActionIndex" xml:space="preserve">
    <value>Нажмите на цифру для вставки текста</value>
  </data>
  <data name="BreakInterButton" xml:space="preserve">
    <value>Разбить текст по символу перевода строки</value>
  </data>
  <data name="BreakSpaceButton" xml:space="preserve">
    <value>Разбить текст по пробелу</value>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>Выход</value>
  </data>
  <data name="FixWindow" xml:space="preserve">
    <value>Закрепить окно</value>
  </data>
  <data name="FromLang" xml:space="preserve">
    <value>С языка:</value>
  </data>
  <data name="ShowHistoryButton" xml:space="preserve">
    <value>Показать историю</value>
  </data>
  <data name="HideHeaderButton" xml:space="preserve">
    <value>Свернуть</value>
  </data>
  <data name="MaxHeaderButton" xml:space="preserve">
    <value>Развернуть</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Да</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Нет</value>
  </data>
  <data name="PastButton" xml:space="preserve">
    <value>Вставить</value>
  </data>
  <data name="GoToMainWindowButton" xml:space="preserve">
    <value>Открыть главное окно с переводом</value>
  </data>
  <data name="SiteSourceTextButton" xml:space="preserve">
    <value>Открыть сайт сервиса перевода</value>
  </data>
  <data name="OpenMainWindowShortcut" xml:space="preserve">
    <value>Комбинация клавиш для открытия главного окна</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Настройки</value>
  </data>
  <data name="ListOnMainWindow" xml:space="preserve">
    <value>Открыть список в главном окне</value>
  </data>
  <data name="StopWorkingShortcut" xml:space="preserve">
    <value>Комбинация клавиш для отключения всех функций</value>
  </data>
  <data name="WorkingOff" xml:space="preserve">
    <value>Отключить программу</value>
  </data>
  <data name="WorkingOn" xml:space="preserve">
    <value>Включить программу</value>
  </data>
  <data name="WorkingOffTitle" xml:space="preserve">
    <value>Программа отключена</value>
  </data>
  <data name="StayOnTopButton" xml:space="preserve">
    <value>Не закрывать</value>
  </data>
  <data name="LatinButton" xml:space="preserve">
    <value>На латинице</value>
  </data>
  <data name="ToLang" xml:space="preserve">
    <value>На язык:</value>
  </data>
  <data name="TranslateButton" xml:space="preserve">
    <value>Перевести</value>
  </data>
  <data name="TranslateError" xml:space="preserve">
    <value>В процессе перевода возникла ошибка, попробуйте еще раз или выберите другой сервис для перевода</value>
  </data>
  <data name="SoundButton" xml:space="preserve">
    <value>Прослушать</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Обновление</value>
  </data>
  <data name="SettingHeaderButton" xml:space="preserve">
    <value>НАСТРОЙКИ</value>
  </data>
  <data name="HistoryHeaderButton" xml:space="preserve">
    <value>ИСТОРИЯ</value>
  </data>
  <data name="ClipboardHeaderButton" xml:space="preserve">
    <value>БУФЕР ОБМЕНА</value>
  </data>
  <data name="DiaryHeaderButton" xml:space="preserve">
    <value>ДНЕВНИК</value>
  </data>
  <data name="SnippetsHeaderButton" xml:space="preserve">
    <value>ШАБЛОНЫ</value>
  </data>
  <data name="ProHeaderButton" xml:space="preserve">
    <value>Активировать PRO</value>
  </data>
  <data name="HistoryMenuItem" xml:space="preserve">
    <value>История</value>
  </data>
  <data name="ClipboardMenuItem" xml:space="preserve">
    <value>Буфер обмена</value>
  </data>
  <data name="HotkeysMenuItem" xml:space="preserve">
    <value>Горячие клавиши</value>
  </data>
  <data name="DiaryMenuItem" xml:space="preserve">
    <value>Дневник</value>
  </data>
  <data name="ErrorHotkey" xml:space="preserve">
    <value>Невозможно зарегистрировать комбинацию клавиш</value>
  </data>
  <data name="UpdateAvailable" xml:space="preserve">
    <value>Доступно обновление, перезапустите приложение</value>
  </data>
  <data name="MinimizedText" xml:space="preserve">
    <value>Приложение запущено и свернуто</value>
  </data>
  <data name="AppUpdated" xml:space="preserve">
    <value>Приложение обновлено, список изменений на сайте</value>
  </data>
  <data name="AppNotAsAdmin" xml:space="preserve">
    <value>Для корректной работы программы со всеми приложениями, необходимо запустить ее от имени администратора</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Отмена</value>
  </data>
  <data name="ErrorText" xml:space="preserve">
    <value>Произошла ошибка, приложение будет завершено. Пожалуйста отправьте текст с ошибками на адрес <EMAIL></value>
  </data>
  <data name="InputLanguagesError" xml:space="preserve">
    <value>На Вашем компьютере для некоторых языков установлено более одной раскладки клавиатуры, что может негативно повлиять на правильность работы функции переключения раскладки</value>
  </data>
  <data name="SetFont" xml:space="preserve">
    <value>Шрифт</value>
  </data>
  <data name="SearchText" xml:space="preserve">
    <value>Найти...</value>
  </data>
  <data name="OnlyPro" xml:space="preserve">
    <value>(PRO версия)</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Изменить</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Сохранить</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Удалить</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>Создать</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Все</value>
  </data>
  <data name="AllApp" xml:space="preserve">
    <value>Все программы</value>
  </data>
  <data name="OnlyInProVersion" xml:space="preserve">
    <value>Только в PRO версии</value>
  </data>
  <data name="SystemTrayHide" xml:space="preserve">
    <value>Программа свернута в трей</value>
  </data>
  <data name="StartWindowTitle" xml:space="preserve">
    <value>Перед началом использования программы ознакомьтесь с документацией</value>
  </data>
  <data name="FastActionTextWindowSearch" xml:space="preserve">
    <value>Tab - Поиск.  Esc - Отмена и очистка</value>
  </data>
  <data name="noun" xml:space="preserve">
    <value>Существительное</value>
  </data>
  <data name="pronoun" xml:space="preserve">
    <value>Местоимение</value>
  </data>
  <data name="adjective" xml:space="preserve">
    <value>Прилагательное</value>
  </data>
  <data name="verb" xml:space="preserve">
    <value>Глагол</value>
  </data>
  <data name="adverb" xml:space="preserve">
    <value>Наречие</value>
  </data>
  <data name="preposition" xml:space="preserve">
    <value>Предлог</value>
  </data>
  <data name="conjunction" xml:space="preserve">
    <value>Союз</value>
  </data>
  <data name="interjection" xml:space="preserve">
    <value>Междометие</value>
  </data>
  <data name="participle" xml:space="preserve">
    <value>Причастие</value>
  </data>
  <data name="auxiliary verb" xml:space="preserve">
    <value>Вспомогательный глагол</value>
  </data>
  <data name="parenthetic" xml:space="preserve">
    <value>Вводное слово</value>
  </data>
  <data name="SpellCheckHeader" xml:space="preserve">
    <value>Проверка правописания</value>
  </data>
  <data name="LabelNoErrors" xml:space="preserve">
    <value>Ошибок нет</value>
  </data>
  <data name="LangNotCorrect" xml:space="preserve">
    <value>Язык не поддерживается</value>
  </data>
  <data name="LabelError" xml:space="preserve">
    <value>Произошла ошибка</value>
  </data>
  <data name="LabelTextTooLong" xml:space="preserve">
    <value>Текст слишком длинный</value>
  </data>
  <data name="ButtonClose" xml:space="preserve">
    <value>Закрыть</value>
  </data>
  <data name="LabelOptions" xml:space="preserve">
    <value>Варианты:</value>
  </data>
  <data name="LabelOptionsNo" xml:space="preserve">
    <value>Варианты отсутствуют</value>
  </data>
  <data name="LabelOptionsEnd" xml:space="preserve">
    <value>Проверка завершена</value>
  </data>
  <data name="bSkip" xml:space="preserve">
    <value>Пропустить</value>
  </data>
  <data name="bSkipAll" xml:space="preserve">
    <value>Пропустить все</value>
  </data>
  <data name="bReplace" xml:space="preserve">
    <value>Заменить</value>
  </data>
  <data name="bReplaceAll" xml:space="preserve">
    <value>Заменить все</value>
  </data>
  <data name="bReplaceText" xml:space="preserve">
    <value>Вставить текст</value>
  </data>
  <data name="bCopy" xml:space="preserve">
    <value>Копировать</value>
  </data>
  <data name="buttonBack" xml:space="preserve">
    <value>Вернуть</value>
  </data>
  <data name="SetDefaultSetting" xml:space="preserve">
    <value>Сбросить настройки</value>
  </data>
  <data name="InterKeyboardShortcuts" xml:space="preserve">
    <value>Нажмите комбинацию клавиш</value>
  </data>
  <data name="Russian" xml:space="preserve">
    <value>Русский</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>Английский</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>Французский</value>
  </data>
  <data name="Italian" xml:space="preserve">
    <value>Итальянский</value>
  </data>
  <data name="Ukrainian" xml:space="preserve">
    <value>Украинский</value>
  </data>
  <data name="AboutSettingsHeader" xml:space="preserve">
    <value>О ПРОГРАММЕ</value>
  </data>
  <data name="AboutSettingsLicense" xml:space="preserve">
    <value>Лицензионное соглашение</value>
  </data>
  <data name="AboutSettingsDesc" xml:space="preserve">
    <value>Универсальный помощник для работы с текстом на разных языках</value>
  </data>
  <data name="AboutSettingsVersion" xml:space="preserve">
    <value>Версия:</value>
  </data>
  <data name="AboutSettingsUpdateAvailable" xml:space="preserve">
    <value>Доступна новая версия программы</value>
  </data>
  <data name="AboutSettingsUpdate" xml:space="preserve">
    <value>Обновить</value>
  </data>
  <data name="AboutSettingsContactHeader" xml:space="preserve">
    <value>ОБРАТНАЯ СВЯЗЬ</value>
  </data>
  <data name="AboutSettingsContactText" xml:space="preserve">
    <value>Напишите ваши комментарии и/или вопросы</value>
  </data>
  <data name="AboutResetSettings" xml:space="preserve">
    <value>Сброс всех настроек программы</value>
  </data>
  <data name="AboutOpenStartWindow" xml:space="preserve">
    <value>Открыть окно приветствия</value>
  </data>
  <data name="AboutResetSettingsQuestion" xml:space="preserve">
    <value>Сбросить все настройки и данные программы?</value>
  </data>
  <data name="AboutSettingsContactLink" xml:space="preserve">
    <value>Контактная форма</value>
  </data>
  <data name="AboutSettingsIsAdmin" xml:space="preserve">
    <value>Программа запущена от имени администратора</value>
  </data>
  <data name="AboutSettingsIsNotAdmin" xml:space="preserve">
    <value>Программа запущена не от имени администратора</value>
  </data>
  <data name="AboutSettingsAckowledgementsHeader" xml:space="preserve">
    <value>КОМПОНЕНТЫ</value>
  </data>
  <data name="SwitcherSettingsHeader" xml:space="preserve">
    <value>Изменение раскладки</value>
  </data>
  <data name="SwitcherSettingsKeyboardShortcutsSwitch" xml:space="preserve">
    <value>Переключение раскладки последнего слова</value>
  </data>
  <data name="SwitcherSettingsIsUseBreak" xml:space="preserve">
    <value>Использовать Break</value>
  </data>
  <data name="SwitcherSettingsKeyboardShortcutsSwitchSelected" xml:space="preserve">
    <value>Изменение раскладки выделенного текста</value>
  </data>
  <data name="SwitcherSettingsMethodSelect" xml:space="preserve">
    <value>Выбор метода переключения раскладки</value>
  </data>
  <data name="SwitcherSettingsSwitchMethod1" xml:space="preserve">
    <value>Эмуляция клавиш переключения раскладки</value>
  </data>
  <data name="SwitcherSettingsSwitchMethod2" xml:space="preserve">
    <value>Выполнение команды Windows</value>
  </data>
  <data name="SwitcherSettingsLeaveTextSelectedAfterSwitch" xml:space="preserve">
    <value>Оставить текст выделенным после переключения раскладки</value>
  </data>
  <data name="SwitcherSettingsIsOn" xml:space="preserve">
    <value>Переключение раскладки включено</value>
  </data>
  <data name="SwitcherSettingsSwitcherCtrlNumberIsOn" xml:space="preserve">
    <value>Использовать Ctrl+(число) для переключения на конкретный язык</value>
  </data>
  <data name="SwitcherSettingsIsUseShift" xml:space="preserve">
    <value>Использовать двойное нажатие кнопки Shift</value>
  </data>
  <data name="SwitcherSettingsIsOnInsert" xml:space="preserve">
    <value>Переключение с начала строки</value>
  </data>
  <data name="SwitcherSettingsIsUseScrollLock" xml:space="preserve">
    <value>Использовать двойное нажатие кнопки ScrollLock</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOn" xml:space="preserve">
    <value>Переключать раскладку по кнопке</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnStandart" xml:space="preserve">
    <value>Системные настройки</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRCtrl" xml:space="preserve">
    <value>Правому Ctrl</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLCtrl" xml:space="preserve">
    <value>Левому Ctrl</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRShift" xml:space="preserve">
    <value>Правому Shift</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLShift" xml:space="preserve">
    <value>Левому Shift</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLRCtrl" xml:space="preserve">
    <value>По правому или левому Ctrl</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnLRShift" xml:space="preserve">
    <value>По правому или левому Shift</value>
  </data>
  <data name="SwitcherSettingsKeyboardSwitchOnRCtrlOrCapsLock" xml:space="preserve">
    <value>Правому Ctrl или CapsLock</value>
  </data>
  <data name="SwitcherSettingsToolTipForCurrentSwitchOnKey" xml:space="preserve">
    <value>Для включения или отключеня функции CapsLock используйте одновременное нажатие на правый и левый Shift</value>
  </data>
  <data name="SwitcherSettingsSwitcherSountIsOn" xml:space="preserve">
    <value>Звук переключения раскладки</value>
  </data>
  <data name="SwitcherSettingsSoundEdit" xml:space="preserve">
    <value>Настройка звука переключения раскладки</value>
  </data>
  <data name="SwitcherSettingsTrueListOfLang" xml:space="preserve">
    <value>Список языков на которые будет переключаться раскладка</value>
  </data>
  <data name="SwitcherLangAndKeysForSwitch" xml:space="preserve">
    <value>Настройка клавиш для переключения на конкретный язык</value>
  </data>
  <data name="SwitcherSettingsAskToDeactivateAutoswitcherOff" xml:space="preserve">
    <value>Отключить автопереключение раскладки?</value>
  </data>
  <data name="SwitcherSettingsAskToDeactivateAutoswitcherOn" xml:space="preserve">
    <value>Включить автопереключение раскладки?</value>
  </data>
  <data name="AutoSwitcherSettingsHeader" xml:space="preserve">
    <value>Автопереключение раскладки</value>
  </data>
  <data name="SwitcherSettingsHeaderAuto" xml:space="preserve">
    <value>Автопереключение включено</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnTwoUpperCaseLetters" xml:space="preserve">
    <value>Исправлять две заглавные буквы в начале слова</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnFixWrongUpperCase" xml:space="preserve">
    <value>Исправлять случайное нажатие CapsLock</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnUpperCaseNotSwitch" xml:space="preserve">
    <value>Не переключать, если все буквы в слове заглавные</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnByEnter" xml:space="preserve">
    <value>Исправлять раскладку после нажатия на клавишу Enter</value>
  </data>
  <data name="AutoSwitcherSettingsIsSwitchOneLetter" xml:space="preserve">
    <value>Добавлять в правила однобуквенные слова</value>
  </data>
  <data name="AutoSwitcherSettingsIsOnAddingRule" xml:space="preserve">
    <value>Автоматически добавлять правила переключения</value>
  </data>
  <data name="AutoSwitcherSettingsDisableAutoSwitchAfterManualSwitch" xml:space="preserve">
    <value>Не исправлять раскладку, если до этого она была изменена вручную</value>
  </data>
  <data name="AutoSwitcherSettingsOnlyAfterSeparator" xml:space="preserve">
    <value>Переключать раскладку только после ввода слова целиком</value>
  </data>
  <data name="AutoSwitcherSettingsAfterPause" xml:space="preserve">
    <value>Переключать раскладку после остановки ввода слова</value>
  </data>
  <data name="AutoSwitcherSettingsResetRule" xml:space="preserve">
    <value>Удалить все правила автопереключения</value>
  </data>
  <data name="AutoSwitcherSettingsCombination" xml:space="preserve">
    <value>Текст</value>
  </data>
  <data name="AutoSwitcherSettingsAllLayouts" xml:space="preserve">
    <value>Все раскладки</value>
  </data>
  <data name="AutoSwitcherSettingsAction" xml:space="preserve">
    <value>Действие</value>
  </data>
  <data name="AutoSwitcherSettingsShowAcceptWindow" xml:space="preserve">
    <value>Добавлять правила только после подтверждении</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionConvert" xml:space="preserve">
    <value>Переключать</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionNotConvert" xml:space="preserve">
    <value>Не переключать</value>
  </data>
  <data name="AutoSwitcherSettingsRuleActionIntermediate" xml:space="preserve">
    <value>Кандидат</value>
  </data>
  <data name="AutoSwitcherSettingsHelpWindowTitle" xml:space="preserve">
    <value>Правила автопереключения</value>
  </data>
  <data name="AutoSwitcherSettingsTrueListOfLang" xml:space="preserve">
    <value>Список языков для которых будет работать автопереключение</value>
  </data>
  <data name="AutoSwitcherSettingsListRulesHeader" xml:space="preserve">
    <value>Список правил для автопереключения</value>
  </data>
  <data name="AutoSwitcherSettingsListRulesHeaderShowAll" xml:space="preserve">
    <value>Показывать кандидатов</value>
  </data>
  <data name="AutoSwitcherSettingsOpenRulesList" xml:space="preserve">
    <value>Открыть список правил автопереключения</value>
  </data>
  <data name="AutoSwitcherSettingsCountCheckRule" xml:space="preserve">
    <value>Количество ручных переключений раскладки слова для включения в правила</value>
  </data>
  <data name="AutoSwitchAcceptText" xml:space="preserve">
    <value>Добавить слово в правила автопереключения? Enter - ДА</value>
  </data>
  <data name="IsLangInfoWindowShowForMouse" xml:space="preserve">
    <value>Текущий язык ввода на указателе мыши</value>
  </data>
  <data name="IsLangInfoWindowShowForCaret" xml:space="preserve">
    <value>Текущий язык ввода в текстовом курсоре</value>
  </data>
  <data name="IsLangInfoWindowShowLargeWindow" xml:space="preserve">
    <value>Отдельное окно индикатора языка</value>
  </data>
  <data name="IsLangInfoInTray" xml:space="preserve">
    <value>Текущий язык в системном трее</value>
  </data>
  <data name="IsLangInfoWindowShowForCaretEx" xml:space="preserve">
    <value>Расширенные возможности</value>
  </data>
  <data name="IsLangInfoShowIconsImage" xml:space="preserve">
    <value>Показывать флаг страны</value>
  </data>
  <data name="IsLangInfoShowIconsText" xml:space="preserve">
    <value>Показывать название языка</value>
  </data>
  <data name="OpacityIconLangInfo" xml:space="preserve">
    <value>Прозрачность индикатора</value>
  </data>
  <data name="SizeIconLangInfo" xml:space="preserve">
    <value>Увеличение размера индикатора в процентах</value>
  </data>
  <data name="IsIndicateCurrentLangInKeyboardLed" xml:space="preserve">
    <value>Индикация текущего языка на клавиатуре</value>
  </data>
  <data name="PosMouse" xml:space="preserve">
    <value>Позиция индикатора на указателе мыши</value>
  </data>
  <data name="IsHideIndicateInFullScreenApp" xml:space="preserve">
    <value>Скрывать индикатор в программах, запущенных на полный экран</value>
  </data>
  <data name="IsIndicateCapsLockState" xml:space="preserve">
    <value>Показывать состояние CapsLock</value>
  </data>
  <data name="StatusButtonCapsLockIsOn" xml:space="preserve">
    <value>CapsLock включен</value>
  </data>
  <data name="PosCarret" xml:space="preserve">
    <value>Позиция индикатора в текстовом курсоре</value>
  </data>
  <data name="SpellcheckingSettingsHeader" xml:space="preserve">
    <value>Проверка орфографии</value>
  </data>
  <data name="SpellcheckingSettingsIsOn" xml:space="preserve">
    <value>Проверка орфографии включена</value>
  </data>
  <data name="SpellcheckingSettingsWhileTyping" xml:space="preserve">
    <value>Проверка орфографии при наборе текста</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingSoundOn" xml:space="preserve">
    <value>Звук для проверки орфографии при наборе текста</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingUseNumber" xml:space="preserve">
    <value>Для быстрой замены использовать числа</value>
  </data>
  <data name="SpellcheckingSettingsWhileTypingSoundEdit" xml:space="preserve">
    <value>Настройки звука при проверке орфографии</value>
  </data>
  <data name="SpellcheckingKeyboardShortcuts" xml:space="preserve">
    <value>Комбинация клавиш для проверки орфографии выделенного текста</value>
  </data>
  <data name="SpellcheckingKeyboardShortcutsShort" xml:space="preserve">
    <value>Проверка орфографии выделенного текста</value>
  </data>
  <data name="SpellcheckingSettingsCloseByTimer" xml:space="preserve">
    <value>Закрывать окно, если нет ошибок, через 3 секунды</value>
  </data>
  <data name="ClipboardSettingsHeader" xml:space="preserve">
    <value>Менеджер буфера обмена</value>
  </data>
  <data name="ClipboardKeyboardShortcuts" xml:space="preserve">
    <value>Вставка текста без форматированияи и вставка пути скопированного файла</value>
  </data>
  <data name="ClipboardOn" xml:space="preserve">
    <value>Менеджер буфера обмена включен</value>
  </data>
  <data name="ClipboardKeyboardViewShortcuts" xml:space="preserve">
    <value>Открывать историю буфера обмена по сочетанию клавиш</value>
  </data>
  <data name="ClipboardKeyboardRoundShortcuts" xml:space="preserve">
    <value>Последовательная вставка текста из истории буфера обмена для текущего окна</value>
  </data>
  <data name="ClipboardKeyboardRoundShortcutsShort" xml:space="preserve">
    <value>Последовательная вставка текста из истории буфера обмена</value>
  </data>
  <data name="ClipboardSettingsPasteByIndexIsOn" xml:space="preserve">
    <value>Вставлять текст по Ctrl+Shift+(число) - число 1, 2, 3, 4, 5, 6, 7, 8, 9 - индекс записи в истории буфера обмена</value>
  </data>
  <data name="ClipboardSettingsSaveFilePath" xml:space="preserve">
    <value>Сохранять в истории буфера обмена путь к скопированному файлу</value>
  </data>
  <data name="ClipboardSettingsSaveImage" xml:space="preserve">
    <value>Сохранять в истории буфера изображения</value>
  </data>
  <data name="ClipboardSettingsReplaceWithoutChangeClipboard" xml:space="preserve">
    <value>При вставке текста из истории заменить текущее значение в буфере обмена</value>
  </data>
  <data name="ClipboardMaxClipboardItems" xml:space="preserve">
    <value>Размер истории буфера обмена</value>
  </data>
  <data name="ClipboardSound" xml:space="preserve">
    <value>Звук изменения буфера обмена</value>
  </data>
  <data name="ConverterSettingsHeader" xml:space="preserve">
    <value>Конвертер текста</value>
  </data>
  <data name="ConverterSettingsExpression" xml:space="preserve">
    <value>Конвертер чисел и дат в строки, вычисление выражений</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchCapsSettings" xml:space="preserve">
    <value>Настроить горячие клавиши</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsInvert" xml:space="preserve">
    <value>Инвертировать регистр выделенного текста</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsUp" xml:space="preserve">
    <value>Привести к верхнему регистру выделенный текст</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsSwitchSelectedCapsDown" xml:space="preserve">
    <value>Привести к нижнему регистру выделенный текст</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsFirstLetterToDown" xml:space="preserve">
    <value>К нижнему регистру первый символ слова под курсором</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsFirstLetterToUp" xml:space="preserve">
    <value>К верхнему регистру первый символ слова под курсором</value>
  </data>
  <data name="ConverterSettingsTransliteration" xml:space="preserve">
    <value>Транслитерация выделенного текста</value>
  </data>
  <data name="ConverterSettingsCamelCase" xml:space="preserve">
    <value>Преобразование текста в camelCase стиле</value>
  </data>
  <data name="ConverterReplaceSelText" xml:space="preserve">
    <value>Поиск и замена текста в выделенном тексте</value>
  </data>
  <data name="ConverterSettingsEncloseTextQuotationMarks" xml:space="preserve">
    <value>Обрамление выделенного текста символами (пример)</value>
  </data>
  <data name="ConverterAdd" xml:space="preserve">
    <value>Добавить</value>
  </data>
  <data name="ConverterStart" xml:space="preserve">
    <value>Слева</value>
  </data>
  <data name="ConverterEnd" xml:space="preserve">
    <value>Справа</value>
  </data>
  <data name="ConverterDelete" xml:space="preserve">
    <value>Удалить</value>
  </data>
  <data name="ConverterSampleText" xml:space="preserve">
    <value>текст для примера</value>
  </data>
  <data name="TransCloseHeaderButton" xml:space="preserve">
    <value>Закрыть ESC</value>
  </data>
  <data name="TransReplaceTextButton" xml:space="preserve">
    <value>Заменить текст ENTER</value>
  </data>
  <data name="TransSettingsHeader" xml:space="preserve">
    <value>Перевод</value>
  </data>
  <data name="TransSettingsNativeLanguage" xml:space="preserve">
    <value>Язык по умолчанию на который переводить</value>
  </data>
  <data name="TransSettingsLanguageFromTranslate" xml:space="preserve">
    <value>Язык по умолчанию с которого переводить</value>
  </data>
  <data name="TransSettingsMainLanguageForTheTranslation" xml:space="preserve">
    <value>Основной язык для перевода</value>
  </data>
  <data name="TransSettingsProviderOfTranslation" xml:space="preserve">
    <value>Сервис перевода</value>
  </data>
  <data name="TransSettingsKeyboardShortcuts" xml:space="preserve">
    <value>Комбинация клавиш для перевода выделенного текста</value>
  </data>
  <data name="TransSettingsInterKeyboardShortcuts" xml:space="preserve">
    <value>Нажмите комбинацию клавиш</value>
  </data>
  <data name="TransSettingsUseDoubleCtrl" xml:space="preserve">
    <value>Двойное нажатие кнопки Ctrl</value>
  </data>
  <data name="TransSettingsTranslationIsAlways" xml:space="preserve">
    <value>Переводить при выделении текста мышкой</value>
  </data>
  <data name="TransSettingsIsOn" xml:space="preserve">
    <value>Перевод включен</value>
  </data>
  <data name="GeneralSettingsHeader" xml:space="preserve">
    <value>Общие настройки</value>
  </data>
  <data name="GeneralSettingsLanguageProgram" xml:space="preserve">
    <value>Язык интерфейса программы</value>
  </data>
  <data name="GeneralSettingsLanguageProgramRestart" xml:space="preserve">
    <value>Перезапустить приложение для изменения языка?</value>
  </data>
  <data name="GeneralSettingsOther" xml:space="preserve">
    <value>Разное</value>
  </data>
  <data name="GeneralSettingsMinimizeToTray" xml:space="preserve">
    <value>Сворачивать в трей</value>
  </data>
  <data name="GeneralSettingsStartUpWithWindows" xml:space="preserve">
    <value>Запуск с windows</value>
  </data>
  <data name="GeneralSettingsStartAdmin" xml:space="preserve">
    <value>Запуск с правами администратора</value>
  </data>
  <data name="GeneralSettingsIsCheckUpdate" xml:space="preserve">
    <value>Проверять обновления</value>
  </data>
  <data name="GeneralSettingsIsCheckUpdateBeta" xml:space="preserve">
    <value>Обновлять до бета-версии</value>
  </data>
  <data name="GeneralSettingsTheme" xml:space="preserve">
    <value>Тема</value>
  </data>
  <data name="GeneralSettingsThemeDayNight" xml:space="preserve">
    <value>День или ночь</value>
  </data>
  <data name="GeneralSettingsThemeAccent" xml:space="preserve">
    <value>Стили оформления</value>
  </data>
  <data name="GeneralSettingsDataFilePath" xml:space="preserve">
    <value>Папка для сохранения настроек программы</value>
  </data>
  <data name="GeneralSettingsIsProxyUseIE" xml:space="preserve">
    <value>Использовать системные настройки прокси</value>
  </data>
  <data name="GeneralSettingsProxyServer" xml:space="preserve">
    <value>Введите адрес сервера</value>
  </data>
  <data name="GeneralSettingsProxyPort" xml:space="preserve">
    <value>Введите порт</value>
  </data>
  <data name="GeneralSettingsProxyUsername" xml:space="preserve">
    <value>Введите имя пользователя</value>
  </data>
  <data name="GeneralSettingsProxyPassword" xml:space="preserve">
    <value>Введите пароль</value>
  </data>
  <data name="GeneralSettingsSaveProxy" xml:space="preserve">
    <value>Сохранить настройки прокси</value>
  </data>
  <data name="GeneralSettingsProxy" xml:space="preserve">
    <value>Настройки прокси</value>
  </data>
  <data name="GeneralSettingsProxyError" xml:space="preserve">
    <value>Ошибка параметров прокси сервера, измените настройки</value>
  </data>
  <data name="GeneralSettingsUseNightTheme" xml:space="preserve">
    <value>Использовать темную тему в вечерние часы</value>
  </data>
  <data name="GeneralSettingsUseNightThemeStart" xml:space="preserve">
    <value>Вечерние часы от</value>
  </data>
  <data name="GeneralSettingsUseNightThemeEnd" xml:space="preserve">
    <value>Вечерние часы до</value>
  </data>
  <data name="GeneralSettingsIsStopWorkingFullScreen" xml:space="preserve">
    <value>Отключить все функции в программах запущенных в полноэкранном режиме</value>
  </data>
  <data name="GeneralSettingsImport" xml:space="preserve">
    <value>Импорт настроек</value>
  </data>
  <data name="GeneralSettingsExport" xml:space="preserve">
    <value>Экспорт настроек</value>
  </data>
  <data name="GeneralTab" xml:space="preserve">
    <value>Основные настройки</value>
  </data>
  <data name="TranslationTab" xml:space="preserve">
    <value>Переводчик</value>
  </data>
  <data name="CheckSpellingTab" xml:space="preserve">
    <value>Проверка орфографии</value>
  </data>
  <data name="LangFlagTab" xml:space="preserve">
    <value>Индикатор раскладки</value>
  </data>
  <data name="ProTabs" xml:space="preserve">
    <value>Функции PRO</value>
  </data>
  <data name="ClipboardTab" xml:space="preserve">
    <value>Буфер обмена</value>
  </data>
  <data name="DiareTab" xml:space="preserve">
    <value>Дневник</value>
  </data>
  <data name="ConverterTab" xml:space="preserve">
    <value>Конвертер текста</value>
  </data>
  <data name="SwitcherTab" xml:space="preserve">
    <value>Переключение раскладки</value>
  </data>
  <data name="AutoSwitcherTab" xml:space="preserve">
    <value>Автопереключение</value>
  </data>
  <data name="ProgramsExceptionsTab" xml:space="preserve">
    <value>Программы-исключения</value>
  </data>
  <data name="ProgramsSetLayoutTab" xml:space="preserve">
    <value>Раскладки по умолчанию</value>
  </data>
  <data name="UniversalWindowTab" xml:space="preserve">
    <value>SmartClick</value>
  </data>
  <data name="AboutTab" xml:space="preserve">
    <value>О программе</value>
  </data>
  <data name="ProTab" xml:space="preserve">
    <value>Лицензия</value>
  </data>
  <data name="AutochangeTab" xml:space="preserve">
    <value>Шаблоны</value>
  </data>
  <data name="UniTranslate" xml:space="preserve">
    <value>Перевести</value>
  </data>
  <data name="UniCopy" xml:space="preserve">
    <value>Скопировать</value>
  </data>
  <data name="UniSpellCheck" xml:space="preserve">
    <value>Проверить орфографию</value>
  </data>
  <data name="UniSearch" xml:space="preserve">
    <value>Вызов поиска</value>
  </data>
  <data name="UniLink" xml:space="preserve">
    <value>Открыть ссылку в браузере</value>
  </data>
  <data name="UniLinkTranslate" xml:space="preserve">
    <value>Перевести сайт по ссылке</value>
  </data>
  <data name="UniLinkShorter" xml:space="preserve">
    <value>Генерация короткой ссылки</value>
  </data>
  <data name="UniEmail" xml:space="preserve">
    <value>Создать почтовое сообщение</value>
  </data>
  <data name="UniPaste" xml:space="preserve">
    <value>Вставить текст</value>
  </data>
  <data name="UniPasteUnf" xml:space="preserve">
    <value>Вставить текст без форматирования</value>
  </data>
  <data name="UniClipboardHistory" xml:space="preserve">
    <value>Открыть историю буфера обмена</value>
  </data>
  <data name="UniTranslateHistory" xml:space="preserve">
    <value>Открыть историю переводов</value>
  </data>
  <data name="UniDiaryHistory" xml:space="preserve">
    <value>Открыть дневник</value>
  </data>
  <data name="UniUppercase" xml:space="preserve">
    <value>Изменить регистр выделенного текста</value>
  </data>
  <data name="UniAutochange" xml:space="preserve">
    <value>Открыть список шаблонов</value>
  </data>
  <data name="UniConverter" xml:space="preserve">
    <value>Конвертер текста</value>
  </data>
  <data name="UniDownCase" xml:space="preserve">
    <value>Привести к нижнему регистру выделенный текст</value>
  </data>
  <data name="UniUpCase" xml:space="preserve">
    <value>Привести к верхнему регистру выделенный текст</value>
  </data>
  <data name="UniInvertCase" xml:space="preserve">
    <value>Инвертировать регистр выделенного текста</value>
  </data>
  <data name="UniEnclose" xml:space="preserve">
    <value>Обрамление выделенного текста символами</value>
  </data>
  <data name="UniTranslit" xml:space="preserve">
    <value>Транслитерация выделенного текста</value>
  </data>
  <data name="UniConvertExpressions" xml:space="preserve">
    <value>Конвертер чисел и дат в строки, вычисление выражений</value>
  </data>
  <data name="UniTextConverter" xml:space="preserve">
    <value>Конвертер</value>
  </data>
  <data name="UniTextTranslate" xml:space="preserve">
    <value>Перевод</value>
  </data>
  <data name="UniTextCopy" xml:space="preserve">
    <value>Копировать</value>
  </data>
  <data name="UniTextSpellCheck" xml:space="preserve">
    <value>Орфография</value>
  </data>
  <data name="UniTextSearch" xml:space="preserve">
    <value>Поиск</value>
  </data>
  <data name="UniTextLink" xml:space="preserve">
    <value>Ссылка</value>
  </data>
  <data name="UniTextLinkTranslate" xml:space="preserve">
    <value>Перевод сайта</value>
  </data>
  <data name="UniTextLinkShorter" xml:space="preserve">
    <value>URL Shorter</value>
  </data>
  <data name="UniTextEmail" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="UniTextPaste" xml:space="preserve">
    <value>Вставка</value>
  </data>
  <data name="UniTextPasteUnf1" xml:space="preserve">
    <value>Вставка без формат.</value>
  </data>
  <data name="UniTextPasteUnf2" xml:space="preserve">
    <value>без формат.</value>
  </data>
  <data name="UniTextClipboardHistory1" xml:space="preserve">
    <value>История буфера</value>
  </data>
  <data name="UniTextClipboardHistory2" xml:space="preserve">
    <value>буфера</value>
  </data>
  <data name="UniTextDiaryHistory" xml:space="preserve">
    <value>Дневник</value>
  </data>
  <data name="UniTextAutochange" xml:space="preserve">
    <value>Шаблоны</value>
  </data>
  <data name="UniTextDownCase" xml:space="preserve">
    <value>Регистр вниз</value>
  </data>
  <data name="UniTextUpCase" xml:space="preserve">
    <value>Регистр вверх</value>
  </data>
  <data name="UniTextInvertCase" xml:space="preserve">
    <value>Инвертировать регистр</value>
  </data>
  <data name="UniTextEnclose" xml:space="preserve">
    <value>Обрамление текста</value>
  </data>
  <data name="UniTextTranslit" xml:space="preserve">
    <value>Транслитерация</value>
  </data>
  <data name="UniTextConvertExpressions" xml:space="preserve">
    <value>Мат. выражения</value>
  </data>
  <data name="HistoryDateStart" xml:space="preserve">
    <value>Начало:</value>
  </data>
  <data name="HistoryDateEnd" xml:space="preserve">
    <value>Конец:</value>
  </data>
  <data name="HistoryDelSelected" xml:space="preserve">
    <value>Удалить</value>
  </data>
  <data name="HistoryClear" xml:space="preserve">
    <value>Очистить</value>
  </data>
  <data name="HistoryToggleSwitch" xml:space="preserve">
    <value>Включено</value>
  </data>
  <data name="DiaryHeaderText" xml:space="preserve">
    <value>ТЕКСТ</value>
  </data>
  <data name="DiaryHeaderApp" xml:space="preserve">
    <value>ПРИЛОЖЕНИЕ</value>
  </data>
  <data name="ProgramsExceptionsHeader" xml:space="preserve">
    <value>Программы-исключения</value>
  </data>
  <data name="ProgramsExceptionsAddNew" xml:space="preserve">
    <value>Добавить</value>
  </data>
  <data name="ProgramsExceptionsAddNewExeFile" xml:space="preserve">
    <value>Добавить exe файл</value>
  </data>
  <data name="ProgramsExceptionsAddNewFilesFromFolder" xml:space="preserve">
    <value>Добавить папку</value>
  </data>
  <data name="ProgramsExceptionsAddNewTitle" xml:space="preserve">
    <value>Добавить по заголовку окна</value>
  </data>
  <data name="ProgramsExceptionsIsOnLayoutFlag" xml:space="preserve">
    <value>Индикатор раскладки</value>
  </data>
  <data name="ProgramsExceptionsIsOnLayoutSwitcher" xml:space="preserve">
    <value>Переключение раскладки</value>
  </data>
  <data name="ProgramsExceptionsIsOnSmartClick" xml:space="preserve">
    <value>SmartClick</value>
  </data>
  <data name="ProgramsExceptionsIsOnAutochange" xml:space="preserve">
    <value>Шаблоны</value>
  </data>
  <data name="ProgramsExceptionsIsOnHotKeys" xml:space="preserve">
    <value>Горячие клавиши</value>
  </data>
  <data name="ProgramsExceptionsIsOnAutoSwitch" xml:space="preserve">
    <value>Автопереключение языка</value>
  </data>
  <data name="ProgramsExceptionsIsOnDiary" xml:space="preserve">
    <value>Дневник ввода текста</value>
  </data>
  <data name="ProgramsExceptionsIsOnConverter" xml:space="preserve">
    <value>Конвертер и регистр текста</value>
  </data>
  <data name="ProgramsExceptionsIsOnClipboard" xml:space="preserve">
    <value>История буфера обмена</value>
  </data>
  <data name="ProgramsExceptionsIsOnClipboardImage" xml:space="preserve">
    <value>Сохранять изображения</value>
  </data>
  <data name="ProgramsExceptionsDelete" xml:space="preserve">
    <value>Удалить</value>
  </data>
  <data name="ProgramsExceptionsAddNewHelp" xml:space="preserve">
    <value>Кликните мышкой в необходимой программе</value>
  </data>
  <data name="ProgramsExceptionsCurrentInfoTooltip" xml:space="preserve">
    <value>Отметьте функции, которые будут работать для программы</value>
  </data>
  <data name="ProgramsExceptionsProgramsList" xml:space="preserve">
    <value>Добавление из списка программ</value>
  </data>
  <data name="ProgramsExceptionsFromPoint" xml:space="preserve">
    <value>Добавление с помощью курсора</value>
  </data>
  <data name="ProgramsSetLayoutHeader" xml:space="preserve">
    <value>Программы-языки</value>
  </data>
  <data name="ProgramsSetLayoutTabHeader" xml:space="preserve">
    <value>Язык по умолчанию для выбранных программ</value>
  </data>
  <data name="ProSettingsHeader" xml:space="preserve">
    <value>Активация PRO функций</value>
  </data>
  <data name="ProSettingsStatus" xml:space="preserve">
    <value>Статус</value>
  </data>
  <data name="ProSettingsInput" xml:space="preserve">
    <value>Код активации</value>
  </data>
  <data name="ProSettingsActivation" xml:space="preserve">
    <value>Активировать</value>
  </data>
  <data name="ProSettingsEvaluation" xml:space="preserve">
    <value>Попробовать</value>
  </data>
  <data name="ProSettingsStatusOk" xml:space="preserve">
    <value>PRO активировано</value>
  </data>
  <data name="ProSettingsStatusEvaluate" xml:space="preserve">
    <value>Пробный период в течение 40 дней</value>
  </data>
  <data name="ProSettingsLicenseInfo" xml:space="preserve">
    <value>Сведения о лицензии</value>
  </data>
  <data name="ProSettingsLicenseActivateDate" xml:space="preserve">
    <value>Дата активации:</value>
  </data>
  <data name="ProSettingsLicenseExpiryDate" xml:space="preserve">
    <value>Срок действия лицензии:</value>
  </data>
  <data name="ProSettingsLicenseEmail" xml:space="preserve">
    <value>Email:</value>
  </data>
  <data name="ProSettingsLicenseUserName" xml:space="preserve">
    <value>Владелец:</value>
  </data>
  <data name="ProSettingsGetData" xml:space="preserve">
    <value>Информация о лицензии</value>
  </data>
  <data name="ProSettingsLicenseUsersCount" xml:space="preserve">
    <value>Количество мест:</value>
  </data>
  <data name="ProSettingsLicenseCountReact" xml:space="preserve">
    <value>Количество доступных реактиваций:</value>
  </data>
  <data name="ProSettingsLicenseCountFreeSeats" xml:space="preserve">
    <value>Свободных мест:</value>
  </data>
  <data name="ProSettingsLicenseIsEnterprise" xml:space="preserve">
    <value>Тип лицензии:</value>
  </data>
  <data name="ProSettingsLicenseNotPro" xml:space="preserve">
    <value>PRO не активированно</value>
  </data>
  <data name="ProSettingsIsEvaluation" xml:space="preserve">
    <value>PRO активированно на пробный период</value>
  </data>
  <data name="ProSettingsPurchase" xml:space="preserve">
    <value>Купить</value>
  </data>
  <data name="ProSettingsEmail" xml:space="preserve">
    <value>Введите email</value>
  </data>
  <data name="ProSettingsCode" xml:space="preserve">
    <value>Введите код</value>
  </data>
  <data name="ProSendEmailLic" xml:space="preserve">
    <value>Информация по вашей лицензии отправлена на email:</value>
  </data>
  <data name="ProSettingsDelete" xml:space="preserve">
    <value>Удалить лицензию</value>
  </data>
  <data name="ProSettingsCodeEmail" xml:space="preserve">
    <value>Введите код и email</value>
  </data>
  <data name="ProSettingsActivationOk" xml:space="preserve">
    <value>Активация завершена успешно</value>
  </data>
  <data name="ProSettingsActivationError" xml:space="preserve">
    <value>Активация завершена с ошибкой</value>
  </data>
  <data name="ProSettingsActivationBlocked" xml:space="preserve">
    <value>Активация завершена с ошибкой, ваша лицензия заблокирована</value>
  </data>
  <data name="ProSettingsActivationReactivated" xml:space="preserve">
    <value>Программа была активирована на новом рабочем месте, при этом была деактивирована на компьютере</value>
  </data>
  <data name="ProSettingsActivationInternetError" xml:space="preserve">
    <value>Активация завершена с ошибкой, проверьте интернет соединение</value>
  </data>
  <data name="ProSettingsActivationErrorEmail" xml:space="preserve">
    <value>Активация завершена с ошибкой, возможно введен неверный email</value>
  </data>
  <data name="ProSettingsExpiredEva" xml:space="preserve">
    <value>Пробный период истек</value>
  </data>
  <data name="ProSettingsExpired" xml:space="preserve">
    <value>Срок действия лицензии истек</value>
  </data>
  <data name="ProSettingsBlockedByMonthActivateLimit" xml:space="preserve">
    <value>Активация невозможна по причине превышения лимита реактиваций, докупите количество рабочих мест для данной лицензии</value>
  </data>
  <data name="ProSettingsBlockedByEarlyActivation" xml:space="preserve">
    <value>Активация невозможна из-за реактивации данной лицензии на другом компьютере</value>
  </data>
  <data name="ProSettingsNewLic" xml:space="preserve">
    <value>Ваш код активации был обновлен</value>
  </data>
  <data name="ProSettingsNoExpiry" xml:space="preserve">
    <value>Бессрочная лицензия</value>
  </data>
  <data name="ProBlockedByEarlyActivation" xml:space="preserve">
    <value>Вы превысили лимит количества рабочих мест для лицензии, все PRO функции будут отключены</value>
  </data>
  <data name="ProBlockedExp" xml:space="preserve">
    <value>Срок действия PRO версии истек, новую лицензию можно приобрести на сайте EVERYLANG.NET</value>
  </data>
  <data name="ProBlockedExpToDays" xml:space="preserve">
    <value>Срок действия PRO версии скоро истекает</value>
  </data>
  <data name="ProBlockedByMonthActivateLimit" xml:space="preserve">
    <value>Превышен лимит реактиваций лицензии за последние 30 дней, все PRO функции будут отключены</value>
  </data>
  <data name="ProBlocked" xml:space="preserve">
    <value>Ваша лицензия заблокирована, все PRO функции будут отключены</value>
  </data>
  <data name="UniversalWindowSettingsHeader" xml:space="preserve">
    <value>SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsUniversalWindowIsOn" xml:space="preserve">
    <value>SmartClick включен</value>
  </data>
  <data name="UniversalWindowSettingsSearchServices" xml:space="preserve">
    <value>Выберите поисковый сервис для SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsItemsCheck" xml:space="preserve">
    <value>Отметьте функции, которые будут доступны</value>
  </data>
  <data name="UniversalWindowSettingsShowOnPressLeftAndRightMouseButtons" xml:space="preserve">
    <value>Открывать при нажатии левой и затем правой кнопки мыши</value>
  </data>
  <data name="UniversalWindowSettingsShowOnDoubleMiddle" xml:space="preserve">
    <value>Открывать при двойном нажатии на среднюю кнопку мыши</value>
  </data>
  <data name="UniversalWindowSettingsShowOnPressHotKeys" xml:space="preserve">
    <value>Использовать горячии клавиши для открытия</value>
  </data>
  <data name="SmartClickShortcutSettingsHeader" xml:space="preserve">
    <value>Горячие клавиши для открытия окна SmartClick</value>
  </data>
  <data name="UniversalWindowSettingsShowMiniOn" xml:space="preserve">
    <value>Показывать вспомогательное окно после выделения текста</value>
  </data>
  <data name="SmartClickMiniSize" xml:space="preserve">
    <value>Размер окна</value>
  </data>
  <data name="SmartClickMiniPos" xml:space="preserve">
    <value>Позиция окна относительно указателя мыши</value>
  </data>
  <data name="DiarySettingsHeader" xml:space="preserve">
    <value>Дневник</value>
  </data>
  <data name="DiaryShortcuts" xml:space="preserve">
    <value>Открыть дневник</value>
  </data>
  <data name="DiaryIsOn" xml:space="preserve">
    <value>Дневник включен</value>
  </data>
  <data name="DiaryPassword" xml:space="preserve">
    <value>Пароль на дневник</value>
  </data>
  <data name="DiaryPasswordOld" xml:space="preserve">
    <value>Введите старый пароль на дневник</value>
  </data>
  <data name="DiaryPasswordSaved" xml:space="preserve">
    <value>Новый пароль сохранен</value>
  </data>
  <data name="DiaryPasswordReset" xml:space="preserve">
    <value>Пароль сброшен</value>
  </data>
  <data name="DiaryMaxItems" xml:space="preserve">
    <value>Количество записей в дневнике</value>
  </data>
  <data name="DiaryOldPasswordWrong" xml:space="preserve">
    <value>Введенный пароль не верен, сбросить текущий пароль? При этом все данные из дневника будут удалены</value>
  </data>
  <data name="IsSaveOneWordSentences" xml:space="preserve">
    <value>Сохранять однословные предложения</value>
  </data>
  <data name="AutochangeHelperFromText" xml:space="preserve">
    <value>Текст, который заменить (необязательно):</value>
  </data>
  <data name="AutochangeHelperToText" xml:space="preserve">
    <value>Текст шаблона:</value>
  </data>
  <data name="AutochangeHelperLangListDesc" xml:space="preserve">
    <value>На какой язык переключать раскладку</value>
  </data>
  <data name="AutochangeHelperLangListNoSwitch" xml:space="preserve">
    <value>Не переключать</value>
  </data>
  <data name="AutochangeHelperTags" xml:space="preserve">
    <value>Тэги (через пробел):</value>
  </data>
  <data name="AutochangeHelperTagsWatermark" xml:space="preserve">
    <value>Тэги</value>
  </data>
  <data name="AutochangeHelperDesc" xml:space="preserve">
    <value>Описание:</value>
  </data>
  <data name="AutochangeHelperOk" xml:space="preserve">
    <value>Сохранить</value>
  </data>
  <data name="AutochangeHelperCancel" xml:space="preserve">
    <value>Отмена</value>
  </data>
  <data name="AutochangeHelperSaveCursorPosition" xml:space="preserve">
    <value>Сохранять позицию курсора</value>
  </data>
  <data name="AutochangeHelperChangeAtOnce" xml:space="preserve">
    <value>Заменять при наборе</value>
  </data>
  <data name="AutochangeTextHeader" xml:space="preserve">
    <value>Шаблоны</value>
  </data>
  <data name="ToReplacerButton" xml:space="preserve">
    <value>Новый шаблон</value>
  </data>
  <data name="AutochangeDelWithTag" xml:space="preserve">
    <value>Удалить все шаблоны с данным тегом?</value>
  </data>
  <data name="AutochangeOnInSnippetsList" xml:space="preserve">
    <value>Включена автозамена</value>
  </data>
  <data name="AutochangeSnippetsList" xml:space="preserve">
    <value>Редактировать шаблоны</value>
  </data>
  <data name="AutochangeHeaderFromText" xml:space="preserve">
    <value>Что заменять</value>
  </data>
  <data name="AutochangeHeaderToText" xml:space="preserve">
    <value>На что заменять</value>
  </data>
  <data name="AutochangeAddNew" xml:space="preserve">
    <value>Добавить</value>
  </data>
  <data name="AutochangeEdit" xml:space="preserve">
    <value>Изменить</value>
  </data>
  <data name="AutochangeDelete" xml:space="preserve">
    <value>Удалить</value>
  </data>
  <data name="AutochangeOtherLayout" xml:space="preserve">
    <value>Заменять при наборе в другой раскладке</value>
  </data>
  <data name="AutochangeCaseLetters" xml:space="preserve">
    <value>Учитывать регистр букв</value>
  </data>
  <data name="AutochangeShowMiniWindow" xml:space="preserve">
    <value>Показывать подсказку при наборе текста</value>
  </data>
  <data name="AutochangeIsEnabledCountUsage" xml:space="preserve">
    <value>Сортировать в зависимости от частоты использования</value>
  </data>
  <data name="AutochangeChangeMethods" xml:space="preserve">
    <value>Заменять по:</value>
  </data>
  <data name="AutochangeIsOn" xml:space="preserve">
    <value>Шаблоны включены</value>
  </data>
  <data name="AutochangeKeyboardShortcuts" xml:space="preserve">
    <value>Открыть список шаблонов для вставки</value>
  </data>
  <data name="AutochangeKeyboardShortcutsAddNew" xml:space="preserve">
    <value>Добавить выделенный текст в шаблоны</value>
  </data>
  <data name="AutochangeOnTab" xml:space="preserve">
    <value>Клавиша Tab</value>
  </data>
  <data name="AutochangeOnInter" xml:space="preserve">
    <value>Клавиша Enter</value>
  </data>
  <data name="AutochangeOnTabOrInter" xml:space="preserve">
    <value>Tab или Enter</value>
  </data>
  <data name="AutochangeOnSpace" xml:space="preserve">
    <value>Пробел</value>
  </data>
  <data name="AutochangeOnSpaceOrInter" xml:space="preserve">
    <value>Пробел или Enter</value>
  </data>
  <data name="HotKeyUseHotkey" xml:space="preserve">
    <value>Горячие клавиши</value>
  </data>
  <data name="HotKeyUseDoubleKeyDown" xml:space="preserve">
    <value>Двойное нажатие клавиши</value>
  </data>
  <data name="HotKeyUseMouseXKey" xml:space="preserve">
    <value>Нажатие кнопки мыши</value>
  </data>
  <data name="HotKeyIsON" xml:space="preserve">
    <value>Горячие клавиши включены</value>
  </data>
  <data name="HotKeyWithoutShortcutNull" xml:space="preserve">
    <value>Отсутствует</value>
  </data>
  <data name="HotKeyWithoutPressShortcut" xml:space="preserve">
    <value>Нажмите горячие клавиши</value>
  </data>
  <data name="HotKeyShortcut" xml:space="preserve">
    <value>Сочетание</value>
  </data>
  <data name="HotKeyDoubleKeyDown" xml:space="preserve">
    <value>Двойное нажатие</value>
  </data>
  <data name="HotKeyMouse" xml:space="preserve">
    <value>Нажатие кнопки мыши</value>
  </data>
  <data name="HotKeyDoubleKeyDownSelectKey" xml:space="preserve">
    <value>Выберите клавишу для двойного нажатия</value>
  </data>
  <data name="HotKeyMouseSelectKey" xml:space="preserve">
    <value>Выберите дополнительную кнопку мыши</value>
  </data>
  <data name="HotKeyIsOff" xml:space="preserve">
    <value>Горячие клавиши выключены</value>
  </data>
  <data name="HotKeyDoubleLeftOrRightCtrl" xml:space="preserve">
    <value>Левый или правый Ctrl</value>
  </data>
  <data name="HotKeyDoubleLeftCtrl" xml:space="preserve">
    <value>Левый Ctrl</value>
  </data>
  <data name="HotKeyDoubleRightCtrl" xml:space="preserve">
    <value>Правый Ctrl</value>
  </data>
  <data name="HotKeyDoubleLeftOrRightShift" xml:space="preserve">
    <value>Левый или правый Shift</value>
  </data>
  <data name="HotKeyDoubleLeftShift" xml:space="preserve">
    <value>Левый Shift</value>
  </data>
  <data name="HotKeyDoubleRightShift" xml:space="preserve">
    <value>Правый Shift</value>
  </data>
  <data name="HotKeyDoubleLeftAlt" xml:space="preserve">
    <value>Левый Alt</value>
  </data>
  <data name="HotKeyDoubleRightAlt" xml:space="preserve">
    <value>Правый Alt</value>
  </data>
  <data name="SoundOnOff" xml:space="preserve">
    <value>Звук включен</value>
  </data>
  <data name="SoundFormVolume" xml:space="preserve">
    <value>Громкость</value>
  </data>
  <data name="SoundFormSelectTrack" xml:space="preserve">
    <value>Выберите звук</value>
  </data>
  <data name="StartPageHeader" xml:space="preserve">
    <value>Перед началом использования программы ознакомьтесь с документацией</value>
  </data>
  <data name="StartPageHelp" xml:space="preserve">
    <value>Изучите функции программы</value>
  </data>
  <data name="StartPageVideo" xml:space="preserve">
    <value>Посмотрите видео-презентацию</value>
  </data>
  <data name="StartPageSite" xml:space="preserve">
    <value>Сайт программы</value>
  </data>
  <data name="StartPageLicense" xml:space="preserve">
    <value>Приобрести лицензию</value>
  </data>
  <data name="OcrHeader" xml:space="preserve">
    <value>Распознавание текста</value>
  </data>
  <data name="OcrKeyboardShortcuts" xml:space="preserve">
    <value>Комбинация клавиш для запуска распознавания текста</value>
  </data>
  <data name="OcrDescDefault" xml:space="preserve">
    <value>Выберите языки по умолчанию</value>
  </data>
  <data name="OcrDescLang" xml:space="preserve">
    <value>Выберите языки</value>
  </data>
  <data name="OcrDescNotSup" xml:space="preserve">
    <value>Использование одновременно европейских и азиатских языков не поддерживается</value>
  </data>
  <data name="OcrEuropeanLang" xml:space="preserve">
    <value>Европейские языки:</value>
  </data>
  <data name="OcrAsianLang" xml:space="preserve">
    <value>Азиатские языки:</value>
  </data>
  <data name="OcrCaptureArea" xml:space="preserve">
    <value>Выделить область экрана</value>
  </data>
  <data name="OcrOpenImageOrPDFFile" xml:space="preserve">
    <value>Открыть изображение</value>
  </data>
  <data name="OcrRecognize" xml:space="preserve">
    <value>Распознать</value>
  </data>
  <data name="OcrRecognizeBarcode" xml:space="preserve">
    <value>Распознать barcode</value>
  </data>
  <data name="OcrSelectLanguages" xml:space="preserve">
    <value>Выберите языки</value>
  </data>
  <data name="OcrStartText" xml:space="preserve">
    <value>Выделите область на экране или загрузите файл для распознавания</value>
  </data>
  <data name="OcrTab" xml:space="preserve">
    <value>Распознавание текста</value>
  </data>
  <data name="OcrInit" xml:space="preserve">
    <value>Подождите, идет загрузка модуля</value>
  </data>
  <data name="OcrLoadLibs" xml:space="preserve">
    <value>Нажмите для загрузки модуля</value>
  </data>
  <data name="OcrWaitResult" xml:space="preserve">
    <value>Текст скопирован</value>
  </data>
  <data name="OcrWaitResultFail" xml:space="preserve">
    <value>Текст не распознан</value>
  </data>
  <data name="OcrCopyImage" xml:space="preserve">
    <value>Скопировать изображение</value>
  </data>
  <data name="OcrCopyText" xml:space="preserve">
    <value>Скопировать текст</value>
  </data>
  <data name="ReplaceTo" xml:space="preserve">
    <value>Заменить на:</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Искать:</value>
  </data>
  <data name="TotalMatchFound" xml:space="preserve">
    <value>Всего найдено соответствий:</value>
  </data>
  <data name="NoMatchFound" xml:space="preserve">
    <value>Совпадений не найдено!</value>
  </data>
  <data name="TotalMatchFoundReplace" xml:space="preserve">
    <value>Всего произведено замен:</value>
  </data>
  <data name="DiaryHeaderDate" xml:space="preserve">
    <value>ДАТА</value>
  </data>
  <data name="DiaryHeaderFormat" xml:space="preserve">
    <value>ТИП</value>
  </data>
  <data name="ClipboardOff" xml:space="preserve">
    <value>Менеджер буфера обмена выключен</value>
  </data>
  <data name="GridViewClearFilter" xml:space="preserve">
    <value>Очистить фильтр</value>
  </data>
  <data name="GridViewColumnsSelectionButtonTooltip" xml:space="preserve">
    <value>Выберите столбцы</value>
  </data>
  <data name="GridViewFilter" xml:space="preserve">
    <value>Фильтр</value>
  </data>
  <data name="GridViewFilterAnd" xml:space="preserve">
    <value>И</value>
  </data>
  <data name="GridViewFilterContains" xml:space="preserve">
    <value>Содержит</value>
  </data>
  <data name="GridViewFilterDoesNotContain" xml:space="preserve">
    <value>Не содержит</value>
  </data>
  <data name="GridViewFilterEndsWith" xml:space="preserve">
    <value>Заканчивается на</value>
  </data>
  <data name="GridViewFilterIsContainedIn" xml:space="preserve">
    <value>Содержится в</value>
  </data>
  <data name="GridViewFilterIsEmpty" xml:space="preserve">
    <value>Пустой</value>
  </data>
  <data name="GridViewFilterIsEqualTo" xml:space="preserve">
    <value>Равно</value>
  </data>
  <data name="GridViewFilterIsGreaterThan" xml:space="preserve">
    <value>Больше, чем</value>
  </data>
  <data name="GridViewFilterIsGreaterThanOrEqualTo" xml:space="preserve">
    <value>Больше или равно</value>
  </data>
  <data name="GridViewFilterIsLessThan" xml:space="preserve">
    <value>Меньше чем</value>
  </data>
  <data name="GridViewFilterIsLessThanOrEqualTo" xml:space="preserve">
    <value>Меньше или равно</value>
  </data>
  <data name="GridViewFilterIsNotContainedIn" xml:space="preserve">
    <value>Не содержится в</value>
  </data>
  <data name="GridViewFilterIsNotEmpty" xml:space="preserve">
    <value>не пустой</value>
  </data>
  <data name="GridViewFilterIsNotEqualTo" xml:space="preserve">
    <value>не равно</value>
  </data>
  <data name="GridViewFilterIsNotNull" xml:space="preserve">
    <value>Не является нулевым</value>
  </data>
  <data name="GridViewFilterIsNull" xml:space="preserve">
    <value>Нулевой</value>
  </data>
  <data name="GridViewFilterMatchCase" xml:space="preserve">
    <value>Учитывать регистр</value>
  </data>
  <data name="GridViewFilterOr" xml:space="preserve">
    <value>Или</value>
  </data>
  <data name="GridViewFilterSelectAll" xml:space="preserve">
    <value>Выбрать все</value>
  </data>
  <data name="GridViewFilterShowRowsWithValueThat" xml:space="preserve">
    <value>Показать строки со значением, которое</value>
  </data>
  <data name="GridViewFilterStartsWith" xml:space="preserve">
    <value>Начинается с</value>
  </data>
  <data name="GridViewGroupPanelText" xml:space="preserve">
    <value>Перетащите заголовок столбца и поместите его сюда, чтобы сгруппировать по этому столбцу</value>
  </data>
  <data name="GridViewGroupPanelTopText" xml:space="preserve">
    <value>Заголовок группы</value>
  </data>
  <data name="GridViewGroupPanelTopTextGrouped" xml:space="preserve">
    <value>Сгруппировано по:</value>
  </data>
  <data name="GridViewSearchPanelTopText" xml:space="preserve">
    <value>Полнотекстовый поиск</value>
  </data>
  <data name="DiaryOff" xml:space="preserve">
    <value>Дневник выключен</value>
  </data>
  <data name="DiaryOn" xml:space="preserve">
    <value>Дневник включен</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>Отключены</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>Включены</value>
  </data>
  <data name="SwitcherSettingsIsOff" xml:space="preserve">
    <value>Переключение раскладки отключено</value>
  </data>
  <data name="AutoSwitchSettingsIsOff" xml:space="preserve">
    <value>Автопереключение раскладки выключено</value>
  </data>
  <data name="AutoSwitchSettingsIsOn" xml:space="preserve">
    <value>Автопереключение раскладки включено</value>
  </data>
  <data name="GridViewAlwaysVisibleNewRow" xml:space="preserve">
    <value>Нажмите здесь, чтобы добавить новый элемент</value>
  </data>
  <data name="TransSettingsClearAllHistory" xml:space="preserve">
    <value>Очистить историю переводов</value>
  </data>
  <data name="TransSettingsHistoryIsOn" xml:space="preserve">
    <value>Хранить историю переводов</value>
  </data>
  <data name="CopyTranslatedText" xml:space="preserve">
    <value>Копировать переведенный текст</value>
  </data>
  <data name="ClearAll" xml:space="preserve">
    <value>Очистить все</value>
  </data>
  <data name="SiteSourceButton" xml:space="preserve">
    <value>Открыть в браузере</value>
  </data>
  <data name="CloseQuestion" xml:space="preserve">
    <value>Закрыть программу?</value>
  </data>
  <data name="TransSettingsFavoriteLanguages" xml:space="preserve">
    <value>Избранные языки</value>
  </data>
  <data name="TransSettingsChooseYourFavoriteLanguages" xml:space="preserve">
    <value>Выберите ваши избранные языки</value>
  </data>
  <data name="GeneralSettingsFont" xml:space="preserve">
    <value>Шрифт</value>
  </data>
  <data name="ProgramsExceptionsCurrentInfo" xml:space="preserve">
    <value>Функции</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Загрузка</value>
  </data>
  <data name="ImageEditor_CanvasResize" xml:space="preserve">
    <value>Изменение размера полотна</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Закрыть</value>
  </data>
  <data name="ImageEditor_Adjust" xml:space="preserve">
    <value>Коррекция</value>
  </data>
  <data name="ImageEditor_Amount" xml:space="preserve">
    <value>Сумма</value>
  </data>
  <data name="ImageEditor_Auto" xml:space="preserve">
    <value>Авто</value>
  </data>
  <data name="ImageEditor_Background" xml:space="preserve">
    <value>Фон:</value>
  </data>
  <data name="ImageEditor_BorderColor" xml:space="preserve">
    <value>Цвет границы:</value>
  </data>
  <data name="ImageEditor_BorderThickness" xml:space="preserve">
    <value>Толщина границ:</value>
  </data>
  <data name="ImageEditor_CanvasSize" xml:space="preserve">
    <value>Размер полотна</value>
  </data>
  <data name="ImageEditor_ColorPicker_NoColorText_White" xml:space="preserve">
    <value>белый</value>
  </data>
  <data name="ImageEditor_Crop" xml:space="preserve">
    <value>Обрезать</value>
  </data>
  <data name="ImageEditor_DrawText" xml:space="preserve">
    <value>Текст рисунка</value>
  </data>
  <data name="ImageEditor_DrawText_YourTextHere" xml:space="preserve">
    <value>Ваш текст</value>
  </data>
  <data name="ImageEditor_DrawTool" xml:space="preserve">
    <value>Нарисовать</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushColor" xml:space="preserve">
    <value>Цвет кисти:</value>
  </data>
  <data name="ImageEditor_DrawTool_BrushSize" xml:space="preserve">
    <value>Размер кисти:</value>
  </data>
  <data name="ImageEditor_Effect_Blur" xml:space="preserve">
    <value>Размытие</value>
  </data>
  <data name="ImageEditor_Effect_Brightness" xml:space="preserve">
    <value>Яркость</value>
  </data>
  <data name="ImageEditor_Effect_ContrastAdjust" xml:space="preserve">
    <value>Контраст</value>
  </data>
  <data name="ImageEditor_Effect_HueShift" xml:space="preserve">
    <value>Изменение оттенка</value>
  </data>
  <data name="ImageEditor_Effect_InvertColors" xml:space="preserve">
    <value>Обратить цвета</value>
  </data>
  <data name="ImageEditor_Effect_Saturation" xml:space="preserve">
    <value>Насыщенность</value>
  </data>
  <data name="ImageEditor_Effect_Sharpen" xml:space="preserve">
    <value>Повысить резкость</value>
  </data>
  <data name="ImageEditor_Effects" xml:space="preserve">
    <value>Видоизменение</value>
  </data>
  <data name="ImageEditor_FlipHorizontal" xml:space="preserve">
    <value>Перевернуть горизонтально</value>
  </data>
  <data name="ImageEditor_FlipVertical" xml:space="preserve">
    <value>Перевернуть вертикально</value>
  </data>
  <data name="ImageEditor_FontSize" xml:space="preserve">
    <value>Размер шрифта</value>
  </data>
  <data name="ImageEditor_Height" xml:space="preserve">
    <value>Высота:</value>
  </data>
  <data name="ImageEditor_HorizontalPosition" xml:space="preserve">
    <value>Горизонтальное положение</value>
  </data>
  <data name="ImageEditor_ImageAlignment" xml:space="preserve">
    <value>Выравнивание изображения</value>
  </data>
  <data name="ImageEditor_ImagePreview" xml:space="preserve">
    <value>Предварительный просмотр изображения</value>
  </data>
  <data name="ImageEditor_ImageSize" xml:space="preserve">
    <value>Размер изображения</value>
  </data>
  <data name="ImageEditor_Open" xml:space="preserve">
    <value>Открыть</value>
  </data>
  <data name="ImageEditor_Options" xml:space="preserve">
    <value>Параметры</value>
  </data>
  <data name="ImageEditor_PreserveAspectRatio" xml:space="preserve">
    <value>Сохранить исходное соотношение сторон</value>
  </data>
  <data name="ImageEditor_Radius" xml:space="preserve">
    <value>Радиус:</value>
  </data>
  <data name="ImageEditor_Redo" xml:space="preserve">
    <value>Вернуть</value>
  </data>
  <data name="ImageEditor_RelativeSize" xml:space="preserve">
    <value>Относительный размер</value>
  </data>
  <data name="ImageEditor_Resize" xml:space="preserve">
    <value>Изменить размер</value>
  </data>
  <data name="ImageEditor_Rotate180" xml:space="preserve">
    <value>Повернуть на 180°</value>
  </data>
  <data name="ImageEditor_Rotate270" xml:space="preserve">
    <value>Повернуть на 270°</value>
  </data>
  <data name="ImageEditor_Rotate90" xml:space="preserve">
    <value>Повернуть на 90°</value>
  </data>
  <data name="ImageEditor_Rotation" xml:space="preserve">
    <value>Поворот</value>
  </data>
  <data name="ImageEditor_RoundCorners" xml:space="preserve">
    <value>Скругленные углы</value>
  </data>
  <data name="ImageEditor_Save" xml:space="preserve">
    <value>Сохранить</value>
  </data>
  <data name="ImageEditor_Shape" xml:space="preserve">
    <value>Фигура</value>
  </data>
  <data name="ImageEditor_Shapes_Ellipse" xml:space="preserve">
    <value>Эллипс</value>
  </data>
  <data name="ImageEditor_Shapes_Line" xml:space="preserve">
    <value>График</value>
  </data>
  <data name="ImageEditor_Shapes_Rectangle" xml:space="preserve">
    <value>Прямоугольник</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderColor" xml:space="preserve">
    <value>Цвет границы</value>
  </data>
  <data name="ImageEditor_ShapeTool_BorderThickness" xml:space="preserve">
    <value>Толщина границ</value>
  </data>
  <data name="ImageEditor_ShapeTool_FillShape" xml:space="preserve">
    <value>Форма Заполнения Трубы</value>
  </data>
  <data name="ImageEditor_ShapeTool_LockRatio" xml:space="preserve">
    <value>Заблокировать пропорции</value>
  </data>
  <data name="ImageEditor_ShapeTool_Shape" xml:space="preserve">
    <value>Фигура</value>
  </data>
  <data name="ImageEditor_ShapeTool_ShapeFill" xml:space="preserve">
    <value>Заливка фигуры</value>
  </data>
  <data name="ImageEditor_Text" xml:space="preserve">
    <value>Текст</value>
  </data>
  <data name="ImageEditor_TextColor" xml:space="preserve">
    <value>Цвет текста</value>
  </data>
  <data name="ImageEditor_TheFileCannotBeOpened" xml:space="preserve">
    <value>Невозможно открыть файл.</value>
  </data>
  <data name="ImageEditor_TheFileIsLocked" xml:space="preserve">
    <value>Невозможно открыть файл. Это может быть заблокировано другим приложением.</value>
  </data>
  <data name="ImageEditor_Transform" xml:space="preserve">
    <value>Преобразовать</value>
  </data>
  <data name="ImageEditor_UnableToSaveFile" xml:space="preserve">
    <value>Не удалось сохранить файл.</value>
  </data>
  <data name="ImageEditor_Undo" xml:space="preserve">
    <value>Отменить</value>
  </data>
  <data name="ImageEditor_UnsupportedFileFormat" xml:space="preserve">
    <value>Этот формат файла не поддерживается.</value>
  </data>
  <data name="ImageEditor_VerticalPosition" xml:space="preserve">
    <value>Вертикальное положение</value>
  </data>
  <data name="ImageEditor_Width" xml:space="preserve">
    <value>Ширина:</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>ОК</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Сброс</value>
  </data>
  <data name="ResetAll" xml:space="preserve">
    <value>Сбросить все</value>
  </data>
  <data name="OcrFromClipboard" xml:space="preserve">
    <value>Из буфера обмена</value>
  </data>
  <data name="OcrEditImage" xml:space="preserve">
    <value>Редактировать изображение</value>
  </data>
  <data name="PasteButtonWithoutClipboard" xml:space="preserve">
    <value>Вставить эмулируя ввод</value>
  </data>
  <data name="AutochangeEditor" xml:space="preserve">
    <value>Редактор шаблона</value>
  </data>
  <data name="AutochangeHelperFromTextTootlTip" xml:space="preserve">
    <value>Текст для замены:</value>
  </data>
  <data name="OcrModuleNotLoaded" xml:space="preserve">
    <value>Модуль распознавания текста не загружен</value>
  </data>
  <data name="AppearanceTab" xml:space="preserve">
    <value>Внешний вид</value>
  </data>
  <data name="OrderFunctionsTab" xml:space="preserve">
    <value>Порядок функций</value>
  </data>
  <data name="TranslateOnlyFavoriteLanguages" xml:space="preserve">
    <value>Показывать только избранные языки</value>
  </data>
  <data name="TranslateSowAll" xml:space="preserve">
    <value>Показать все</value>
  </data>
  <data name="GeneralSettingsThemeProgramRestart" xml:space="preserve">
    <value>Перезапустить программу для изменения темы?</value>
  </data>
  <data name="CommonWindowPressKeyForPast" xml:space="preserve">
    <value>Для вставки текста нажмите на клавишу</value>
  </data>
  <data name="MiminoteTab" xml:space="preserve">
    <value>Заметки</value>
  </data>
  <data name="NoteTab" xml:space="preserve">
    <value>Заметки</value>
  </data>
  <data name="NotesShow" xml:space="preserve">
    <value>Показать заметки</value>
  </data>
  <data name="NotesToArchive" xml:space="preserve">
    <value>В архив</value>
  </data>
  <data name="NotesAddNew" xml:space="preserve">
    <value>Добавить заметку</value>
  </data>
  <data name="NotesList" xml:space="preserve">
    <value>Список заметок</value>
  </data>
  <data name="NoteColor" xml:space="preserve">
    <value>Цвет заметки</value>
  </data>
  <data name="NoteConvertToNote" xml:space="preserve">
    <value>Преобразовать в заметку</value>
  </data>
  <data name="NoteConvertToTaskList" xml:space="preserve">
    <value>Преобразовать в список задач</value>
  </data>
  <data name="NotePasteAsPlainText" xml:space="preserve">
    <value>Вставить как обычный текст</value>
  </data>
  <data name="NotePasteAsText" xml:space="preserve">
    <value>Вставить как текст</value>
  </data>
  <data name="NoteArchiveList" xml:space="preserve">
    <value>Архив заметок</value>
  </data>
  <data name="NoteRestore" xml:space="preserve">
    <value>Восстановить</value>
  </data>
  <data name="NoteFontFamilyAndSize" xml:space="preserve">
    <value>Семейство и размер шрифтов для заметок</value>
  </data>
  <data name="NoteTransparencyForInactiveNotes" xml:space="preserve">
    <value>Прозрачность неактивных заметок</value>
  </data>
  <data name="UniversalWindowSettingsUniversalWindowIsOff" xml:space="preserve">
    <value>SmartClick отключен</value>
  </data>
  <data name="DiaryIsOff" xml:space="preserve">
    <value>Дневник отключен</value>
  </data>
  <data name="AutochangeIsOff" xml:space="preserve">
    <value>Шаблоны отключены</value>
  </data>
  <data name="GeneralSettingsCanClose" xml:space="preserve">
    <value>Показывать кнопку закрыть</value>
  </data>
  <data name="SwitcherPauseTimeForKeysSend" xml:space="preserve">
    <value>Таймаут для эмуляции нажатия клавиш</value>
  </data>
  <data name="OcrImage" xml:space="preserve">
    <value>Изображение</value>
  </data>
  <data name="OcrRecognizedText" xml:space="preserve">
    <value>Распознанный текст</value>
  </data>
  <data name="UpdateError" xml:space="preserve">
    <value>Обновление не было произведено, пожалуйста обновитесь вручную.</value>
  </data>
  <data name="UpdateErrorTitle" xml:space="preserve">
    <value>Ошибка обновления EveryLang</value>
  </data>
  <data name="ConverterSettingsConvertDependsOnKeyboardLayout" xml:space="preserve">
    <value>Преобразование в зависимости от текущей раскладки клавиатуры</value>
  </data>
  <data name="NoteCheckMarket" xml:space="preserve">
    <value>Отмеченные</value>
  </data>
  <data name="NotesIsShowing" xml:space="preserve">
    <value>Включены</value>
  </data>
  <data name="SearchHelperText" xml:space="preserve">
    <value>Введите текст для поиска</value>
  </data>
  <data name="TransLangAuto" xml:space="preserve">
    <value>Авто</value>
  </data>
  <data name="NotesFromArchive" xml:space="preserve">
    <value>Из архива</value>
  </data>
  <data name="NotesIsHiding" xml:space="preserve">
    <value>Отключены</value>
  </data>
  <data name="NoteInArchive" xml:space="preserve">
    <value>В архиве</value>
  </data>
  <data name="KeyboardLayoutTab" xml:space="preserve">
    <value>Раскладка клавиатуры</value>
  </data>
  <data name="CapsTab" xml:space="preserve">
    <value>Регистр текста</value>
  </data>
  <data name="IsIndicateNumLockState" xml:space="preserve">
    <value>Показывать состояние NumLock</value>
  </data>
  <data name="StatusButtonNumLockIsOff" xml:space="preserve">
    <value>NumLock выключен</value>
  </data>
  <data name="StatusButtonNumLockIsOn" xml:space="preserve">
    <value>NumLock включен</value>
  </data>
  <data name="ConverterSettingsOpenWindow" xml:space="preserve">
    <value>Открыть окно с функциями конвертера</value>
  </data>
  <data name="UniFirstLetterUp" xml:space="preserve">
    <value>Привести к верхнему регистру первую букву</value>
  </data>
  <data name="UniTextFirstLetterUp" xml:space="preserve">
    <value>Первая вверх</value>
  </data>
  <data name="UniFirstLetterDown" xml:space="preserve">
    <value>Привести к нижнему регистру первую букву</value>
  </data>
  <data name="UniTextFirstLetterDown" xml:space="preserve">
    <value>Первая вниз</value>
  </data>
  <data name="ConverterSettingsKeyboardShortcutsCapsOpenWindow" xml:space="preserve">
    <value>Открыть окно с функциями изменения регистра</value>
  </data>
  <data name="ConverterSettingsSnakeCase" xml:space="preserve">
    <value>Преобразование текста в snake_case стиле</value>
  </data>
  <data name="ConverterSettingsKebabCase" xml:space="preserve">
    <value>Преобразование текста в kebab-case стиле</value>
  </data>
  <data name="ConverterSettingsPascalCase" xml:space="preserve">
    <value>Преобразование текста в PascalCase стиле</value>
  </data>
  <data name="UniCase" xml:space="preserve">
    <value>Регистр текста</value>
  </data>
  <data name="AboutSettingsUpdatePressForUpdate" xml:space="preserve">
    <value>Нажмите для скачивания</value>
  </data>
</root>